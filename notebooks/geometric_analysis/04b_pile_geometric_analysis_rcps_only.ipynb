{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Pile Geometric Analysis - RCPS Site\n", "\n", "Comprehensive geometric analysis of pile installations using ML detection results.\n", "\n", "## Analysis Components\n", "- Pile spacing analysis (tracker-aware)\n", "- Pile height measurement (adaptive multi-ring approach)\n", "- Pile verticality assessment\n", "- Basic alignment analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "markdown", "id": "config_header", "metadata": {}, "source": ["## Configuration"]}, {"cell_type": "code", "execution_count": 5, "id": "parameters", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration: Althea RPCS Site | Max piles: 400 | Sampling: 20%\n"]}], "source": ["# RCPS Site Configuration (Althea RPCS)\n", "SITE_NAME = 'althea_rpcs'\n", "SITE_DISPLAY_NAME = 'Althea RPCS Site'\n", "\n", "# ML Results - try cross-site raw methods first, fallback to backup structure\n", "ML_RESULTS_PATTERNS = [\n", "    '../modeling/pile_detection/02_cross_site_raw_methods/inference/output_runs/pointnet_plus_plus_inference/althea_rpcs/althea_rpcs_pile_detections_*.csv',\n", "    '../modeling/pile_detection/02_cross_site_raw_methods/inference/output_runs/dgcnn_inference/althea_rpcs/dgcnn_althea_rpcs_detections.csv',\n", "    '../modeling/pile_detection/00_backup_original_structure/02_ml_based/classic/output_runs/true_generalization/rcps_generalization_results_*.csv'\n", "]\n", "\n", "POINT_CLOUD_PATH = '../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las'\n", "SITE_CRS = 'EPSG:32614'  # UTM Zone 14N\n", "EXPECTED_PILES = 1359\n", "\n", "OUTPUT_DIR = \"output_runs/geometric_analysis_althea_rpcs\"\n", "\n", "# Analysis Parameters\n", "MAX_ANALYSIS_PILES = 400\n", "CONFIDENCE_THRESHOLD = 0.6\n", "SAMPLE_POINT_CLOUD = True\n", "POINT_CLOUD_SAMPLE_RATIO = 0.2\n", "\n", "# Measurement Parameters\n", "PILE_EXTRACTION_RADIUS = 2.0\n", "MIN_PILE_POINTS = 8\n", "HEIGHT_ANALYSIS_RADIUS = 1.5\n", "VERTICALITY_ANALYSIS_RADIUS = 0.5\n", "\n", "# Spacing Parameters\n", "MAX_NEIGHBOR_DISTANCE = 12.0\n", "WITHIN_TRACKER_RANGE = [2.0, 6.0]\n", "BETWEEN_TRACKER_RANGE = [6.0, 12.0]\n", "\n", "print(f\"Configuration: {SITE_DISPLAY_NAME} | Max piles: {MAX_ANALYSIS_PILES} | Sampling: {POINT_CLOUD_SAMPLE_RATIO*100:.0f}%\")"]}, {"cell_type": "markdown", "id": "imports_header", "metadata": {}, "source": ["## Imports and Setup"]}, {"cell_type": "code", "execution_count": 6, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import glob\n", "from pathlib import Path\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from scipy.spatial import cKDTree\n", "from scipy.stats import describe\n", "from sklearn.decomposition import PCA\n", "from sklearn.cluster import DBSCAN\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "id": "functions_header", "metadata": {}, "source": ["## Analysis Functions"]}, {"cell_type": "code", "execution_count": 7, "id": "core_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analysis functions defined\n"]}], "source": ["def build_spatial_index(points):\n", "    \"\"\"Build KD-tree for fast spatial queries\"\"\"\n", "    return cKDTree(points[:, :2])\n", "\n", "def extract_pile_z_coordinates(pile_locations_2d, points, spatial_index, search_radius=2.0):\n", "    \"\"\"Extract Z coordinates for pile locations from point cloud\"\"\"\n", "    pile_z_coords = []\n", "    \n", "    for pile_xy in pile_locations_2d:\n", "        indices = spatial_index.query_ball_point(pile_xy, search_radius)\n", "        \n", "        if len(indices) > 0:\n", "            nearby_points = points[indices]\n", "            pile_z = np.percentile(nearby_points[:, 2], 95)\n", "        else:\n", "            pile_z = np.median(points[:, 2])\n", "        \n", "        pile_z_coords.append(pile_z)\n", "    \n", "    return np.array(pile_z_coords)\n", "\n", "def calculate_pile_height_adaptive(points, spatial_index, pile_center, search_radius=3.0, min_points=15):\n", "    \"\"\"Improved adaptive donut approach with multiple rings and slope compensation\"\"\"\n", "    # Get all points within search radius\n", "    indices = spatial_index.query_ball_point(pile_center, search_radius)\n", "    if len(indices) < min_points:\n", "        return np.nan\n", "    \n", "    region_points = points[indices]\n", "    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + \n", "                       (region_points[:, 1] - pile_center[1])**2)\n", "    max_distance = np.max(distances)\n", "    \n", "    # Find highest point as pile top\n", "    highest_idx = np.argmax(region_points[:, 2])\n", "    pile_top = region_points[highest_idx, 2]\n", "    \n", "    # Define multiple ring zones for ground estimation\n", "    ring_zones = [(70, 80), (80, 90), (90, 95)]  # Percentages of max_distance\n", "    ground_levels = []\n", "    weights = []\n", "    \n", "    for inner_pct, outer_pct in ring_zones:\n", "        inner_radius = (inner_pct / 100) * max_distance\n", "        outer_radius = (outer_pct / 100) * max_distance\n", "        \n", "        ring_mask = (distances >= inner_radius) & (distances <= outer_radius)\n", "        ring_points = region_points[ring_mask]\n", "        \n", "        if len(ring_points) >= min_points:\n", "            # Remove outliers using IQR\n", "            z_values = ring_points[:, 2]\n", "            Q1, Q3 = np.percentile(z_values, [25, 75])\n", "            IQR = Q3 - Q1\n", "            valid_mask = (z_values >= Q1 - 1.5 * IQR) & (z_values <= Q3 + 1.5 * IQR)\n", "            filtered_z = z_values[valid_mask]\n", "            \n", "            if len(filtered_z) >= min_points:\n", "                ring_mean = np.mean(filtered_z)\n", "                ring_std = np.std(filtered_z)\n", "                weight = len(filtered_z) / (ring_std + 1e-6)  # Weight by point count and consistency\n", "                \n", "                ground_levels.append(ring_mean)\n", "                weights.append(weight)\n", "    \n", "    if not ground_levels:\n", "        return np.nan\n", "    \n", "    # Calculate weighted average ground level\n", "    weights = np.array(weights)\n", "    ground_levels = np.array(ground_levels)\n", "    ground_level = np.average(ground_levels, weights=weights)\n", "    \n", "    height = pile_top - ground_level\n", "    return max(0, height)\n", "\n", "def calculate_pile_verticality_simple(points, spatial_index, pile_center, radius=1.0):\n", "    \"\"\"Simple verticality calculation using top-bottom displacement\"\"\"\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    if len(indices) < 5:\n", "        return np.nan\n", "    \n", "    pile_points = points[indices]\n", "    z_coords = pile_points[:, 2]\n", "    z_range = np.max(z_coords) - np.min(z_coords)\n", "    \n", "    if z_range < 0.5:\n", "        return np.nan\n", "    \n", "    z_threshold = np.min(z_coords) + 0.3 * z_range\n", "    high_points = pile_points[z_coords > z_threshold]\n", "    \n", "    if len(high_points) < 3:\n", "        return np.nan\n", "    \n", "    top_center = np.mean(high_points[:, :2], axis=0)\n", "    horizontal_displacement = np.linalg.norm(top_center - pile_center)\n", "    vertical_height = z_range\n", "    \n", "    if vertical_height < 0.1:\n", "        return np.nan\n", "    \n", "    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))\n", "    return lean_angle\n", "\n", "print(\"Analysis functions defined\")"]}, {"cell_type": "markdown", "id": "data_loading_header", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 8, "id": "load_ml_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading ML detection results...\n", "Loading: dgcnn_althea_rpcs_detections.csv\n", "Loaded 686 pile detections\n"]}, {"ename": "KeyError", "evalue": "'confidence'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/pandas/core/indexes/base.py:3812\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3811\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m3812\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_engine\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcasted_key\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   3813\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m err:\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/index.pyx:167\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/index.pyx:196\u001b[39m, in \u001b[36mpandas._libs.index.IndexEngine.get_loc\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7088\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32mpandas/_libs/hashtable_class_helper.pxi:7096\u001b[39m, in \u001b[36mpandas._libs.hashtable.PyObjectHashTable.get_item\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'confidence'", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[8]\u001b[39m\u001b[32m, line 14\u001b[39m\n\u001b[32m     11\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mLoaded \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(ml_results)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m pile detections\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Filter by confidence\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m high_conf_piles = ml_results[\u001b[43mml_results\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mconfidence\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m >= CONFIDENCE_THRESHOLD].copy()\n\u001b[32m     15\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mHigh confidence piles (>=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mCONFIDENCE_THRESHOLD\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m): \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(high_conf_piles)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     17\u001b[39m \u001b[38;5;66;03m# Sample for analysis\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/pandas/core/frame.py:4107\u001b[39m, in \u001b[36mDataFrame.__getitem__\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   4105\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.columns.nlevels > \u001b[32m1\u001b[39m:\n\u001b[32m   4106\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._getitem_multilevel(key)\n\u001b[32m-> \u001b[39m\u001b[32m4107\u001b[39m indexer = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget_loc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   4108\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m is_integer(indexer):\n\u001b[32m   4109\u001b[39m     indexer = [indexer]\n", "\u001b[36mFile \u001b[39m\u001b[32m~/.local/lib/python3.11/site-packages/pandas/core/indexes/base.py:3819\u001b[39m, in \u001b[36mIndex.get_loc\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m   3814\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(casted_key, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[32m   3815\u001b[39m         \u001b[38;5;28misinstance\u001b[39m(casted_key, abc.Iterable)\n\u001b[32m   3816\u001b[39m         \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28many\u001b[39m(\u001b[38;5;28misinstance\u001b[39m(x, \u001b[38;5;28mslice\u001b[39m) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m casted_key)\n\u001b[32m   3817\u001b[39m     ):\n\u001b[32m   3818\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m InvalidIndexError(key)\n\u001b[32m-> \u001b[39m\u001b[32m3819\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01merr\u001b[39;00m\n\u001b[32m   3820\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m:\n\u001b[32m   3821\u001b[39m     \u001b[38;5;66;03m# If we have a listlike key, _check_indexing_error will raise\u001b[39;00m\n\u001b[32m   3822\u001b[39m     \u001b[38;5;66;03m#  InvalidIndexError. Otherwise we fall through and re-raise\u001b[39;00m\n\u001b[32m   3823\u001b[39m     \u001b[38;5;66;03m#  the TypeError.\u001b[39;00m\n\u001b[32m   3824\u001b[39m     \u001b[38;5;28mself\u001b[39m._check_indexing_error(key)\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'confidence'"]}], "source": ["print(\"Loading ML detection results...\")\n", "\n", "files = glob.glob(ML_RESULTS_PATTERNS[1])\n", "if not files:\n", "    raise FileNotFoundError(f\"No ML results found: {ML_RESULTS_PATTERNS}\")\n", "\n", "latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)\n", "print(f\"Loading: {Path(latest_file).name}\")\n", "\n", "ml_results = pd.read_csv(latest_file)\n", "print(f\"Loaded {len(ml_results)} pile detections\")\n", "\n", "# Filter by confidence\n", "high_conf_piles = ml_results[ml_results['confidence'] >= CONFIDENCE_THRESHOLD].copy()\n", "print(f\"High confidence piles (>={CONFIDENCE_THRESHOLD}): {len(high_conf_piles)}\")\n", "\n", "# Sample for analysis\n", "if len(high_conf_piles) > MAX_ANALYSIS_PILES:\n", "    sampled_piles = high_conf_piles.sample(n=MAX_ANALYSIS_PILES, random_state=42)\n", "    print(f\"Sampled {MAX_ANALYSIS_PILES} piles for analysis\")\n", "else:\n", "    sampled_piles = high_conf_piles\n", "    print(f\"Using all {len(sampled_piles)} high confidence piles\")\n", "\n", "# Extract 2D coordinates\n", "pile_locations_2d = sampled_piles[['utm_x', 'utm_y']].values\n", "print(f\"Extracted 2D coordinates for {len(pile_locations_2d)} piles\")"]}, {"cell_type": "code", "execution_count": null, "id": "load_point_cloud", "metadata": {}, "outputs": [], "source": ["print(\"Loading point cloud data...\")\n", "\n", "las_file = laspy.read(POINT_CLOUD_PATH)\n", "points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "\n", "if SAMPLE_POINT_CLOUD:\n", "    n_sample = int(len(points) * POINT_CLOUD_SAMPLE_RATIO)\n", "    sample_indices = np.random.choice(len(points), n_sample, replace=False)\n", "    points = points[sample_indices]\n", "    print(f\"Sampled {len(points):,} points ({POINT_CLOUD_SAMPLE_RATIO*100:.0f}%)\")\n", "else:\n", "    print(f\"Using full point cloud: {len(points):,} points\")\n", "\n", "# Build spatial index and extract Z coordinates\n", "spatial_index = build_spatial_index(points)\n", "pile_z_coords = extract_pile_z_coordinates(pile_locations_2d, points, spatial_index)\n", "pile_locations = np.column_stack([pile_locations_2d, pile_z_coords])\n", "print(f\"Extracted Z coordinates for {len(pile_locations)} piles\")"]}, {"cell_type": "markdown", "id": "spacing_header", "metadata": {}, "source": ["## Pile Spacing Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "spacing_analysis", "metadata": {}, "outputs": [], "source": ["print(\"Analyzing pile spacing patterns with directional awareness...\")\n", "\n", "def analyze_directional_spacing(pile_locations, max_distance=12.0):\n", "    \"\"\"Enhanced spacing analysis that distinguishes intra-tracker vs inter-tracker spacing\"\"\"\n", "    from scipy.spatial.distance import pdist, squareform\n", "    \n", "    # Calculate all pairwise distances and angles\n", "    n_piles = len(pile_locations)\n", "    intra_tracker_distances = []\n", "    inter_tracker_distances = []\n", "    all_distances = []\n", "    \n", "    for i in range(n_piles):\n", "        for j in range(i + 1, n_piles):\n", "            pile1 = pile_locations[i]\n", "            pile2 = pile_locations[j]\n", "            \n", "            # Calculate distance\n", "            dx = pile2[0] - pile1[0]\n", "            dy = pile2[1] - pile1[1]\n", "            distance = np.sqrt(dx**2 + dy**2)\n", "            \n", "            if distance <= max_distance:\n", "                all_distances.append(distance)\n", "                \n", "                # Calculate angle (0° = East, 90° = North)\n", "                angle = np.degrees(np.arctan2(dy, dx))\n", "                angle = (angle + 360) % 360  # Normalize to 0-360°\n", "                \n", "                # Determine if this is horizontal (intra-tracker) or vertical (inter-tracker)\n", "                # Horizontal: angles near 0°, 180° (East-West direction)\n", "                # Vertical: angles near 90°, 270° (North-South direction)\n", "                \n", "                # Check if angle is more horizontal (±30° from E-W axis)\n", "                is_horizontal = (angle <= 30) or (angle >= 330) or (150 <= angle <= 210)\n", "                \n", "                if is_horizontal:\n", "                    intra_tracker_distances.append(distance)\n", "                else:\n", "                    inter_tracker_distances.append(distance)\n", "    \n", "    return {\n", "        'all_distances': np.array(all_distances),\n", "        'intra_tracker': np.array(intra_tracker_distances),\n", "        'inter_tracker': np.array(inter_tracker_distances)\n", "    }\n", "\n", "# Perform enhanced spacing analysis\n", "spacing_results = analyze_directional_spacing(pile_locations, MAX_NEIGHBOR_DISTANCE)\n", "\n", "all_distances = spacing_results['all_distances']\n", "intra_tracker = spacing_results['intra_tracker']\n", "inter_tracker = spacing_results['inter_tracker']\n", "\n", "if len(all_distances) > 0:\n", "    print(f\"Total distance pairs analyzed: {len(all_distances)}\")\n", "    print(f\"Overall spacing: mean={np.mean(all_distances):.2f}m, std={np.std(all_distances):.2f}m\")\n", "    print(f\"Range: {np.min(all_distances):.2f}m - {np.max(all_distances):.2f}m\")\n", "    \n", "    if len(intra_tracker) > 0:\n", "        print(f\"\\nIntra-tracker (horizontal) spacing:\")\n", "        print(f\"  Count: {len(intra_tracker)} pairs\")\n", "        print(f\"  Mean: {np.mean(intra_tracker):.2f}m ± {np.std(intra_tracker):.2f}m\")\n", "        print(f\"  Range: {np.min(intra_tracker):.2f}m - {np.max(intra_tracker):.2f}m\")\n", "    else:\n", "        print(f\"\\nNo intra-tracker spacing detected\")\n", "    \n", "    if len(inter_tracker) > 0:\n", "        print(f\"\\nInter-tracker (vertical) spacing:\")\n", "        print(f\"  Count: {len(inter_tracker)} pairs\")\n", "        print(f\"  Mean: {np.mean(inter_tracker):.2f}m ± {np.std(inter_tracker):.2f}m\")\n", "        print(f\"  Range: {np.min(inter_tracker):.2f}m - {np.max(inter_tracker):.2f}m\")\n", "    else:\n", "        print(f\"\\nNo inter-tracker spacing detected\")\n", "    \n", "    spacing_stats = {\n", "        'total_pairs': len(all_distances),\n", "        'overall_mean': np.mean(all_distances),\n", "        'overall_std': np.std(all_distances),\n", "        'intra_tracker_count': len(intra_tracker),\n", "        'intra_tracker_mean': np.mean(intra_tracker) if len(intra_tracker) > 0 else np.nan,\n", "        'intra_tracker_std': np.std(intra_tracker) if len(intra_tracker) > 0 else np.nan,\n", "        'inter_tracker_count': len(inter_tracker),\n", "        'inter_tracker_mean': np.mean(inter_tracker) if len(inter_tracker) > 0 else np.nan,\n", "        'inter_tracker_std': np.std(inter_tracker) if len(inter_tracker) > 0 else np.nan\n", "    }\n", "else:\n", "    print(\"No distance pairs calculated\")\n", "    spacing_stats = {}"]}, {"cell_type": "markdown", "id": "height_header", "metadata": {}, "source": ["## Pile Height Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "height_analysis", "metadata": {}, "outputs": [], "source": ["print(\"Analyzing pile heights using adaptive multi-ring approach...\")\n", "\n", "pile_heights = []\n", "for pile_center in pile_locations:\n", "    height = calculate_pile_height_adaptive(\n", "        points, spatial_index, pile_center[:2], \n", "        search_radius=3.0, min_points=15\n", "    )\n", "    pile_heights.append(height)\n", "\n", "pile_heights = np.array(pile_heights)\n", "valid_heights = pile_heights[~np.isnan(pile_heights)]\n", "\n", "if len(valid_heights) > 0:\n", "    print(f\"Valid height measurements: {len(valid_heights)}/{len(pile_locations)}\")\n", "    print(f\"Height statistics: mean={np.mean(valid_heights):.2f}m, std={np.std(valid_heights):.2f}m\")\n", "    print(f\"Height range: {np.min(valid_heights):.2f}m - {np.max(valid_heights):.2f}m\")\n", "    \n", "    height_stats = {\n", "        'valid_count': len(valid_heights),\n", "        'total_count': len(pile_locations),\n", "        'mean_height': np.mean(valid_heights),\n", "        'std_height': np.std(valid_heights),\n", "        'min_height': np.min(valid_heights),\n", "        'max_height': np.max(valid_heights)\n", "    }\n", "else:\n", "    print(\"No valid height measurements\")\n", "    height_stats = {}"]}, {"cell_type": "markdown", "id": "verticality_header", "metadata": {}, "source": ["## Pile Verticality Analysis"]}, {"cell_type": "code", "execution_count": null, "id": "verticality_analysis", "metadata": {}, "outputs": [], "source": ["print(\"Analyzing pile verticality...\")\n", "\n", "pile_lean_angles = []\n", "for pile_center in pile_locations:\n", "    lean_angle = calculate_pile_verticality_simple(\n", "        points, spatial_index, pile_center[:2], \n", "        VERTICALITY_ANALYSIS_RADIUS\n", "    )\n", "    pile_lean_angles.append(lean_angle)\n", "\n", "pile_lean_angles = np.array(pile_lean_angles)\n", "valid_lean_angles = pile_lean_angles[~np.isnan(pile_lean_angles)]\n", "\n", "if len(valid_lean_angles) > 0:\n", "    print(f\"Valid verticality measurements: {len(valid_lean_angles)}/{len(pile_locations)}\")\n", "    print(f\"Lean angle statistics: mean={np.mean(valid_lean_angles):.2f}°, std={np.std(valid_lean_angles):.2f}°\")\n", "    print(f\"Lean angle range: {np.min(valid_lean_angles):.2f}° - {np.max(valid_lean_angles):.2f}°\")\n", "    \n", "    excellent_vertical = np.sum(valid_lean_angles <= 2.0)\n", "    good_vertical = np.sum((valid_lean_angles > 2.0) & (valid_lean_angles <= 5.0))\n", "    poor_vertical = np.sum(valid_lean_angles > 5.0)\n", "    \n", "    print(f\"Verticality quality: Excellent (≤2°): {excellent_vertical}, Good (2-5°): {good_vertical}, Poor (>5°): {poor_vertical}\")\n", "    \n", "    verticality_stats = {\n", "        'valid_count': len(valid_lean_angles),\n", "        'total_count': len(pile_locations),\n", "        'mean_lean': np.mean(valid_lean_angles),\n", "        'std_lean': np.std(valid_lean_angles),\n", "        'excellent_count': excellent_vertical,\n", "        'good_count': good_vertical,\n", "        'poor_count': poor_vertical\n", "    }\n", "else:\n", "    print(\"No valid verticality measurements\")\n", "    verticality_stats = {}"]}, {"cell_type": "markdown", "id": "export_header", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": null, "id": "export_results", "metadata": {}, "outputs": [], "source": ["print(\"Exporting analysis results...\")\n", "\n", "# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Create detailed results DataFrame\n", "pile_df = pd.DataFrame({\n", "    'pile_id': sampled_piles['pile_id'].values,\n", "    'utm_x': pile_locations[:, 0],\n", "    'utm_y': pile_locations[:, 1],\n", "    'z_extracted': pile_locations[:, 2],\n", "    'confidence': sampled_piles['confidence'].values,\n", "    'pile_height': pile_heights,\n", "    'lean_angle': pile_lean_angles\n", "})\n", "\n", "# Add geometry for GIS\n", "geometry = [Point(x, y) for x, y in zip(pile_df['utm_x'], pile_df['utm_y'])]\n", "gdf = gpd.GeoDataFrame(pile_df, geometry=geometry, crs=SITE_CRS)\n", "\n", "# Export to CSV and GeoJSON\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "csv_filename = f\"rcps_geometric_analysis_{timestamp}.csv\"\n", "geojson_filename = f\"rcps_geometric_analysis_{timestamp}.geojson\"\n", "\n", "gdf.to_csv(output_dir / csv_filename, index=False)\n", "gdf.to_file(output_dir / geojson_filename, driver='GeoJSON')\n", "\n", "# Create summary\n", "summary = {\n", "    'site_name': SITE_NAME,\n", "    'analysis_timestamp': timestamp,\n", "    'total_piles_analyzed': len(pile_locations),\n", "    'spacing_stats': spacing_stats,\n", "    'height_stats': height_stats,\n", "    'verticality_stats': verticality_stats\n", "}\n", "\n", "summary_filename = f\"rcps_analysis_summary_{timestamp}.json\"\n", "with open(output_dir / summary_filename, 'w') as f:\n", "    json.dump(summary, f, indent=2, default=str)\n", "\n", "print(f\"Results saved: {csv_filename}\")\n", "print(f\"<PERSON><PERSON> analyzed: {len(pile_df)} | Valid heights: {pile_df['pile_height'].notna().sum()} | Valid verticality: {pile_df['lean_angle'].notna().sum()}\")\n", "print(f\"Summary saved: {summary_filename}\")\n", "print(f\"\\nRCPS SITE ANALYSIS COMPLETE\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Load CSV in QGIS for visualization\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}