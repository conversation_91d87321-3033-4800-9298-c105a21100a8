{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Pile Geometric Analysis - RES Site\n", "\n", "Comprehensive geometric analysis of pile installations using ML detection results.\n", "\n", "## Analysis Components\n", "- Pile spacing analysis (tracker-aware)\n", "- Pile height measurement (adaptive multi-ring approach)\n", "- Pile verticality assessment\n", "- Basic alignment analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "markdown", "id": "config_header", "metadata": {}, "source": ["## Configuration"]}, {"cell_type": "code", "execution_count": 58, "id": "parameters", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration: RES Site | Max piles: 300 | Sampling: 30%\n"]}], "source": ["# RES Site Configuration\n", "SITE_NAME = 'nortan_res'\n", "SITE_DISPLAY_NAME = 'RES Site'\n", "\n", "# ML Results - try cross-site raw methods first, fallback to backup structure\n", "ML_RESULTS_PATTERNS = [\n", "    # \"../../notebooks/modeling/pile_detection/02_cross_site_raw_methods/inference/output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250728_202947.csv\"\n", "    \"../../notebooks/modeling/pile_detection/02_classical_ml/output_runs/clean_validation/nortan_res_ml_detected_piles_20250815_101635.csv\"\n", "]\n", "\n", "POINT_CLOUD_PATH = '../../data/raw/nortan_res/pointcloud/Block_11_2m.las'\n", "SITE_CRS = 'EPSG:32614'  # UTM Zone 14N\n", "EXPECTED_PILES = 368\n", "\n", "OUTPUT_DIR = \"output_runs/geometric_analysis_res\"\n", "\n", "# Analysis Parameters\n", "MAX_ANALYSIS_PILES = 300\n", "CONFIDENCE_THRESHOLD = 0.6\n", "SAMPLE_POINT_CLOUD = True\n", "POINT_CLOUD_SAMPLE_RATIO = 0.3\n", "\n", "# Measurement Parameters\n", "PILE_EXTRACTION_RADIUS = 2.0\n", "MIN_PILE_POINTS = 8\n", "HEIGHT_ANALYSIS_RADIUS = 1.5\n", "VERTICALITY_ANALYSIS_RADIUS = 0.5\n", "\n", "# Donut Parameters\n", "DONUT_INNER_RADIUS = 0.8\n", "DONUT_OUTER_RADIUS = 2.5\n", "\n", "# Spacing Parameters\n", "MAX_NEIGHBOR_DISTANCE = 12.0\n", "WITHIN_TRACKER_RANGE = [2.0, 6.0]\n", "BETWEEN_TRACKER_RANGE = [6.0, 12.0]\n", "\n", "print(f\"Configuration: {SITE_DISPLAY_NAME} | Max piles: {MAX_ANALYSIS_PILES} | Sampling: {POINT_CLOUD_SAMPLE_RATIO*100:.0f}%\")"]}, {"cell_type": "markdown", "id": "imports_header", "metadata": {}, "source": ["## Imports and Setup"]}, {"cell_type": "code", "execution_count": 59, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import glob\n", "from pathlib import Path\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "from datetime import datetime\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "from scipy.spatial import cKDTree\n", "from scipy.stats import describe\n", "from sklearn.decomposition import PCA\n", "from sklearn.cluster import DBSCAN\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "markdown", "id": "functions_header", "metadata": {}, "source": ["## Analysis Functions"]}, {"cell_type": "code", "execution_count": 60, "id": "core_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analysis functions defined\n"]}], "source": ["def build_spatial_index(points):\n", "    \"\"\"Build KD-tree for fast spatial queries\"\"\"\n", "    return cKDTree(points[:, :2])\n", "\n", "def extract_pile_z_coordinates(pile_locations_2d, points, spatial_index, search_radius=2.0):\n", "    \"\"\"Extract Z coordinates for pile locations from point cloud\"\"\"\n", "    pile_z_coords = []\n", "    \n", "    for pile_xy in pile_locations_2d:\n", "        indices = spatial_index.query_ball_point(pile_xy, search_radius)\n", "        \n", "        if len(indices) > 0:\n", "            nearby_points = points[indices]\n", "            pile_z = np.percentile(nearby_points[:, 2], 95)\n", "        else:\n", "            pile_z = np.median(points[:, 2])\n", "        \n", "        pile_z_coords.append(pile_z)\n", "    \n", "    return np.array(pile_z_coords)\n", "\n", "def calculate_pile_height_adaptive(points, spatial_index, pile_center, search_radius=3.0, min_points=15):\n", "    \"\"\"Improved adaptive donut approach with multiple rings and slope compensation\"\"\"\n", "    # Get all points within search radius\n", "    indices = spatial_index.query_ball_point(pile_center, search_radius)\n", "    if len(indices) < min_points:\n", "        return np.nan\n", "    \n", "    region_points = points[indices]\n", "    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + \n", "                       (region_points[:, 1] - pile_center[1])**2)\n", "    max_distance = np.max(distances)\n", "    \n", "    # Find highest point as pile top\n", "    highest_idx = np.argmax(region_points[:, 2])\n", "    pile_top = region_points[highest_idx, 2]\n", "    \n", "    # Define multiple ring zones for ground estimation\n", "    ring_zones = [(70, 80), (80, 90), (90, 95)]  # Percentages of max_distance\n", "    ground_levels = []\n", "    weights = []\n", "    \n", "    for inner_pct, outer_pct in ring_zones:\n", "        inner_radius = (inner_pct / 100) * max_distance\n", "        outer_radius = (outer_pct / 100) * max_distance\n", "        \n", "        ring_mask = (distances >= inner_radius) & (distances <= outer_radius)\n", "        ring_points = region_points[ring_mask]\n", "        \n", "        if len(ring_points) >= min_points:\n", "            # Remove outliers using IQR\n", "            z_values = ring_points[:, 2]\n", "            Q1, Q3 = np.percentile(z_values, [25, 75])\n", "            IQR = Q3 - Q1\n", "            valid_mask = (z_values >= Q1 - 1.5 * IQR) & (z_values <= Q3 + 1.5 * IQR)\n", "            filtered_z = z_values[valid_mask]\n", "            \n", "            if len(filtered_z) >= min_points:\n", "                ring_mean = np.mean(filtered_z)\n", "                ring_std = np.std(filtered_z)\n", "                weight = len(filtered_z) / (ring_std + 1e-6)  # Weight by point count and consistency\n", "                \n", "                ground_levels.append(ring_mean)\n", "                weights.append(weight)\n", "    \n", "    if not ground_levels:\n", "        return np.nan\n", "    \n", "    # Calculate weighted average ground level\n", "    weights = np.array(weights)\n", "    ground_levels = np.array(ground_levels)\n", "    ground_level = np.average(ground_levels, weights=weights)\n", "    \n", "    height = pile_top - ground_level\n", "    return max(0, height)\n", "\n", "def calculate_pile_verticality_simple(points, spatial_index, pile_center, radius=1.0):\n", "    \"\"\"Simple verticality calculation using top-bottom displacement\"\"\"\n", "    indices = spatial_index.query_ball_point(pile_center, radius)\n", "    if len(indices) < 5:\n", "        return np.nan\n", "    \n", "    pile_points = points[indices]\n", "    z_coords = pile_points[:, 2]\n", "    z_range = np.max(z_coords) - np.min(z_coords)\n", "    \n", "    if z_range < 0.5:\n", "        return np.nan\n", "    \n", "    z_threshold = np.min(z_coords) + 0.3 * z_range\n", "    high_points = pile_points[z_coords > z_threshold]\n", "    \n", "    if len(high_points) < 3:\n", "        return np.nan\n", "    \n", "    top_center = np.mean(high_points[:, :2], axis=0)\n", "    horizontal_displacement = np.linalg.norm(top_center - pile_center)\n", "    vertical_height = z_range\n", "    \n", "    if vertical_height < 0.1:\n", "        return np.nan\n", "    \n", "    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))\n", "    return lean_angle\n", "\n", "print(\"Analysis functions defined\")"]}, {"cell_type": "markdown", "id": "data_loading_header", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 61, "id": "load_ml_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading ML detection results...\n", "Found 1 files using pattern: ../../notebooks/modeling/pile_detection/02_classical_ml/output_runs/clean_validation/nortan_res_ml_detected_piles_20250815_101635.csv\n", "Loading latest: nortan_res_ml_detected_piles_20250815_101635.csv\n", "Loaded 368 pile detections\n", "High confidence piles (>=0.6): 368\n", "Sampled 300 piles for analysis\n", "Extracted 2D coordinates for 300 piles\n"]}], "source": ["# Updated pattern to match your file names\n", "print(\"Loading ML detection results...\")\n", "\n", "files = []\n", "used_pattern = None\n", "for pattern in ML_RESULTS_PATTERNS:\n", "    files = glob.glob(pattern)\n", "    if files:\n", "        used_pattern = pattern\n", "        break\n", "\n", "if not files:\n", "    raise FileNotFoundError(f\"No ML results found in any of: {ML_RESULTS_PATTERNS}\")\n", "\n", "# Load latest file\n", "latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)\n", "print(f\"Found {len(files)} files using pattern: {used_pattern}\")\n", "print(f\"Loading latest: {Path(latest_file).name}\")\n", "\n", "ml_results = pd.read_csv(latest_file)\n", "print(f\"Loaded {len(ml_results)} pile detections\")\n", "\n", "# Standardize column names\n", "if 'pile_probability' in ml_results.columns:\n", "    ml_results = ml_results.rename(columns={'pile_probability': 'confidence'})\n", "if 'x' in ml_results.columns and 'y' in ml_results.columns:\n", "    ml_results = ml_results.rename(columns={'x': 'utm_x', 'y': 'utm_y'})\n", "\n", "# Filter by confidence\n", "high_conf_piles = ml_results[ml_results['confidence'] >= CONFIDENCE_THRESHOLD].copy()\n", "print(f\"High confidence piles (>={CONFIDENCE_THRESHOLD}): {len(high_conf_piles)}\")\n", "\n", "# Sample for analysis\n", "if len(high_conf_piles) > MAX_ANALYSIS_PILES:\n", "    sampled_piles = high_conf_piles.sample(n=MAX_ANALYSIS_PILES, random_state=42)\n", "    print(f\"Sampled {MAX_ANALYSIS_PILES} piles for analysis\")\n", "else:\n", "    sampled_piles = high_conf_piles\n", "    print(f\"Using all {len(sampled_piles)} high confidence piles\")\n", "\n", "# Extract 2D coordinates\n", "pile_locations_2d = sampled_piles[['utm_x', 'utm_y']].values\n", "print(f\"Extracted 2D coordinates for {len(pile_locations_2d)} piles\")\n"]}, {"cell_type": "code", "execution_count": 62, "id": "load_point_cloud", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud data...\n", "Sampled 10,669,605 points (30%)\n", "Extracted Z coordinates for 300 piles\n"]}], "source": ["print(\"Loading point cloud data...\")\n", "\n", "las_file = laspy.read(POINT_CLOUD_PATH)\n", "points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "\n", "if SAMPLE_POINT_CLOUD:\n", "    n_sample = int(len(points) * POINT_CLOUD_SAMPLE_RATIO)\n", "    sample_indices = np.random.choice(len(points), n_sample, replace=False)\n", "    points = points[sample_indices]\n", "    print(f\"Sampled {len(points):,} points ({POINT_CLOUD_SAMPLE_RATIO*100:.0f}%)\")\n", "else:\n", "    print(f\"Using full point cloud: {len(points):,} points\")\n", "\n", "# Build spatial index and extract Z coordinates\n", "spatial_index = build_spatial_index(points)\n", "pile_z_coords = extract_pile_z_coordinates(pile_locations_2d, points, spatial_index)\n", "pile_locations = np.column_stack([pile_locations_2d, pile_z_coords])\n", "print(f\"Extracted Z coordinates for {len(pile_locations)} piles\")"]}, {"cell_type": "markdown", "id": "spacing_header", "metadata": {}, "source": ["## Pile Spacing Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["nortan_res | Pairs=872 | Overall=9.55±1.82 m\n", "Intra (179.5°): 7.15±0.23 m (n=213)\n", "Inter (89.5°): 8.78±0.60 m (n=235)\n"]}], "source": ["from scipy.spatial import cKDTree\n", "import numpy as np, json\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "def analyze_spacing(pile_xy, max_dist=12.0, ang_tol=15.0):\n", "    XY = np.asarray(pile_xy, float)\n", "    T = cKDTree(XY)\n", "    angs, dists = [], []\n", "    for i, p in enumerate(XY):\n", "        for j in T.query_ball_point(p, max_dist):\n", "            if j <= i: continue\n", "            dx, dy = XY[j] - p\n", "            dists.append(float(np.hypot(dx, dy)))\n", "            angs.append((np.degrees(np.arctan2(dy, dx)) + 180) % 180)\n", "    if not dists:\n", "        return None\n", "\n", "    angs = np.array(angs); dists = np.array(dists)\n", "    H, E = np.histogram(angs, bins=180, range=(0,180))\n", "    a0 = 0.5*(E[np.argmax(H)] + E[np.argmax(H)+1])     # primary axis\n", "    a1 = (a0 + 90) % 180                               # orthogonal\n", "\n", "    def ad(a,b): d=abs(a-b); return min(d,180-d)\n", "    intra = dists[[ad(a, a0) <= ang_tol for a in angs]]\n", "    inter = dists[[ad(a, a1) <= ang_tol for a in angs]]\n", "\n", "    # keep intra = smaller mean\n", "    if np.mean(intra) > np.mean(inter):\n", "        intra, inter, a0, a1 = inter, intra, a1, a0\n", "\n", "    return {\n", "        \"pairs\": int(dists.size),\n", "        \"overall_mean\": float(dists.mean()), \"overall_std\": float(dists.std()),\n", "        \"intra_deg\": float(a0), \"intra_mean\": float(np.mean(intra)), \"intra_std\": float(np.std(intra)), \"intra_n\": int(intra.size),\n", "        \"inter_deg\": float(a1), \"inter_mean\": float(np.mean(inter)), \"inter_std\": float(np.std(inter)), \"inter_n\": int(inter.size),\n", "    }\n", "\n", "def print_and_save_spacing(site_name, pile_xy, out_dir=\"output_runs/spacing\"):\n", "    s = analyze_spacing(pile_xy)\n", "    if not s:\n", "        print(f\"{site_name}: no spacing pairs.\")\n", "        return {}\n", "    print(f\"{site_name} | Pairs={s['pairs']} | Overall={s['overall_mean']:.2f}±{s['overall_std']:.2f} m\")\n", "    print(f\"Intra ({s['intra_deg']:.1f}°): {s['intra_mean']:.2f}±{s['intra_std']:.2f} m (n={s['intra_n']})\")\n", "    print(f\"Inter ({s['inter_deg']:.1f}°): {s['inter_mean']:.2f}±{s['inter_std']:.2f} m (n={s['inter_n']})\")\n", "    ts = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    Path(out_dir).mkdir(parents=True, exist_ok=True)\n", "    with open(Path(out_dir)/f\"{site_name}_spacing_{ts}.json\",\"w\") as f:\n", "        json.dump({\"site\": site_name, **s}, f, indent=2)\n", "    return s\n", "\n", "pile_xy = pile_locations[:, :2]  \n", "\n", "spacing_stats =print_and_save_spacing(SITE_NAME, pile_xy)\n"]}, {"cell_type": "markdown", "id": "height_header", "metadata": {}, "source": ["## Pile Height Analysis"]}, {"cell_type": "code", "execution_count": 64, "id": "height_analysis", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing pile heights using adaptive multi-ring approach...\n", "Valid height measurements: 300/300\n", "Height statistics: mean=1.28m, std=0.26m\n", "Height range: 0.13m - 1.80m\n"]}], "source": ["print(\"Analyzing pile heights using adaptive multi-ring approach...\")\n", "\n", "pile_heights = []\n", "for pile_center in pile_locations:\n", "    height = calculate_pile_height_adaptive(\n", "        points, spatial_index, pile_center[:2], \n", "        search_radius=3.0, min_points=15\n", "    )\n", "    pile_heights.append(height)\n", "\n", "pile_heights = np.array(pile_heights)\n", "valid_heights = pile_heights[~np.isnan(pile_heights)]\n", "\n", "if len(valid_heights) > 0:\n", "    print(f\"Valid height measurements: {len(valid_heights)}/{len(pile_locations)}\")\n", "    print(f\"Height statistics: mean={np.mean(valid_heights):.2f}m, std={np.std(valid_heights):.2f}m\")\n", "    print(f\"Height range: {np.min(valid_heights):.2f}m - {np.max(valid_heights):.2f}m\")\n", "    \n", "    height_stats = {\n", "        'valid_count': len(valid_heights),\n", "        'total_count': len(pile_locations),\n", "        'mean_height': np.mean(valid_heights),\n", "        'std_height': np.std(valid_heights),\n", "        'min_height': np.min(valid_heights),\n", "        'max_height': np.max(valid_heights)\n", "    }\n", "else:\n", "    print(\"No valid height measurements\")\n", "    height_stats = {}"]}, {"cell_type": "markdown", "id": "verticality_header", "metadata": {}, "source": ["## Pile Verticality Analysis"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing C-section pile verticality & twist...\n", "Valid=298/300, Mean tilt=3.17°, Std=5.05°\n"]}], "source": ["print(\"Analyzing C-section pile verticality & twist...\")\n", "\n", "# Full-res points & KD-tree\n", "las_full = laspy.read(POINT_CLOUD_PATH)\n", "P = np.vstack([las_full.x, las_full.y, las_full.z]).T\n", "tree = cKDTree(P[:, :2])\n", "\n", "# Ensure pile_id\n", "if 'pile_id' not in sampled_piles.columns:\n", "    sampled_piles = sampled_piles.copy()\n", "    sampled_piles['pile_id'] = np.arange(len(sampled_piles))\n", "\n", "def fit_line_pca(pts):\n", "    X = pts - pts.mean(axis=0, keepdims=True)\n", "    _, _, Vt = np.linalg.svd(X, full_matrices=False)\n", "    v = Vt[0]; v /= (np.linalg.norm(v)+1e-12)\n", "    if v[2] < 0: v = -v\n", "    return v, pts.mean(axis=0)\n", "\n", "def c_section_metrics(center_xy,\n", "                      neigh_radius=1.0,      # small neighborhood\n", "                      gate_cyl=0.35,         # tight radial gate (m)\n", "                      min_pts=25,\n", "                      min_height=0.15,\n", "                      top_keep=0.60,         # keep top 60% for axis estimate\n", "                      slice_top=0.20):       # use top 20% for twist\n", "    # 1) gather\n", "    idx = tree.query_ball_point(center_xy, neigh_radius)\n", "    if not idx: return np.nan, np.nan\n", "    N = P[idx]\n", "    # radial gate (coarse)\n", "    r = np.hypot(N[:,0]-center_xy[0], N[:,1]-center_xy[1])\n", "    C = N[r <= gate_cyl]\n", "    if <PERSON><PERSON>shape[0] < min_pts: return np.nan, np.nan\n", "\n", "    # 2) keep upper band to avoid ground\n", "    z = C[:,2]; zmin, zmax = z.min(), z.max()\n", "    if (zmax - zmin) < min_height: return np.nan, np.nan\n", "    z_lo = zmin + (1.0 - top_keep) * (zmax - zmin)\n", "    U = C[z >= z_lo]\n", "    if U.shape[0] < min_pts//2: return np.nan, np.nan\n", "\n", "    # 3) first axis fit (robust enough for C-section)\n", "    axis, p0 = fit_line_pca(U)\n", "\n", "    # 4) rotate points so axis ≈ Z\n", "    zhat = np.array([0.,0.,1.])\n", "    # rotation matrix from axis->zhat\n", "    a = axis/np.linalg.norm(axis); b = zhat\n", "    v = np.cross(a,b); s = np.linalg.norm(v); c = float(np.dot(a,b))\n", "    if s < 1e-12:\n", "        R = np.eye(3) if c>0 else -np.eye(3)\n", "    else:\n", "        K = np.array([[0,-v[2],v[1]],[v[2],0,-v[0]],[-v[1],v[0],0]])/ (s+1e-12)\n", "        R = np.eye(3) + <PERSON> + <PERSON>@K*((1-c)/(s**2+1e-12))\n", "\n", "    Z = (R @ (C - p0).T).T\n", "    # 5) top slice for twist (thin slab)\n", "    z2 = Z[:,2]; z2min, z2max = z2.min(), z2.max()\n", "    Ztop = Z[z2 >= z2min + (1.0 - slice_top)*(z2max - z2min)]\n", "    if Ztop.shape[0] < min_pts//2: return np.nan, np.nan\n", "\n", "    # 6) 2D PCA on XY of top slice -> major axis direction in cross-section\n", "    XY = Ztop[:, :2] - Ztop[:, :2].mean(axis=0, keepdims=True)\n", "    C2 = np.cov(XY.T)\n", "    eigvals, eigvecs = np.linalg.eigh(C2)\n", "    major = eigvecs[:,1]  # larger variance direction in the section plane\n", "\n", "    # twist = angle of 'major' axis vs global X after axis alignment\n", "    twist = np.degrees(np.arctan2(major[1], major[0])) % 180.0  # 0..180\n", "\n", "    # 7) lean angle from original axis\n", "    horiz = np.hypot(axis[0], axis[1])\n", "    lean = np.degrees(np.arctan2(ho<PERSON>, abs(axis[2])))\n", "\n", "    return float(lean), float(twist)\n", "\n", "# Run\n", "results = [c_section_metrics((row.utm_x, row.utm_y)) for _, row in sampled_piles.iterrows()]\n", "angles = np.array([r[0] for r in results], dtype=float)\n", "twists = np.array([r[1] for r in results], dtype=float)\n", "\n", "valid_mask = ~np.isnan(angles)\n", "print(f\"Valid={valid_mask.sum()}/{len(angles)}, \"\n", "      f\"Mean tilt={np.nanmean(angles):.2f}°, Std={np.nanstd(angles):.2f}°\")\n", "\n", "# Quality & stats\n", "quality = np.where(np.isnan(angles), \"Unreliable\",\n", "           np.where(angles <= 2.0, \"Excellent\",\n", "           np.where(angles <= 5.0, \"Good\", \"Poor\")))\n", "\n", "from collections import Counter\n", "verticality_stats = {\n", "    \"valid_count\": int(np.isfinite(angles).sum()),\n", "    \"total_count\": int(len(angles)),\n", "    \"mean_deg\": float(np.nanmean(angles)),\n", "    \"std_deg\": float(np.nanstd(angles)),\n", "    \"min_deg\": float(np.nanmin(angles)),\n", "    \"max_deg\": float(np.nanmax(angles)),\n", "    \"quality_bins\": dict(Counter(quality)),\n", "    \"twist_mean_deg\": float(np.nanmean(twists)),\n", "    \"twist_std_deg\": float(np.nanstd(twists))\n", "}\n", "\n", "pile_lean_angles = angles  # keep for export\n", "pile_twist_angles = twists\n"]}, {"cell_type": "markdown", "id": "export_header", "metadata": {}, "source": ["## Export Results"]}, {"cell_type": "code", "execution_count": 66, "id": "export_results", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exporting analysis results...\n", "Saved: nortan_res_csection_geometric_20250815_110631.csv | nortan_res_csection_geometric_20250815_110631.geo<PERSON>son\n"]}], "source": ["print(\"Exporting analysis results...\")\n", "\n", "# Setup\n", "output_dir = Path(OUTPUT_DIR); output_dir.mkdir(parents=True, exist_ok=True)\n", "ts = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "\n", "# Per-pile table\n", "pile_df = pd.DataFrame({\n", "    \"pile_id\": sampled_piles[\"pile_id\"].values,\n", "    \"utm_x\": pile_locations[:, 0],\n", "    \"utm_y\": pile_locations[:, 1],\n", "    \"z_extracted\": pile_locations[:, 2],\n", "    \"confidence\": sampled_piles[\"confidence\"].values,\n", "    \"pile_height\": pile_heights,\n", "    \"lean_angle_deg\": pile_lean_angles,      # from C-section fit\n", "    \"twist_angle_deg\": pile_twist_angles     # from C-section top-slice PCA\n", "})\n", "for c in [\"utm_x\",\"utm_y\",\"z_extracted\",\"confidence\",\"pile_height\",\"lean_angle_deg\",\"twist_angle_deg\"]:\n", "    if c in pile_df: pile_df[c] = pile_df[c].astype(float).round(3)\n", "\n", "# Geo export\n", "gdf = gpd.GeoDataFrame(pile_df, geometry=[Point(xy) for xy in zip(pile_df.utm_x, pile_df.utm_y)], crs=SITE_CRS)\n", "csv_name     = f\"{SITE_NAME}_csection_geometric_{ts}.csv\"\n", "geojson_name = f\"{SITE_NAME}_csection_geometric_{ts}.geojson\"\n", "gdf.to_csv(output_dir / csv_name, index=False)\n", "gdf.to_file(output_dir / geojson_name, driver=\"GeoJSON\")\n", "\n", "print(f\"Saved: {csv_name} | {geojson_name}\" )\n"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved: nortan_res_csection_summary_20250815_110631.json\n", "Piles=300 | Heights valid=300 | Tilt valid=298 | Twist valid=298\n", "Output dir: output_runs/geometric_analysis_res\n"]}], "source": ["# Summary\n", "summary = {\n", "    \"site_name\": SITE_NAME,\n", "    \"site_crs\": SITE_CRS,\n", "    \"analysis_timestamp\": ts,\n", "    \"total_piles_analyzed\": int(len(pile_df)),\n", "    \"spacing_stats\": spacing_stats,\n", "    \"height_stats\": height_stats,\n", "    \"verticality_stats\": verticality_stats\n", "}\n", "summary_name = f\"{SITE_NAME}_csection_summary_{ts}.json\"\n", "with open(output_dir / summary_name, \"w\") as f:\n", "    json.dump(summary, f, indent=2)\n", "\n", "# Log\n", "vh = int(np.isfinite(pile_df[\"pile_height\"]).sum())\n", "vt = int(np.isfinite(pile_df[\"lean_angle_deg\"]).sum())\n", "vw = int(np.isfinite(pile_df[\"twist_angle_deg\"]).sum())\n", "print(f\"Saved: {summary_name}\")\n", "print(f\"Piles={len(pile_df)} | Heights valid={vh} | Tilt valid={vt} | Twist valid={vw}\")\n", "print(f\"Output dir: {output_dir}\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}