# RES Site Configuration
SITE_NAME = 'nortan_res'
SITE_DISPLAY_NAME = 'RES Site'

# ML Results - try cross-site raw methods first, fallback to backup structure
ML_RESULTS_PATTERNS = [
    # "../../notebooks/modeling/pile_detection/02_cross_site_raw_methods/inference/output_runs/pointnet_plus_plus_inference/nortan_res/nortan_res_pile_detections_20250728_202947.csv"
    "../../notebooks/modeling/pile_detection/02_classical_ml/output_runs/clean_validation/nortan_res_ml_detected_piles_20250815_101635.csv"
]

POINT_CLOUD_PATH = '../../data/raw/nortan_res/pointcloud/Block_11_2m.las'
SITE_CRS = 'EPSG:32614'  # UTM Zone 14N
EXPECTED_PILES = 368

OUTPUT_DIR = "output_runs/geometric_analysis_res"

# Analysis Parameters
MAX_ANALYSIS_PILES = 300
CONFIDENCE_THRESHOLD = 0.6
SAMPLE_POINT_CLOUD = True
POINT_CLOUD_SAMPLE_RATIO = 0.3

# Measurement Parameters
PILE_EXTRACTION_RADIUS = 2.0
MIN_PILE_POINTS = 8
HEIGHT_ANALYSIS_RADIUS = 1.5
VERTICALITY_ANALYSIS_RADIUS = 0.5

# Donut Parameters
DONUT_INNER_RADIUS = 0.8
DONUT_OUTER_RADIUS = 2.5

# Spacing Parameters
MAX_NEIGHBOR_DISTANCE = 12.0
WITHIN_TRACKER_RANGE = [2.0, 6.0]
BETWEEN_TRACKER_RANGE = [6.0, 12.0]

print(f"Configuration: {SITE_DISPLAY_NAME} | Max piles: {MAX_ANALYSIS_PILES} | Sampling: {POINT_CLOUD_SAMPLE_RATIO*100:.0f}%")

import numpy as np
import pandas as pd
import laspy
import glob
from pathlib import Path
import geopandas as gpd
from shapely.geometry import Point
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

from scipy.spatial import cKDTree
from scipy.stats import describe
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN

print("Libraries imported successfully")

def build_spatial_index(points):
    """Build KD-tree for fast spatial queries"""
    return cKDTree(points[:, :2])

def extract_pile_z_coordinates(pile_locations_2d, points, spatial_index, search_radius=2.0):
    """Extract Z coordinates for pile locations from point cloud"""
    pile_z_coords = []
    
    for pile_xy in pile_locations_2d:
        indices = spatial_index.query_ball_point(pile_xy, search_radius)
        
        if len(indices) > 0:
            nearby_points = points[indices]
            pile_z = np.percentile(nearby_points[:, 2], 95)
        else:
            pile_z = np.median(points[:, 2])
        
        pile_z_coords.append(pile_z)
    
    return np.array(pile_z_coords)

def calculate_pile_height_adaptive(points, spatial_index, pile_center, search_radius=3.0, min_points=15):
    """Improved adaptive donut approach with multiple rings and slope compensation"""
    # Get all points within search radius
    indices = spatial_index.query_ball_point(pile_center, search_radius)
    if len(indices) < min_points:
        return np.nan
    
    region_points = points[indices]
    distances = np.sqrt((region_points[:, 0] - pile_center[0])**2 + 
                       (region_points[:, 1] - pile_center[1])**2)
    max_distance = np.max(distances)
    
    # Find highest point as pile top
    highest_idx = np.argmax(region_points[:, 2])
    pile_top = region_points[highest_idx, 2]
    
    # Define multiple ring zones for ground estimation
    ring_zones = [(70, 80), (80, 90), (90, 95)]  # Percentages of max_distance
    ground_levels = []
    weights = []
    
    for inner_pct, outer_pct in ring_zones:
        inner_radius = (inner_pct / 100) * max_distance
        outer_radius = (outer_pct / 100) * max_distance
        
        ring_mask = (distances >= inner_radius) & (distances <= outer_radius)
        ring_points = region_points[ring_mask]
        
        if len(ring_points) >= min_points:
            # Remove outliers using IQR
            z_values = ring_points[:, 2]
            Q1, Q3 = np.percentile(z_values, [25, 75])
            IQR = Q3 - Q1
            valid_mask = (z_values >= Q1 - 1.5 * IQR) & (z_values <= Q3 + 1.5 * IQR)
            filtered_z = z_values[valid_mask]
            
            if len(filtered_z) >= min_points:
                ring_mean = np.mean(filtered_z)
                ring_std = np.std(filtered_z)
                weight = len(filtered_z) / (ring_std + 1e-6)  # Weight by point count and consistency
                
                ground_levels.append(ring_mean)
                weights.append(weight)
    
    if not ground_levels:
        return np.nan
    
    # Calculate weighted average ground level
    weights = np.array(weights)
    ground_levels = np.array(ground_levels)
    ground_level = np.average(ground_levels, weights=weights)
    
    height = pile_top - ground_level
    return max(0, height)

def calculate_pile_verticality_simple(points, spatial_index, pile_center, radius=1.0):
    """Simple verticality calculation using top-bottom displacement"""
    indices = spatial_index.query_ball_point(pile_center, radius)
    if len(indices) < 5:
        return np.nan
    
    pile_points = points[indices]
    z_coords = pile_points[:, 2]
    z_range = np.max(z_coords) - np.min(z_coords)
    
    if z_range < 0.5:
        return np.nan
    
    z_threshold = np.min(z_coords) + 0.3 * z_range
    high_points = pile_points[z_coords > z_threshold]
    
    if len(high_points) < 3:
        return np.nan
    
    top_center = np.mean(high_points[:, :2], axis=0)
    horizontal_displacement = np.linalg.norm(top_center - pile_center)
    vertical_height = z_range
    
    if vertical_height < 0.1:
        return np.nan
    
    lean_angle = np.degrees(np.arctan(horizontal_displacement / vertical_height))
    return lean_angle

print("Analysis functions defined")

# Updated pattern to match your file names
print("Loading ML detection results...")

files = []
used_pattern = None
for pattern in ML_RESULTS_PATTERNS:
    files = glob.glob(pattern)
    if files:
        used_pattern = pattern
        break

if not files:
    raise FileNotFoundError(f"No ML results found in any of: {ML_RESULTS_PATTERNS}")

# Load latest file
latest_file = max(files, key=lambda x: Path(x).stat().st_mtime)
print(f"Found {len(files)} files using pattern: {used_pattern}")
print(f"Loading latest: {Path(latest_file).name}")

ml_results = pd.read_csv(latest_file)
print(f"Loaded {len(ml_results)} pile detections")

# Standardize column names
if 'pile_probability' in ml_results.columns:
    ml_results = ml_results.rename(columns={'pile_probability': 'confidence'})
if 'x' in ml_results.columns and 'y' in ml_results.columns:
    ml_results = ml_results.rename(columns={'x': 'utm_x', 'y': 'utm_y'})

# Filter by confidence
high_conf_piles = ml_results[ml_results['confidence'] >= CONFIDENCE_THRESHOLD].copy()
print(f"High confidence piles (>={CONFIDENCE_THRESHOLD}): {len(high_conf_piles)}")

# Sample for analysis
if len(high_conf_piles) > MAX_ANALYSIS_PILES:
    sampled_piles = high_conf_piles.sample(n=MAX_ANALYSIS_PILES, random_state=42)
    print(f"Sampled {MAX_ANALYSIS_PILES} piles for analysis")
else:
    sampled_piles = high_conf_piles
    print(f"Using all {len(sampled_piles)} high confidence piles")

# Extract 2D coordinates
pile_locations_2d = sampled_piles[['utm_x', 'utm_y']].values
print(f"Extracted 2D coordinates for {len(pile_locations_2d)} piles")


print("Loading point cloud data...")

las_file = laspy.read(POINT_CLOUD_PATH)
points = np.vstack([las_file.x, las_file.y, las_file.z]).T

if SAMPLE_POINT_CLOUD:
    n_sample = int(len(points) * POINT_CLOUD_SAMPLE_RATIO)
    sample_indices = np.random.choice(len(points), n_sample, replace=False)
    points = points[sample_indices]
    print(f"Sampled {len(points):,} points ({POINT_CLOUD_SAMPLE_RATIO*100:.0f}%)")
else:
    print(f"Using full point cloud: {len(points):,} points")

# Build spatial index and extract Z coordinates
spatial_index = build_spatial_index(points)
pile_z_coords = extract_pile_z_coordinates(pile_locations_2d, points, spatial_index)
pile_locations = np.column_stack([pile_locations_2d, pile_z_coords])
print(f"Extracted Z coordinates for {len(pile_locations)} piles")

from scipy.spatial import cKDTree
import numpy as np, json
from pathlib import Path
from datetime import datetime

def analyze_spacing(pile_xy, max_dist=12.0, ang_tol=15.0):
    XY = np.asarray(pile_xy, float)
    T = cKDTree(XY)
    angs, dists = [], []
    for i, p in enumerate(XY):
        for j in T.query_ball_point(p, max_dist):
            if j <= i: continue
            dx, dy = XY[j] - p
            dists.append(float(np.hypot(dx, dy)))
            angs.append((np.degrees(np.arctan2(dy, dx)) + 180) % 180)
    if not dists:
        return None

    angs = np.array(angs); dists = np.array(dists)
    H, E = np.histogram(angs, bins=180, range=(0,180))
    a0 = 0.5*(E[np.argmax(H)] + E[np.argmax(H)+1])     # primary axis
    a1 = (a0 + 90) % 180                               # orthogonal

    def ad(a,b): d=abs(a-b); return min(d,180-d)
    intra = dists[[ad(a, a0) <= ang_tol for a in angs]]
    inter = dists[[ad(a, a1) <= ang_tol for a in angs]]

    # keep intra = smaller mean
    if np.mean(intra) > np.mean(inter):
        intra, inter, a0, a1 = inter, intra, a1, a0

    return {
        "pairs": int(dists.size),
        "overall_mean": float(dists.mean()), "overall_std": float(dists.std()),
        "intra_deg": float(a0), "intra_mean": float(np.mean(intra)), "intra_std": float(np.std(intra)), "intra_n": int(intra.size),
        "inter_deg": float(a1), "inter_mean": float(np.mean(inter)), "inter_std": float(np.std(inter)), "inter_n": int(inter.size),
    }

def print_and_save_spacing(site_name, pile_xy, out_dir="output_runs/spacing"):
    s = analyze_spacing(pile_xy)
    if not s:
        print(f"{site_name}: no spacing pairs.")
        return {}
    print(f"{site_name} | Pairs={s['pairs']} | Overall={s['overall_mean']:.2f}±{s['overall_std']:.2f} m")
    print(f"Intra ({s['intra_deg']:.1f}°): {s['intra_mean']:.2f}±{s['intra_std']:.2f} m (n={s['intra_n']})")
    print(f"Inter ({s['inter_deg']:.1f}°): {s['inter_mean']:.2f}±{s['inter_std']:.2f} m (n={s['inter_n']})")
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    Path(out_dir).mkdir(parents=True, exist_ok=True)
    with open(Path(out_dir)/f"{site_name}_spacing_{ts}.json","w") as f:
        json.dump({"site": site_name, **s}, f, indent=2)
    return s

pile_xy = pile_locations[:, :2]  

spacing_stats =print_and_save_spacing(SITE_NAME, pile_xy)


print("Analyzing pile heights using adaptive multi-ring approach...")

pile_heights = []
for pile_center in pile_locations:
    height = calculate_pile_height_adaptive(
        points, spatial_index, pile_center[:2], 
        search_radius=3.0, min_points=15
    )
    pile_heights.append(height)

pile_heights = np.array(pile_heights)
valid_heights = pile_heights[~np.isnan(pile_heights)]

if len(valid_heights) > 0:
    print(f"Valid height measurements: {len(valid_heights)}/{len(pile_locations)}")
    print(f"Height statistics: mean={np.mean(valid_heights):.2f}m, std={np.std(valid_heights):.2f}m")
    print(f"Height range: {np.min(valid_heights):.2f}m - {np.max(valid_heights):.2f}m")
    
    height_stats = {
        'valid_count': len(valid_heights),
        'total_count': len(pile_locations),
        'mean_height': np.mean(valid_heights),
        'std_height': np.std(valid_heights),
        'min_height': np.min(valid_heights),
        'max_height': np.max(valid_heights)
    }
else:
    print("No valid height measurements")
    height_stats = {}

print("Analyzing C-section pile verticality & twist...")

# Full-res points & KD-tree
las_full = laspy.read(POINT_CLOUD_PATH)
P = np.vstack([las_full.x, las_full.y, las_full.z]).T
tree = cKDTree(P[:, :2])

# Ensure pile_id
if 'pile_id' not in sampled_piles.columns:
    sampled_piles = sampled_piles.copy()
    sampled_piles['pile_id'] = np.arange(len(sampled_piles))

def fit_line_pca(pts):
    X = pts - pts.mean(axis=0, keepdims=True)
    _, _, Vt = np.linalg.svd(X, full_matrices=False)
    v = Vt[0]; v /= (np.linalg.norm(v)+1e-12)
    if v[2] < 0: v = -v
    return v, pts.mean(axis=0)

def c_section_metrics(center_xy,
                      neigh_radius=1.0,      # small neighborhood
                      gate_cyl=0.35,         # tight radial gate (m)
                      min_pts=25,
                      min_height=0.15,
                      top_keep=0.60,         # keep top 60% for axis estimate
                      slice_top=0.20):       # use top 20% for twist
    # 1) gather
    idx = tree.query_ball_point(center_xy, neigh_radius)
    if not idx: return np.nan, np.nan
    N = P[idx]
    # radial gate (coarse)
    r = np.hypot(N[:,0]-center_xy[0], N[:,1]-center_xy[1])
    C = N[r <= gate_cyl]
    if C.shape[0] < min_pts: return np.nan, np.nan

    # 2) keep upper band to avoid ground
    z = C[:,2]; zmin, zmax = z.min(), z.max()
    if (zmax - zmin) < min_height: return np.nan, np.nan
    z_lo = zmin + (1.0 - top_keep) * (zmax - zmin)
    U = C[z >= z_lo]
    if U.shape[0] < min_pts//2: return np.nan, np.nan

    # 3) first axis fit (robust enough for C-section)
    axis, p0 = fit_line_pca(U)

    # 4) rotate points so axis ≈ Z
    zhat = np.array([0.,0.,1.])
    # rotation matrix from axis->zhat
    a = axis/np.linalg.norm(axis); b = zhat
    v = np.cross(a,b); s = np.linalg.norm(v); c = float(np.dot(a,b))
    if s < 1e-12:
        R = np.eye(3) if c>0 else -np.eye(3)
    else:
        K = np.array([[0,-v[2],v[1]],[v[2],0,-v[0]],[-v[1],v[0],0]])/ (s+1e-12)
        R = np.eye(3) + K + K@K*((1-c)/(s**2+1e-12))

    Z = (R @ (C - p0).T).T
    # 5) top slice for twist (thin slab)
    z2 = Z[:,2]; z2min, z2max = z2.min(), z2.max()
    Ztop = Z[z2 >= z2min + (1.0 - slice_top)*(z2max - z2min)]
    if Ztop.shape[0] < min_pts//2: return np.nan, np.nan

    # 6) 2D PCA on XY of top slice -> major axis direction in cross-section
    XY = Ztop[:, :2] - Ztop[:, :2].mean(axis=0, keepdims=True)
    C2 = np.cov(XY.T)
    eigvals, eigvecs = np.linalg.eigh(C2)
    major = eigvecs[:,1]  # larger variance direction in the section plane

    # twist = angle of 'major' axis vs global X after axis alignment
    twist = np.degrees(np.arctan2(major[1], major[0])) % 180.0  # 0..180

    # 7) lean angle from original axis
    horiz = np.hypot(axis[0], axis[1])
    lean = np.degrees(np.arctan2(horiz, abs(axis[2])))

    return float(lean), float(twist)

# Run
results = [c_section_metrics((row.utm_x, row.utm_y)) for _, row in sampled_piles.iterrows()]
angles = np.array([r[0] for r in results], dtype=float)
twists = np.array([r[1] for r in results], dtype=float)

valid_mask = ~np.isnan(angles)
print(f"Valid={valid_mask.sum()}/{len(angles)}, "
      f"Mean tilt={np.nanmean(angles):.2f}°, Std={np.nanstd(angles):.2f}°")

# Quality & stats
quality = np.where(np.isnan(angles), "Unreliable",
           np.where(angles <= 2.0, "Excellent",
           np.where(angles <= 5.0, "Good", "Poor")))

from collections import Counter
verticality_stats = {
    "valid_count": int(np.isfinite(angles).sum()),
    "total_count": int(len(angles)),
    "mean_deg": float(np.nanmean(angles)),
    "std_deg": float(np.nanstd(angles)),
    "min_deg": float(np.nanmin(angles)),
    "max_deg": float(np.nanmax(angles)),
    "quality_bins": dict(Counter(quality)),
    "twist_mean_deg": float(np.nanmean(twists)),
    "twist_std_deg": float(np.nanstd(twists))
}

pile_lean_angles = angles  # keep for export
pile_twist_angles = twists


print("Exporting analysis results...")

# Setup
output_dir = Path(OUTPUT_DIR); output_dir.mkdir(parents=True, exist_ok=True)
ts = datetime.now().strftime('%Y%m%d_%H%M%S')

# Per-pile table
pile_df = pd.DataFrame({
    "pile_id": sampled_piles["pile_id"].values,
    "utm_x": pile_locations[:, 0],
    "utm_y": pile_locations[:, 1],
    "z_extracted": pile_locations[:, 2],
    "confidence": sampled_piles["confidence"].values,
    "pile_height": pile_heights,
    "lean_angle_deg": pile_lean_angles,      # from C-section fit
    "twist_angle_deg": pile_twist_angles     # from C-section top-slice PCA
})
for c in ["utm_x","utm_y","z_extracted","confidence","pile_height","lean_angle_deg","twist_angle_deg"]:
    if c in pile_df: pile_df[c] = pile_df[c].astype(float).round(3)

# Geo export
gdf = gpd.GeoDataFrame(pile_df, geometry=[Point(xy) for xy in zip(pile_df.utm_x, pile_df.utm_y)], crs=SITE_CRS)
csv_name     = f"{SITE_NAME}_csection_geometric_{ts}.csv"
geojson_name = f"{SITE_NAME}_csection_geometric_{ts}.geojson"
gdf.to_csv(output_dir / csv_name, index=False)
gdf.to_file(output_dir / geojson_name, driver="GeoJSON")

print(f"Saved: {csv_name} | {geojson_name}" )


# Summary
summary = {
    "site_name": SITE_NAME,
    "site_crs": SITE_CRS,
    "analysis_timestamp": ts,
    "total_piles_analyzed": int(len(pile_df)),
    "spacing_stats": spacing_stats,
    "height_stats": height_stats,
    "verticality_stats": verticality_stats
}
summary_name = f"{SITE_NAME}_csection_summary_{ts}.json"
with open(output_dir / summary_name, "w") as f:
    json.dump(summary, f, indent=2)

# Log
vh = int(np.isfinite(pile_df["pile_height"]).sum())
vt = int(np.isfinite(pile_df["lean_angle_deg"]).sum())
vw = int(np.isfinite(pile_df["twist_angle_deg"]).sum())
print(f"Saved: {summary_name}")
print(f"Piles={len(pile_df)} | Heights valid={vh} | Tilt valid={vt} | Twist valid={vw}")
print(f"Output dir: {output_dir}")
