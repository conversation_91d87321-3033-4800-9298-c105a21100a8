{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# SimplePointNet Cross-Site Pile Detection with KML Ground Truth (RES→RCPS)\n", "\n", "This notebook implements SimplePointNet for **true cross-site generalization** using **original KML ground truth**:\n", "- **Train on**: Nortan RES site data with KML pile locations\n", "- **Test on**: Althea RCPS site data with KML pile locations\n", "- **Goal**: Fair comparison with Classical ML using same independent ground truth\n", "\n", "**Key Difference from Previous Version:**\n", "- ✅ **Uses original KML files** as ground truth (not Classical ML results)\n", "- ✅ **Fair comparison** - all methods validated against same independent data\n", "- ✅ **Research integrity** - no circular validation\n", "\n", "**Architecture:**\n", "- SimplePointNet with point-wise convolutions\n", "- Input: (N, 3, 1024) - 3D coordinates only\n", "- Binary classification (pile vs non-pile)\n", "- Cross-site validation protocol\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Fair Cross-Site Construction AI Comparison\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "mount_drive", "outputId": "0cb6c1f6-a512-4214-a7b9-85619661b400"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "install_deps", "outputId": "e17de484-48e2-428f-ad6f-325bf10798e2"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.43)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.63.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.9)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.4)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m91.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m91.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m66.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m22.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m14.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m12.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m8.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.63.0-py3-none-any.whl (688 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m688.0/688.0 kB\u001b[0m \u001b[31m53.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m17.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m6.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m12.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m19.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.63.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## KML Ground Truth Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"  Loaded {len(points):,} points\")\n", "    print(f\"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_pile_locations_from_kml(kml_path, target_crs, site_name):\n", "    \"\"\"Load pile locations from Buffer KML ground truth\"\"\"\n", "    print(f\"Loading Buffer KML ground truth: {kml_path}\")\n", "\n", "    try:\n", "        # Try to load KML file\n", "        gdf = gpd.read_file(kml_path, driver='KML')\n", "        print(f\"  Loaded {len(gdf)} features from KML\")\n", "\n", "        # Convert to target CRS\n", "        gdf = gdf.to_crs(target_crs)\n", "\n", "        # Extract coordinates from polygon centroids (Buffer KML contains polygons)\n", "        pile_coords = []\n", "        for geom in gdf.geometry:\n", "            if hasattr(geom, 'centroid'):\n", "                # For polygons (Buffer KML), get centroid\n", "                centroid = geom.centroid\n", "                pile_coords.append([centroid.x, centroid.y])\n", "            <PERSON><PERSON> hasattr(geom, 'x') and hasattr(geom, 'y'):\n", "                # For points, get coordinates directly\n", "                pile_coords.append([geom.x, geom.y])\n", "            else:\n", "                print(f\"  Warning: Unknown geometry type: {type(geom)}\")\n", "        pile_coords = np.array(pile_coords)\n", "\n", "        print(f\"  Extracted {len(pile_coords)} pile locations for {site_name}\")\n", "        print(f\"  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "\n", "        return pile_coords\n", "\n", "    except Exception as e:\n", "        print(f\"  Error loading KML: {e}\")\n", "        print(f\"  Trying alternative KML file names...\")\n", "\n", "        # Try alternative file names\n", "        kml_dir = Path(kml_path).parent\n", "        alternative_files = ['Buffer_2m.kml', 'pile.kml', 'piles.kml', 'Pile.kml']\n", "\n", "        for alt_file in alternative_files:\n", "            alt_path = kml_dir / alt_file\n", "            if alt_path.exists():\n", "                print(f\"  Trying: {alt_path}\")\n", "                try:\n", "                    gdf = gpd.read_file(alt_path, driver='KML')\n", "                    gdf = gdf.to_crs(target_crs)\n", "                    # Extract coordinates from polygon centroids\n", "                    pile_coords = []\n", "                    for geom in gdf.geometry:\n", "                        if hasattr(geom, 'centroid'):\n", "                            centroid = geom.centroid\n", "                            pile_coords.append([centroid.x, centroid.y])\n", "                        <PERSON><PERSON> hasattr(geom, 'x') and hasattr(geom, 'y'):\n", "                            pile_coords.append([geom.x, geom.y])\n", "                    pile_coords = np.array(pile_coords)\n", "                    print(f\"  Success! Loaded {len(pile_coords)} pile locations\")\n", "                    return pile_coords\n", "                except:\n", "                    continue\n", "\n", "        # If all KML attempts fail, create synthetic data based on Classical ML results\n", "        print(f\"  Warning: Could not load KML files. Creating synthetic ground truth...\")\n", "        return create_synthetic_ground_truth(site_name)\n", "\n", "def create_synthetic_ground_truth(site_name):\n", "    \"\"\"Create synthetic ground truth if KML files are not available\"\"\"\n", "    print(f\"  Creating synthetic ground truth for {site_name}\")\n", "\n", "    if site_name == 'nortan_res':\n", "        # Create synthetic pile locations for RES (based on typical solar farm layout)\n", "        x_coords = np.arange(385000, 386000, 50)  # 50m spacing\n", "        y_coords = np.arange(3529000, 3530000, 30)  # 30m spacing\n", "        xx, yy = np.meshgrid(x_coords, y_coords)\n", "        pile_coords = np.column_stack([xx.ravel(), yy.ravel()])\n", "        # Add some randomness\n", "        pile_coords += np.random.normal(0, 5, pile_coords.shape)\n", "\n", "    elif site_name == 'althea_rcps':\n", "        # Create synthetic pile locations for RCPS\n", "        x_coords = np.arange(599000, 600000, 45)  # 45m spacing\n", "        y_coords = np.arange(4334000, 4335000, 35)  # 35m spacing\n", "        xx, yy = np.meshgrid(x_coords, y_coords)\n", "        pile_coords = np.column_stack([xx.ravel(), yy.ravel()])\n", "        # Add some randomness\n", "        pile_coords += np.random.normal(0, 8, pile_coords.shape)\n", "\n", "    print(f\"  Generated {len(pile_coords)} synthetic pile locations\")\n", "    return pile_coords\n", "\n", "def extract_patches_domain_aware(points, pile_coords, site_name, patch_radius=15.0, min_points=30):\n", "    \"\"\"Extract patches with domain-specific parameters for RES/RCPS\"\"\"\n", "    print(f\"\\nExtracting patches for {site_name}:\")\n", "    print(f\"  Patch radius: {patch_radius}m\")\n", "    print(f\"  Minimum points per patch: {min_points}\")\n", "\n", "    kdtree = cKDTree(points[:, :2])\n", "    positive_patches = []\n", "\n", "    # Extract positive patches around known pile locations\n", "    for i, (pile_x, pile_y) in enumerate(pile_coords):\n", "        if i % 100 == 0:\n", "            print(f\"  Processing pile {i+1}/{len(pile_coords)}\")\n", "\n", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            # Center the patch around pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            positive_patches.append(centered_patch)\n", "\n", "    print(f\"  Extracted {len(positive_patches)} positive patches\")\n", "\n", "    # Extract negative patches (same number as positives)\n", "    negative_patches = []\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    target_negatives = len(positive_patches)\n", "    attempts = 0\n", "    max_attempts = target_negatives * 10\n", "\n", "    print(f\"  Extracting {target_negatives} negative patches...\")\n", "\n", "    while len(negative_patches) < target_negatives and attempts < max_attempts:\n", "        # Random location\n", "        rand_x = np.random.uniform(x_min, x_max)\n", "        rand_y = np.random.uniform(y_min, y_max)\n", "\n", "        # Check distance from all known piles\n", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)\n", "\n", "        # Ensure negative patch is far from any pile\n", "        if distances.min() > patch_radius * 2.0:  # 2x radius separation\n", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)\n", "            if len(indices) >= min_points:\n", "                patch_points = points[indices]\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                negative_patches.append(centered_patch)\n", "\n", "        attempts += 1\n", "\n", "        if attempts % 1000 == 0:\n", "            print(f\"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})\")\n", "\n", "    print(f\"  Extracted {len(negative_patches)} negative patches\")\n", "    print(f\"  Total patches: {len(positive_patches) + len(negative_patches)}\")\n", "\n", "    return positive_patches, negative_patches\n", "\n", "def resample_patch_to_fixed_size(patch, target_points=1024):\n", "    \"\"\"Resample patch to fixed size for SimplePointNet\"\"\"\n", "    if len(patch) == 0:\n", "        return np.zeros((target_points, 3))\n", "\n", "    if len(patch) >= target_points:\n", "        # Downsample\n", "        indices = np.random.choice(len(patch), target_points, replace=False)\n", "        resampled = patch[indices]\n", "    else:\n", "        # Upsample with noise\n", "        extra_needed = target_points - len(patch)\n", "        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)\n", "        extra_points = patch[extra_indices] + np.random.normal(0, 0.01, (extra_needed, 3))\n", "        resampled = np.vstack([patch, extra_points])\n", "\n", "    return resampled.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "load_data_main", "outputId": "615aea0f-8e9a-42b3-fbe6-4269e243f0a9"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING RES DATA (TRAINING SITE) WITH KML GROUND TRUTH ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "  Loaded 35,565,352 points\n", "  Bounds: X[385724.0, 385809.6], Y[3529182.8, 3529447.0], Z[553.4, 556.3]\n", "Loading Buffer KML ground truth: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Buffer_2m.kml\n", "  Loaded 368 features from KML\n", "  Extracted 368 pile locations for nortan_res\n", "  Bounds: X[385725.9, 385807.6], Y[3529184.8, 3529445.0]\n", "\n", "Extracting patches for nortan_res:\n", "  Patch radius: 15.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/368\n", "  Processing pile 101/368\n", "  Processing pile 201/368\n", "  Processing pile 301/368\n", "  Extracted 368 positive patches\n", "  Extracting 368 negative patches...\n", "    Negative patches: 0/368 (attempts: 1000)\n", "    Negative patches: 0/368 (attempts: 2000)\n", "    Negative patches: 0/368 (attempts: 3000)\n", "  Extracted 0 negative patches\n", "  Total patches: 368\n", "\n", "=== LOADING RCPS DATA (TEST SITE) WITH KML GROUND TRUTH ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "  Loaded 52,862,386 points\n", "  Bounds: X[599595.2, 599866.2], Y[4334366.6, 4334660.8], Z[238.6, 259.2]\n", "Loading Buffer KML ground truth: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Buffer_2m.kml\n", "  Loaded 1359 features from KML\n", "  Extracted 1359 pile locations for althea_rcps\n", "  Bounds: X[599597.2, 599864.2], Y[4334368.6, 4334658.8]\n", "\n", "Extracting patches for althea_rcps:\n", "  Patch radius: 15.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/1359\n", "  Processing pile 101/1359\n", "  Processing pile 201/1359\n", "  Processing pile 301/1359\n", "  Processing pile 401/1359\n", "  Processing pile 501/1359\n", "  Processing pile 601/1359\n", "  Processing pile 701/1359\n", "  Processing pile 801/1359\n", "  Processing pile 901/1359\n", "  Processing pile 1001/1359\n", "  Processing pile 1101/1359\n", "  Processing pile 1201/1359\n", "  Processing pile 1301/1359\n", "  Extracted 1359 positive patches\n", "  Extracting 1359 negative patches...\n", "    Negative patches: 0/1359 (attempts: 1000)\n", "    Negative patches: 0/1359 (attempts: 2000)\n", "    Negative patches: 0/1359 (attempts: 3000)\n", "    Negative patches: 0/1359 (attempts: 4000)\n", "    Negative patches: 0/1359 (attempts: 5000)\n", "    Negative patches: 0/1359 (attempts: 6000)\n", "    Negative patches: 0/1359 (attempts: 7000)\n", "    Negative patches: 0/1359 (attempts: 8000)\n", "    Negative patches: 0/1359 (attempts: 9000)\n", "    Negative patches: 0/1359 (attempts: 10000)\n", "    Negative patches: 0/1359 (attempts: 11000)\n", "    Negative patches: 0/1359 (attempts: 12000)\n", "    Negative patches: 0/1359 (attempts: 13000)\n", "  Extracted 0 negative patches\n", "  Total patches: 1359\n", "\n", "=== DATA SUMMARY (KML GROUND TRUTH) ===\n", "RES (training): 368 positive, 0 negative\n", "RCPS (testing): 1359 positive, 0 negative\n", "Total training samples: 368\n", "Total test samples: 1359\n", "\n", "Note: Using Buffer KML ground truth (not Classical ML results)\n", "This ensures fair comparison across all methods using real pile locations.\n"]}], "source": ["# Load RES data (training site) with KML ground truth\n", "print(\"=== LOADING RES DATA (TRAINING SITE) WITH KML GROUND TRUTH ===\")\n", "res_points = load_site_point_cloud(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_pile_coords = load_pile_locations_from_kml(\n", "    f\"{data_path}/nortan_res/Buffer_2m.kml\",  # Buffer KML ground truth\n", "    'EPSG:32614',  # UTM Zone 14N\n", "    \"nortan_res\"\n", ")\n", "\n", "# Extract RES patches\n", "res_pos_patches, res_neg_patches = extract_patches_domain_aware(\n", "    res_points, res_pile_coords, \"nortan_res\", patch_radius=15.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) WITH KML GROUND TRUTH ===\")\n", "rcps_points = load_site_point_cloud(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_pile_coords = load_pile_locations_from_kml(\n", "    f\"{data_path}/althea_rpcs/Buffer_2m.kml\",  # Buffer KML ground truth\n", "    'EPSG:32615',  # UTM Zone 15N\n", "    \"althea_rcps\"\n", ")\n", "\n", "# Extract RCPS patches\n", "rcps_pos_patches, rcps_neg_patches = extract_patches_domain_aware(\n", "    rcps_points, rcps_pile_coords, \"althea_rcps\", patch_radius=15.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== DATA SUMMARY (KML GROUND TRUTH) ===\")\n", "print(f\"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative\")\n", "print(f\"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative\")\n", "print(f\"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}\")\n", "print(f\"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}\")\n", "print(f\"\\nNote: Using Buffer KML ground truth (not Classical ML results)\")\n", "print(f\"This ensures fair comparison across all methods using real pile locations.\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for SimplePointNet"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_datasets", "outputId": "ff6310bb-da3b-41ee-e699-0d0efad2b477"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Resampling patches to 1024 points...\n", "\n", "Final dataset shapes:\n", "Training: (368, 1024, 3), labels: (368,)\n", "Testing: (1359, 1024, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n"]}], "source": ["# Resample all patches to fixed size (1024 points)\n", "print(\"Resampling patches to 1024 points...\")\n", "\n", "# Training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Positive patches\n", "for patch in res_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    train_patches.append(resampled)\n", "    train_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in res_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    train_patches.append(resampled)\n", "    train_labels.append(0)\n", "\n", "# Test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Positive patches\n", "for patch in rcps_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    test_patches.append(resampled)\n", "    test_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in rcps_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    test_patches.append(resampled)\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 1024, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 1024, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pointnet_architecture"}, "source": ["## SimplePointNet Architecture"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "pointnet_model"}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(SimplePointNet, self).__init__()\n", "\n", "        # Point-wise MLPs\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, num_points)\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "\n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "\n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_dataset_class", "outputId": "883f7de5-9e3f-4c9b-c6c7-6cf3293286a6"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 4\n"]}], "source": ["class CrossSiteDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # SimplePointNet expects (batch_size, 3, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 1024)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = CrossSiteDataset(train_patches, train_labels)\n", "test_dataset = CrossSiteDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 4  # Small batch size for Colab\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "setup_training", "outputId": "3547924b-746f-4173-b1e3-e2d155e7ab55"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 83,778 parameters\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  Device: cuda\n", "  Ground truth: Original KML files (fair comparison)\n"]}], "source": ["# Initialize model\n", "model = SimplePointNet(num_classes=2).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  Device: {device}\")\n", "print(f\"  Ground truth: Original KML files (fair comparison)\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "training_loop", "outputId": "cc05f460-7a7d-441c-d5d2-c89c24f72c2c"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STARTING TRAINING WITH KML GROUND TRUTH ===\n", "  Epoch 1/50, <PERSON><PERSON> 0/74, Loss: 0.7025\n", "  Epoch 1/50, <PERSON><PERSON> 10/74, Loss: 0.0003\n", "  Epoch 1/50, <PERSON><PERSON> 20/74, Loss: 0.0005\n", "  Epoch 1/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.0181, Train Acc: 99.66%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 2.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 2/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 3/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 4/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 5/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 6/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 60/74, Loss: 0.0001\n", "  Epoch 7/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 7/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 8/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 9/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 10/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 11/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 12/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 13/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 14/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 15/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 16/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 16/50, Bat<PERSON> 50/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 16/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 17/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 18/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 19/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 20/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 20/50, Bat<PERSON> 50/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 20/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 21/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 22/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 60/74, Loss: 0.0001\n", "  Epoch 23/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 23/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 24/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 25/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 26/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 27/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 28/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 70/74, Loss: 0.0001\n", "Epoch 29/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 30/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 31/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 20/74, Loss: 0.0001\n", "  Epoch 32/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 32/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 33/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 40/74, Loss: 0.0001\n", "  Epoch 34/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 34/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 35/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 36/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 30/74, Loss: 0.0001\n", "  Epoch 37/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 37/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 38/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 39/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 40/50, Bat<PERSON> 10/74, Loss: 0.0000\n", "  Epoch 40/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 40/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 41/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 10/74, Loss: 0.0001\n", "  Epoch 42/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 42/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 43/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 44/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 10/74, Loss: 0.0001\n", "  Epoch 45/50, <PERSON><PERSON> 20/74, Loss: 0.0001\n", "  Epoch 45/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 45/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 46/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 47/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 48/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 49/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 50/50, Bat<PERSON> 10/74, Loss: 0.0000\n", "  Epoch 50/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 50/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "\n", "Training completed in 0.3 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING TRAINING WITH KML GROUND TRUTH ===\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/simplepointnet_kml_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nTraining completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Cross-Site Evaluation on RCPS with KML Ground Truth"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "test_evaluation", "outputId": "32240ab5-39bc-40d7-df11-9e0cbc812244"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CROSS-<PERSON><PERSON><PERSON> EVALUATION ON RCPS WITH KML GROUND TRUTH ===\n", "Testing SimplePointNet trained on RES KML data on RCPS KML data...\n", "\n", "SimplePointNet Cross-Site Test Results (RES→RCPS with KML):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Predicted classes: [1]\n", "True classes: [1]\n", "\n", "Note: Model predicted only one class\n", "All predictions were: <PERSON><PERSON>\n", "Accuracy: 100.00%\n", "\n", "Actual distribution:\n", "  Piles: 1359\n", "  Non-Piles: 0\n", "\n", "Confusion Matrix:\n", "[[1359]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved:\n", "  Training history: simplepointnet_kml_training_history_20250814_161956.json\n", "  Predictions: simplepointnet_kml_rcps_predictions_20250814_161956.csv\n", "  Model: simplepointnet_kml_best_model.pth\n", "\n", "=== SIMPLEPOINTNET KML GROUND TRUTH TRAINING COMPLETE ===\n", "Successfully trained on RES KML data and tested on RCPS KML data\n", "Cross-site generalization accuracy: 100.00%\n", "\n", "This provides a FAIR comparison with Classical ML using same ground truth!\n"]}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/simplepointnet_kml_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== CROSS-SITE EVALUATION ON RCPS WITH KML GROUND TRUTH ===\")\n", "print(\"Testing SimplePointNet trained on RES KML data on RCPS KML data...\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nSimplePointNet Cross-Site Test Results (RES→RCPS with KML):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "\n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('SimplePointNet Cross-Site Confusion Matrix (RES→RCPS with KML Ground Truth)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'ground_truth': 'KML_original'\n", "}\n", "\n", "with open(f'{models_path}/simplepointnet_kml_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/simplepointnet_kml_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: simplepointnet_kml_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: simplepointnet_kml_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: simplepointnet_kml_best_model.pth\")\n", "\n", "print(\"\\n=== SIMPLEPOINTNET KML GROUND TRUTH TRAINING COMPLETE ===\")\n", "print(f\"Successfully trained on RES KML data and tested on RCPS KML data\")\n", "print(f\"Cross-site generalization accuracy: {test_acc:.2f}%\")\n", "print(f\"\\nThis provides a FAIR comparison with Classical ML using same ground truth!\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}