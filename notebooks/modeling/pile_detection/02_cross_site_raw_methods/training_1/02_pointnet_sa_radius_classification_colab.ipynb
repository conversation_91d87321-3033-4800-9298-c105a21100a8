{"cells": [{"cell_type": "markdown", "metadata": {"id": "hf02wH1cKVzW"}, "source": ["# PointNet++ Cross-Site Pile Detection (RES→RCPS)\n", "\n", "This notebook implements PointNet++ for **true cross-site generalization**:\n", "- **Train on**: Nortan RES site data\n", "- **Test on**: Althea RCPS site data\n", "- **Goal**: Test if deep learning can generalize across construction sites\n", "\n", "**Architecture:**\n", "- PointNet++ with set abstraction layers\n", "- Input: (N, 1024, 3) - 3D coordinates only\n", "- Binary classification (pile vs non-pile)\n", "- Cross-site validation protocol\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Cross-Site Construction AI Generalization\n"]}, {"cell_type": "markdown", "metadata": {"id": "W0ubzvdCLikt"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s482wPMALjl1", "outputId": "bb8c18ab-6da6-4af5-fdd7-f398899fa3c7"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "install_deps", "outputId": "aa48bbf9-de4b-4e0b-acd9-e914282c8908"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.42)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.62.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.3)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m925.6 kB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m34.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m103.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m83.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m30.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m19.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m14.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m11.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.62.0-py3-none-any.whl (681 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m681.8/681.8 kB\u001b[0m \u001b[31m57.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m25.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m9.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m16.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m27.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.62.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## RES/RCPS Data Loading with Domain-Aware Patch Extraction"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "load_res_rcps_data"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"  Loaded {len(points):,} points\")\n", "    print(f\"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_pile_locations_from_csv(csv_path, site_name):\n", "    \"\"\"Load pile locations from Classical ML results CSV\"\"\"\n", "    print(f\"Loading pile locations from: {csv_path}\")\n", "    df = pd.read_csv(csv_path)\n", "\n", "    # Extract coordinates based on CSV format\n", "    if 'utm_x' in df.columns and 'utm_y' in df.columns:\n", "        pile_coords = df[['utm_x', 'utm_y']].values\n", "    elif 'x' in df.columns and 'y' in df.columns:\n", "        pile_coords = df[['x', 'y']].values\n", "    else:\n", "        raise ValueError(f\"Could not find coordinate columns in {csv_path}\")\n", "\n", "    print(f\"  Loaded {len(pile_coords)} pile locations for {site_name}\")\n", "    print(f\"  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "    return pile_coords\n", "\n", "def extract_patches_domain_aware(points, pile_coords, site_name, patch_radius=15.0, min_points=30):\n", "    \"\"\"Extract patches with domain-specific parameters for RES/RCPS\"\"\"\n", "    print(f\"\\nExtracting patches for {site_name}:\")\n", "    print(f\"  Patch radius: {patch_radius}m\")\n", "    print(f\"  Minimum points per patch: {min_points}\")\n", "\n", "    kdtree = cKDTree(points[:, :2])\n", "    positive_patches = []\n", "\n", "    # Extract positive patches around known pile locations\n", "    for i, (pile_x, pile_y) in enumerate(pile_coords):\n", "        if i % 100 == 0:\n", "            print(f\"  Processing pile {i+1}/{len(pile_coords)}\")\n", "\n", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            # Center the patch around pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            positive_patches.append(centered_patch)\n", "\n", "    print(f\"  Extracted {len(positive_patches)} positive patches\")\n", "\n", "    # Extract negative patches (same number as positives)\n", "    negative_patches = []\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    target_negatives = len(positive_patches)\n", "    attempts = 0\n", "    max_attempts = target_negatives * 10\n", "\n", "    print(f\"  Extracting {target_negatives} negative patches...\")\n", "\n", "    while len(negative_patches) < target_negatives and attempts < max_attempts:\n", "        # Random location\n", "        rand_x = np.random.uniform(x_min, x_max)\n", "        rand_y = np.random.uniform(y_min, y_max)\n", "\n", "        # Check distance from all known piles\n", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)\n", "\n", "        # Ensure negative patch is far from any pile\n", "        if distances.min() > patch_radius * 2.0:  # 2x radius separation\n", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)\n", "            if len(indices) >= min_points:\n", "                patch_points = points[indices]\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                negative_patches.append(centered_patch)\n", "\n", "        attempts += 1\n", "\n", "        if attempts % 1000 == 0:\n", "            print(f\"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})\")\n", "\n", "    print(f\"  Extracted {len(negative_patches)} negative patches\")\n", "    print(f\"  Total patches: {len(positive_patches) + len(negative_patches)}\")\n", "\n", "    return positive_patches, negative_patches\n", "\n", "def resample_patch_to_fixed_size(patch, target_points=1024):\n", "    \"\"\"Resample patch to fixed size for PointNet++\"\"\"\n", "    if len(patch) == 0:\n", "        return np.zeros((target_points, 3))\n", "\n", "    if len(patch) >= target_points:\n", "        # Downsample\n", "        indices = np.random.choice(len(patch), target_points, replace=False)\n", "        resampled = patch[indices]\n", "    else:\n", "        # Upsample with noise\n", "        extra_needed = target_points - len(patch)\n", "        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)\n", "        extra_points = patch[extra_indices] + np.random.normal(0, 0.01, (extra_needed, 3))\n", "        resampled = np.vstack([patch, extra_points])\n", "\n", "    return resampled.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "load_data_main", "outputId": "673c13e5-96bd-4281-ba25-05cdfa18a5ae"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING RES DATA (TRAINING SITE) ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "  Loaded 35,565,352 points\n", "  Bounds: X[385724.0, 385809.6], Y[3529182.8, 3529447.0], Z[553.4, 556.3]\n", "Loading pile locations from: /content/drive/MyDrive/pointnet_pile_detection/data/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv\n", "  Loaded 368 pile locations for nortan_res\n", "  Bounds: X[385725.9, 385807.6], Y[3529184.8, 3529445.0]\n", "\n", "Extracting patches for nortan_res:\n", "  Patch radius: 15.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/368\n", "  Processing pile 101/368\n", "  Processing pile 201/368\n", "  Processing pile 301/368\n", "  Extracted 368 positive patches\n", "  Extracting 368 negative patches...\n", "    Negative patches: 0/368 (attempts: 1000)\n", "    Negative patches: 0/368 (attempts: 2000)\n", "    Negative patches: 0/368 (attempts: 3000)\n", "  Extracted 0 negative patches\n", "  Total patches: 368\n", "\n", "=== LOADING RCPS DATA (TEST SITE) ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "  Loaded 52,862,386 points\n", "  Bounds: X[599595.2, 599866.2], Y[4334366.6, 4334660.8], Z[238.6, 259.2]\n", "Loading pile locations from: /content/drive/MyDrive/pointnet_pile_detection/data/ground_truth/rcps_generalization_results_20250807_221320.csv\n", "  Loaded 1359 pile locations for althea_rcps\n", "  Bounds: X[599597.2, 599864.2], Y[4334368.6, 4334658.8]\n", "\n", "Extracting patches for althea_rcps:\n", "  Patch radius: 15.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/1359\n", "  Processing pile 101/1359\n", "  Processing pile 201/1359\n", "  Processing pile 301/1359\n", "  Processing pile 401/1359\n", "  Processing pile 501/1359\n", "  Processing pile 601/1359\n", "  Processing pile 701/1359\n", "  Processing pile 801/1359\n", "  Processing pile 901/1359\n", "  Processing pile 1001/1359\n", "  Processing pile 1101/1359\n", "  Processing pile 1201/1359\n", "  Processing pile 1301/1359\n", "  Extracted 1359 positive patches\n", "  Extracting 1359 negative patches...\n", "    Negative patches: 0/1359 (attempts: 1000)\n", "    Negative patches: 0/1359 (attempts: 2000)\n", "    Negative patches: 0/1359 (attempts: 3000)\n", "    Negative patches: 0/1359 (attempts: 4000)\n", "    Negative patches: 0/1359 (attempts: 5000)\n", "    Negative patches: 0/1359 (attempts: 6000)\n", "    Negative patches: 0/1359 (attempts: 7000)\n", "    Negative patches: 0/1359 (attempts: 8000)\n", "    Negative patches: 0/1359 (attempts: 9000)\n", "    Negative patches: 0/1359 (attempts: 10000)\n", "    Negative patches: 0/1359 (attempts: 11000)\n", "    Negative patches: 0/1359 (attempts: 12000)\n", "    Negative patches: 0/1359 (attempts: 13000)\n", "  Extracted 0 negative patches\n", "  Total patches: 1359\n", "\n", "=== DATA SUMMARY ===\n", "RES (training): 368 positive, 0 negative\n", "RCPS (testing): 1359 positive, 0 negative\n", "Total training samples: 368\n", "Total test samples: 1359\n"]}], "source": ["# Load RES data (training site)\n", "print(\"=== LOADING RES DATA (TRAINING SITE) ===\")\n", "res_points = load_site_point_cloud(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv\",\n", "    \"nortan_res\"\n", ")\n", "\n", "# Extract RES patches\n", "res_pos_patches, res_neg_patches = extract_patches_domain_aware(\n", "    res_points, res_pile_coords, \"nortan_res\", patch_radius=15.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) ===\")\n", "rcps_points = load_site_point_cloud(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/rcps_generalization_results_20250807_221320.csv\",\n", "    \"althea_rcps\"\n", ")\n", "\n", "# Extract RCPS patches\n", "rcps_pos_patches, rcps_neg_patches = extract_patches_domain_aware(\n", "    rcps_points, rcps_pile_coords, \"althea_rcps\", patch_radius=15.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== DATA SUMMARY ===\")\n", "print(f\"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative\")\n", "print(f\"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative\")\n", "print(f\"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}\")\n", "print(f\"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for PointNet++"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_datasets", "outputId": "426ef2df-0475-44f1-b0bf-cbf614c6c1dc"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Resampling patches to 1024 points...\n", "\n", "Final dataset shapes:\n", "Training: (368, 1024, 3), labels: (368,)\n", "Testing: (1359, 1024, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n"]}], "source": ["# Resample all patches to fixed size (1024 points)\n", "print(\"Resampling patches to 1024 points...\")\n", "\n", "# Training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Positive patches\n", "for patch in res_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    train_patches.append(resampled)\n", "    train_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in res_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    train_patches.append(resampled)\n", "    train_labels.append(0)\n", "\n", "# Test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Positive patches\n", "for patch in rcps_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    test_patches.append(resampled)\n", "    test_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in rcps_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 1024)\n", "    test_patches.append(resampled)\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 1024, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 1024, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pointnet_architecture"}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": 23, "metadata": {"id": "VorD7Gk-A9i_"}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(xyz, points)\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        S = self.npoint\n", "\n", "        # Use random sampling instead of farthest point sampling\n", "        fps_idx = torch.randint(0, N, (B, S), device=xyz.device, dtype=torch.long)\n", "        new_xyz = self.index_points(xyz, fps_idx)\n", "\n", "        # Simplified ball query - use k-nearest neighbors\n", "        idx = self.knn_query(xyz, new_xyz, self.nsample)\n", "        grouped_xyz = self.index_points(xyz, idx)\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = self.index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points\n", "\n", "    def knn_query(self, xyz, new_xyz, k):\n", "        \"\"\"Simplified k-nearest neighbor query\"\"\"\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "\n", "        # Compute pairwise distances\n", "        xyz_expanded = xyz.unsqueeze(2)  # (B, N, 1, C)\n", "        new_xyz_expanded = new_xyz.unsqueeze(1)  # (B, 1, S, C)\n", "\n", "        # Calculate squared distances\n", "        dists = torch.sum((xyz_expanded - new_xyz_expanded) ** 2, dim=-1)  # (B, N, S)\n", "\n", "        # Get k nearest neighbors for each query point\n", "        _, idx = torch.topk(dists, k, dim=0, largest=False)  # (k, N, S)\n", "        idx = idx.permute(1, 2, 0)  # (B, S, k)\n", "\n", "        return idx\n", "\n", "    def index_points(self, points, idx):\n", "        device = points.device\n", "        B = points.shape[0]\n", "        view_shape = list(idx.shape)\n", "        view_shape[1:] = [1] * (len(view_shape) - 1)\n", "        repeat_shape = list(idx.shape)\n", "        repeat_shape[0] = 1\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "        new_points = points[batch_indices, idx, :]\n", "        return new_points\n", "\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Simplified set abstraction layers\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels + 3, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 16, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 128 + 3, [128, 256, 512], True)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(512, 256)\n", "        self.bn1 = nn.BatchNorm1d(256)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.Linear(256, 128)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "\n", "        # Set abstraction layers\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        # Classification\n", "        x = l3_points.view(B, 512)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": 27, "metadata": {"id": "iomASvLaBT8X"}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(SimplePointNet, self).__init__()\n", "\n", "        # Point-wise MLPs\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, num_points)\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "\n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "\n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": 28, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_dataset_class", "outputId": "2a378a74-02bb-45af-fd65-ad4224ef707f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 16\n"]}], "source": ["class CrossSiteDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # SimplePointNet expects (batch_size, 3, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 1024)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = CrossSiteDataset(train_patches, train_labels)\n", "test_dataset = CrossSiteDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 16  # Smaller batch size for Colab\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "setup_training", "outputId": "bad36c1b-a0c8-45e1-ee73-8c43f44be4f4"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 83,778 parameters\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  Device: cuda\n"]}], "source": ["# Initialize model\n", "# model = PointNetPlusPlus(num_classes=2, in_channels=3).to(device)\n", "# print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "model = SimplePointNet(num_classes=2).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  Device: {device}\")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "training_loop", "outputId": "e8ebc5a5-e5bc-4991-cd26-741294251e8e"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== STARTING TRAINING ===\n", "  Epoch 1/50, <PERSON><PERSON> 0/19, Loss: 0.6583\n", "  Epoch 1/50, <PERSON><PERSON> 10/19, Loss: 0.0002\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.0590, Train Acc: 98.30%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 1.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 2/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 3/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 4/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 5/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 6/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 7/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 8/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 9/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 10/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 11/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 12/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 13/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 14/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 15/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 16/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 16/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 17/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 18/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 19/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 20/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 20/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 21/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 22/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 23/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 24/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 25/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 25/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 26/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 27/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 28/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 29/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 30/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 30/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 31/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 32/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 33/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 34/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 35/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 36/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 37/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 38/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 39/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 40/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 40/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 41/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 42/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 43/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 44/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 45/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 46/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 47/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 48/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 10/19, Loss: 0.0000\n", "Epoch 49/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/19, Loss: 0.0000\n", "  Epoch 50/50, Bat<PERSON> 10/19, Loss: 0.0000\n", "Epoch 50/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.1s\n", "--------------------------------------------------------------------------------\n", "\n", "Training completed in 0.1 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING TRAINING ===\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/pointnet_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nTraining completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Cross-Site Evaluation on RCPS"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 957}, "id": "test_evaluation", "outputId": "2acfc236-5c6c-4037-c56d-3c37f8e91759"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CROSS-SITE EVALUATION ON RCPS ===\n", "Testing PointNet++ trained on RES data on RCPS data...\n", "\n", "Cross-Site Test Results (RES→RCPS):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Predicted classes: [1]\n", "True classes: [1]\n", "\n", "Note: Model predicted only one class\n", "All predictions were: <PERSON><PERSON>\n", "Accuracy: 100.00%\n", "\n", "Actual distribution:\n", "  Piles: 1359\n", "  Non-Piles: 0\n", "\n", "Confusion Matrix:\n", "[[1359]]\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/pointnet_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== CROSS-SITE EVALUATION ON RCPS ===\")\n", "print(\"Testing PointNet++ trained on RES data on RCPS data...\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nCross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "\n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix (this should still work)\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('PointNet++ Cross-Site Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "results_analysis"}, "source": ["## Results Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "plot_training_curves", "outputId": "5d97c772-5140-4bc0-c836-a3acbb09e582"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== FINAL RESULTS SUMMARY ===\n", "Training Site: Nortan RES (368 samples)\n", "Test Site: Althea RCPS (1359 samples)\n", "Best Validation Accuracy: 100.00%\n", "Cross-Site Test Accuracy: 100.00%\n", "Cross-Site Test F1-Score: 1.0000\n", "\n", "Comparison with Classical ML:\n", "  Classical ML (local): 100.0% accuracy\n", "  PointNet++ (cross-site): 100.00% accuracy\n", "  Generalization gap: 0.00%\n"]}], "source": ["# Plot training curves\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Loss curves\n", "ax1.plot(train_losses, label='Training Loss', color='blue')\n", "ax1.plot(val_losses, label='Validation Loss', color='red')\n", "ax1.set_title('Training and Validation Loss')\n", "ax1.set_xlabel('Epoch')\n", "ax1.set_ylabel('Loss')\n", "ax1.legend()\n", "ax1.grid(True)\n", "\n", "# Accuracy curves\n", "ax2.plot(train_accs, label='Training Accuracy', color='blue')\n", "ax2.plot(val_accs, label='Validation Accuracy', color='red')\n", "ax2.set_title('Training and Validation Accuracy')\n", "ax2.set_xlabel('Epoch')\n", "ax2.set_ylabel('Accuracy (%)')\n", "ax2.legend()\n", "ax2.grid(True)\n", "\n", "# Class distribution comparison\n", "sites = ['RES (Train)', 'RCPS (Test)']\n", "pile_counts = [np.sum(train_labels), np.sum(test_labels)]\n", "non_pile_counts = [len(train_labels) - np.sum(train_labels), len(test_labels) - np.sum(test_labels)]\n", "\n", "x = np.arange(len(sites))\n", "width = 0.35\n", "\n", "ax3.bar(x - width/2, pile_counts, width, label='Pile', color='orange')\n", "ax3.bar(x + width/2, non_pile_counts, width, label='Non-Pile', color='skyblue')\n", "ax3.set_title('Class Distribution by Site')\n", "ax3.set_xlabel('Site')\n", "ax3.set_ylabel('Number of Samples')\n", "ax3.set_xticks(x)\n", "ax3.set_xticklabels(sites)\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Performance comparison (placeholder for classical ML comparison)\n", "methods = ['Classical ML\\n(Local)', 'PointNet++\\n(Cross-Site)']\n", "accuracies = [100.0, test_acc]  # Classical ML achieved 100% on both sites\n", "f1_scores = [1.0, test_f1]  # Classical ML achieved perfect F1\n", "\n", "x = np.arange(len(methods))\n", "ax4.bar(x - 0.2, accuracies, 0.4, label='Accuracy (%)', color='lightgreen')\n", "ax4.bar(x + 0.2, [f*100 for f in f1_scores], 0.4, label='F1-Score (%)', color='lightcoral')\n", "ax4.set_title('Performance Comparison')\n", "ax4.set_xlabel('Method')\n", "ax4.set_ylabel('Performance (%)')\n", "ax4.set_xticks(x)\n", "ax4.set_xticklabels(methods)\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "ax4.set_ylim(0, 105)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary statistics\n", "print(\"\\n=== FINAL RESULTS SUMMARY ===\")\n", "print(f\"Training Site: Nortan RES ({len(train_labels)} samples)\")\n", "print(f\"Test Site: Althea RCPS ({len(test_labels)} samples)\")\n", "print(f\"Best Validation Accuracy: {best_val_acc:.2f}%\")\n", "print(f\"Cross-Site Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"Cross-Site Test F1-Score: {test_f1:.4f}\")\n", "print(f\"\\nComparison with Classical ML:\")\n", "print(f\"  Classical ML (local): 100.0% accuracy\")\n", "print(f\"  PointNet++ (cross-site): {test_acc:.2f}% accuracy\")\n", "print(f\"  Generalization gap: {100.0 - test_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "save_results"}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "save_final_results", "outputId": "b37debf8-1321-4e7c-e8fa-ab068359a5ab"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Results saved:\n", "  Training history: pointnet_training_history_20250809_133209.json\n", "  Predictions: pointnet_rcps_predictions_20250809_133209.csv\n", "  Model: pointnet_best_model.pth\n", "\n", "=== POINTNET++ CROSS-SITE TRAINING COMPLETE ===\n", "Successfully trained on RES data and tested on RCPS data\n", "Cross-site generalization accuracy: 100.00%\n"]}], "source": ["# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size\n", "}\n", "\n", "with open(f'{models_path}/pointnet_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/pointnet_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"Results saved:\")\n", "print(f\"  Training history: pointnet_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: pointnet_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: pointnet_best_model.pth\")\n", "\n", "print(\"\\n=== POINTNET++ CROSS-SITE TRAINING COMPLETE ===\")\n", "print(f\"Successfully trained on RES data and tested on RCPS data\")\n", "print(f\"Cross-site generalization accuracy: {test_acc:.2f}%\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}