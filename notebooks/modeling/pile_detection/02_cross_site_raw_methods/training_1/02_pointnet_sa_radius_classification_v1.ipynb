{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "MHh2gLbSsAOq", "outputId": "c5c85a2b-d410-4a22-ee97-37803c732aeb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n", "Using device: cuda\n", "Enhanced Configuration:\n", "  batch_size: 16\n", "  num_epochs: 100\n", "  learning_rate: 0.001\n", "  num_points: 512\n", "  patience: 20\n", "  device: cuda\n", "  lr_scheduler: cosine\n", "  weight_decay: 0.0001\n", "  dropout: 0.5\n", "  gradient_clip: 1.0\n", "  augmentation: True\n", "  use_amp: True\n", "POINTNET++ PILE DETECTION - ITERATION 2\n", "==================================================\n", "ENHANCEMENTS:\n", "- Proper Set Abstraction layers with ball query\n", "- Data augmentation (rotation, scaling, jittering)\n", "- Learning rate scheduling (Cosine Annealing)\n", "- Gradient clipping for stability\n", "- Enhanced optimizer (AdamW) with weight decay\n", "- Deeper classification head (4 layers)\n", "==================================================\n", "train: (1287, 1024, 3), labels: 1287\n", "val: (418, 1024, 3), labels: 418\n", "test: (418, 1024, 3), labels: 418\n", "Enhanced preprocessing of 1287 patches...\n", "Enhanced preprocessing complete: 1287 patches\n", "Enhanced preprocessing of 418 patches...\n", "Enhanced preprocessing complete: 418 patches\n", "Enhanced preprocessing of 418 patches...\n", "Enhanced preprocessing complete: 418 patches\n", "Enhanced model parameters: 1,481,346\n", "Starting enhanced training for 100 epochs...\n", "Epoch 10/100: Train Acc=0.5461, Val Acc=0.5311, LR=0.000980\n", "Epoch 20/100: Train Acc=0.5000, Val Acc=0.4139, LR=0.000914\n", "Early stopping at epoch 23\n", "Enhanced training completed in 298.8s\n", "Best validation accuracy: 0.6890\n", "\n", "ITERATION 2 RESULTS:\n", "Test Accuracy: 0.6890\n", "Test F1-Score: 0.8153\n", "Test Precision: 0.6882\n", "Test Recall: 1.0000\n", "\n", "COMPARISON WITH ITERATION 1:\n", "F1-Score:  0.8165 → 0.8153 (-0.1%)\n", "Accuracy:  0.6914 → 0.6890 (-0.3%)\n", "Results saved to: /content/drive/MyDrive/pointnet_pile_detection/results_iter2/iteration2_results.json\n", "\n", "Iteration 2 Complete!\n", "Generating comparison plots...\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1200x1000 with 4 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Comparison plot saved to: /content/drive/MyDrive/pointnet_pile_detection/results_iter2/iteration_comparison.png\n", "\n", "Evolution demonstrated successfully!\n"]}], "source": ["# PointNet++ <PERSON><PERSON> Detection - Iteration 2\n", "# Enhanced Implementation with Architectural Improvements\n", "# Author: [Your Name]\n", "# Date: [Date]\n", "\n", "# =============================================================================\n", "# 🔁 ITERATION 2 IMPROVEMENTS\n", "# =============================================================================\n", "\"\"\"\n", "### Key Enhancements from Iteration 1:\n", "\n", "1. **Architecture Upgrade**:\n", "   - Replaced simplified k-NN grouping with proper Set Abstraction layers\n", "   - Added ball query for radius-based local neighborhoods\n", "   - Implemented standard PointNet++ components\n", "\n", "2. **Training Improvements**:\n", "   - Learning rate scheduling (Cosine Annealing)\n", "   - Gradient clipping for stability\n", "   - Enhanced optimizer (AdamW) with weight decay\n", "   - Data augmentation (rotation, scaling, jittering)\n", "\n", "3. **Model Enhancements**:\n", "   - Deeper classification head (4 layers vs 3)\n", "   - Better regularization with increased dropout\n", "   - Optional focal loss for hard examples\n", "\n", "Expected Performance Gain: 10-25% improvement over Iteration 1\n", "\"\"\"\n", "\n", "# =============================================================================\n", "# SETUP AND IMPORTS\n", "# =============================================================================\n", "\n", "from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "from torch.cuda.amp import GradScaler, autocast\n", "import numpy as np\n", "import pandas as pd\n", "import pickle\n", "import os\n", "from pathlib import Path\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, classification_report\n", "import matplotlib.pyplot as plt\n", "import json\n", "import warnings\n", "import time\n", "warnings.filterwarnings('ignore')\n", "\n", "# =============================================================================\n", "# CONFIGURATION\n", "# =============================================================================\n", "\n", "# Google Drive paths (same data source as Iteration 1)\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "\n", "project_path = Path(GDRIVE_BASE) / PROJECT_FOLDER\n", "data_path = project_path / \"pointnet_data\"  # Same data as Iteration 1\n", "results_path = project_path / \"results_iter2\"\n", "models_path = project_path / \"models_iter2\"\n", "\n", "# Create directories\n", "for path in [results_path, models_path]:\n", "    path.mkdir(parents=True, exist_ok=True)\n", "\n", "# Device configuration\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Enhanced training configuration\n", "config = {\n", "    # Base parameters (optimized from Iteration 1)\n", "    'batch_size': 16 if device.type == 'cuda' else 8,\n", "    'num_epochs': 100,\n", "    'learning_rate': 0.001,\n", "    'num_points': 512,\n", "    'patience': 20,\n", "    'device': device,\n", "\n", "    # NEW: Enhanced parameters\n", "    'lr_scheduler': 'cosine',\n", "    'weight_decay': 1e-4,\n", "    'dropout': 0.5,\n", "    'gradient_clip': 1.0,\n", "    'augmentation': True,\n", "    'use_amp': device.type == 'cuda'\n", "}\n", "\n", "print(\"Enhanced Configuration:\")\n", "for key, value in config.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# =============================================================================\n", "# DATA LOADING (Same as Iteration 1 + Augmentation)\n", "# =============================================================================\n", "\n", "def load_pile_data(data_path):\n", "    \"\"\"Load pile detection data from pickle files - Same as Iteration 1\"\"\"\n", "    datasets = {}\n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl',\n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "\n", "    for split, filename in file_mapping.items():\n", "        filepath = data_path / filename\n", "\n", "        if not filepath.exists():\n", "            print(f\"ERROR: {filepath} not found!\")\n", "            return None\n", "\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "\n", "        patches = data['points']\n", "        labels = data['labels']\n", "\n", "        if patches.shape[2] > 3:\n", "            patches = patches[:, :, :3]\n", "\n", "        patches = patches.astype(np.float32)\n", "        labels = np.array(labels)\n", "\n", "        print(f\"{split}: {patches.shape}, labels: {len(labels)}\")\n", "\n", "        datasets[split] = {\n", "            'patches': patches,\n", "            'labels': labels,\n", "            'metadata': data.get('metadata', [])\n", "        }\n", "\n", "    return datasets\n", "\n", "def augment_point_cloud(points, augment_prob=0.7):\n", "    \"\"\"NEW: Data augmentation for point clouds\"\"\"\n", "    if np.random.random() > augment_prob:\n", "        return points\n", "\n", "    # Random rotation around Z-axis\n", "    angle = np.random.uniform(0, 2 * np.pi)\n", "    cos_angle, sin_angle = np.cos(angle), np.sin(angle)\n", "    rotation_matrix = np.array([\n", "        [cos_angle, -sin_angle, 0],\n", "        [sin_angle, cos_angle, 0],\n", "        [0, 0, 1]\n", "    ])\n", "    points = points @ rotation_matrix.T\n", "\n", "    # Random scaling\n", "    scale = np.random.uniform(0.9, 1.1)\n", "    points = points * scale\n", "\n", "    # Random jittering\n", "    noise = np.random.normal(0, 0.01, points.shape)\n", "    points = points + noise\n", "\n", "    return points\n", "\n", "def preprocess_patches_enhanced(patches, labels, num_points):\n", "    \"\"\"Enhanced preprocessing with augmentation\"\"\"\n", "    processed_patches = []\n", "    processed_labels = []\n", "\n", "    print(f\"Enhanced preprocessing of {len(patches)} patches...\")\n", "\n", "    for i, (patch, label) in enumerate(zip(patches, labels)):\n", "        # Remove zero-padded points\n", "        valid_mask = np.abs(patch).sum(axis=1) > 1e-6\n", "        valid_points = patch[valid_mask]\n", "\n", "        if len(valid_points) < 10:\n", "            continue\n", "\n", "        # Sample to fixed number of points\n", "        if len(valid_points) >= num_points:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=False)\n", "            sampled = valid_points[indices]\n", "        else:\n", "            indices = np.random.choice(len(valid_points), num_points, replace=True)\n", "            sampled = valid_points[indices]\n", "            noise = np.random.normal(0, 0.01, sampled.shape)\n", "            sampled = sampled + noise\n", "\n", "        # NEW: Apply augmentation\n", "        if config['augmentation']:\n", "            sampled = augment_point_cloud(sampled, augment_prob=0.7)\n", "\n", "        # Center and normalize\n", "        centroid = np.mean(sampled, axis=0)\n", "        sampled = sampled - centroid\n", "\n", "        max_dist = np.max(np.linalg.norm(sampled, axis=1))\n", "        if max_dist > 1e-6:\n", "            sampled = sampled / max_dist\n", "\n", "        processed_patches.append(sampled)\n", "        processed_labels.append(label)\n", "\n", "    print(f\"Enhanced preprocessing complete: {len(processed_patches)} patches\")\n", "    return np.array(processed_patches), np.array(processed_labels)\n", "\n", "# =============================================================================\n", "# ENHANCED POINTNET++ ARCHITECTURE\n", "# =============================================================================\n", "\n", "def square_distance(src, dst):\n", "    \"\"\"Calculate squared distance between points\"\"\"\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    \"\"\"Farthest point sampling\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "\n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "\n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    \"\"\"NEW: Ball query for radius-based local neighborhoods\"\"\"\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx\n", "\n", "class PointNetSetAbstraction(nn.Module):\n", "    \"\"\"NEW: Standard PointNet++ Set Abstraction Layer\"\"\"\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        self.group_all = group_all\n", "\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "\n", "    def forward(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "\n", "        if self.group_all:\n", "            new_xyz = torch.zeros(B, 1, C).to(xyz.device)\n", "            new_points = points.view(B, 1, N, -1)\n", "        else:\n", "            fps_idx = farthest_point_sample(xyz, self.npoint)\n", "            new_xyz = xyz[torch.arange(B)[:, None], fps_idx]\n", "            idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "            grouped_xyz = xyz[torch.arange(B)[:, None, None], idx]\n", "            grouped_xyz_norm = grouped_xyz - new_xyz.view(B, self.npoint, 1, C)\n", "\n", "            if points is not None:\n", "                grouped_points = points[torch.arange(B)[:, None, None], idx]\n", "                new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "            else:\n", "                new_points = grouped_xyz_norm\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = torch.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_points = new_points.permute(0, 2, 1)\n", "\n", "        return new_xyz, new_points\n", "\n", "class EnhancedPointNetPlusPlus(nn.Module):\n", "    \"\"\"ENHANCED: PointNet++ with proper Set Abstraction layers\"\"\"\n", "\n", "    def __init__(self, num_classes=2):\n", "        super(EnhancedPointNetPlusPlus, self).__init__()\n", "\n", "        # NEW: Proper PointNet++ Set Abstraction layers\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 32, 3, [64, 64, 128], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 64, 128 + 3, [128, 128, 256], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 256, [256, 512, 1024], True)\n", "\n", "        # ENHANCED: Deeper classification head\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(config['dropout'])\n", "\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(config['dropout'])\n", "\n", "        self.fc3 = nn.Linear(256, 64)\n", "        self.bn3 = nn.<PERSON><PERSON><PERSON>orm1d(64)\n", "        self.drop3 = nn.Dropout(config['dropout'] * 0.6)\n", "\n", "        self.fc4 = nn.Linear(64, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        # Handle input shape\n", "        if len(xyz.shape) == 4:\n", "            xyz = xyz.squeeze(1)\n", "        if xyz.shape[-1] != 3:\n", "            xyz = xyz[:, :, :3]\n", "\n", "        B, N, C = xyz.shape\n", "\n", "        # NEW: Proper Set Abstraction layers\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        # Global feature vector\n", "        global_feat = l3_points.view(B, -1)\n", "\n", "        # ENHANCED: Deeper classification\n", "        x = self.drop1(torch.relu(self.bn1(self.fc1(global_feat))))\n", "        x = self.drop2(torch.relu(self.bn2(self.fc2(x))))\n", "        x = self.drop3(torch.relu(self.bn3(self.fc3(x))))\n", "        x = self.fc4(x)\n", "\n", "        return x\n", "\n", "# =============================================================================\n", "# DATASET CLASS (Same as Iteration 1)\n", "# =============================================================================\n", "\n", "class PileDataset(Dataset):\n", "    def __init__(self, points, labels):\n", "        self.points = torch.FloatTensor(points)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.points)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.points[idx], self.labels[idx]\n", "\n", "# =============================================================================\n", "# ENHANCED TRAINING FUNCTIONS\n", "# =============================================================================\n", "\n", "def setup_enhanced_training(model, train_labels):\n", "    \"\"\"NEW: Enhanced training setup\"\"\"\n", "\n", "    # Class balancing (same as Iteration 1)\n", "    pos_weight = len(train_labels) / (2 * np.sum(train_labels))\n", "    neg_weight = len(train_labels) / (2 * (len(train_labels) - np.sum(train_labels)))\n", "    class_weights = torch.FloatTensor([neg_weight, pos_weight]).to(device)\n", "    criterion = nn.CrossEntropyLoss(weight=class_weights)\n", "\n", "    # NEW: Enhanced optimizer\n", "    optimizer = optim.AdamW(\n", "        model.parameters(),\n", "        lr=config['learning_rate'],\n", "        weight_decay=config['weight_decay']\n", "    )\n", "\n", "    # NEW: Learning rate scheduler\n", "    if config['lr_scheduler'] == 'cosine':\n", "        scheduler = optim.lr_scheduler.CosineAnnealingLR(\n", "            optimizer, T_max=config['num_epochs'], eta_min=1e-6\n", "        )\n", "    else:\n", "        scheduler = None\n", "\n", "    return criterion, optimizer, scheduler\n", "\n", "def train_epoch_enhanced(model, loader, criterion, optimizer, device, scaler=None):\n", "    \"\"\"NEW: Enhanced training with gradient clipping\"\"\"\n", "    model.train()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(loader):\n", "        data, target = data.to(device), target.to(device)\n", "        optimizer.zero_grad()\n", "\n", "        if scaler is not None:\n", "            with autocast():\n", "                output = model(data)\n", "                loss = criterion(output, target)\n", "            scaler.scale(loss).backward()\n", "            # NEW: Gradient clipping\n", "            scaler.unscale_(optimizer)\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "        else:\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "            loss.backward()\n", "            # NEW: Gradient clipping\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=config['gradient_clip'])\n", "            optimizer.step()\n", "\n", "        total_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        correct += pred.eq(target).sum().item()\n", "        total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "def validate_epoch_enhanced(model, loader, criterion, device):\n", "    \"\"\"Enhanced validation\"\"\"\n", "    model.eval()\n", "    total_loss = 0\n", "    correct = 0\n", "    total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in loader:\n", "            data, target = data.to(device), target.to(device)\n", "\n", "            if config['use_amp']:\n", "                with autocast():\n", "                    output = model(data)\n", "                    loss = criterion(output, target)\n", "            else:\n", "                output = model(data)\n", "                loss = criterion(output, target)\n", "\n", "            total_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            correct += pred.eq(target).sum().item()\n", "            total += target.size(0)\n", "\n", "    return total_loss / len(loader), correct / total\n", "\n", "# =============================================================================\n", "# MAIN ENHANCED TRAINING FUNCTION\n", "# =============================================================================\n", "\n", "def train_pointnet_enhanced():\n", "    \"\"\"Main enhanced training function\"\"\"\n", "\n", "    print(\"POINTNET++ PILE DETECTION - ITERATION 2\")\n", "    print(\"=\"*50)\n", "    print(\"ENHANCEMENTS:\")\n", "    print(\"- Proper Set Abstraction layers with ball query\")\n", "    print(\"- Data augmentation (rotation, scaling, jittering)\")\n", "    print(\"- Learning rate scheduling (Cosine Annealing)\")\n", "    print(\"- Gradient clipping for stability\")\n", "    print(\"- Enhanced optimizer (AdamW) with weight decay\")\n", "    print(\"- Deeper classification head (4 layers)\")\n", "    print(\"=\"*50)\n", "\n", "    # Load data (same as Iteration 1)\n", "    datasets = load_pile_data(data_path)\n", "    if not datasets:\n", "        print(\"ERROR: No data found!\")\n", "        return None, None\n", "\n", "    # Enhanced preprocessing\n", "    train_patches, train_labels = preprocess_patches_enhanced(\n", "        datasets['train']['patches'], datasets['train']['labels'], config['num_points']\n", "    )\n", "    val_patches, val_labels = preprocess_patches_enhanced(\n", "        datasets['val']['patches'], datasets['val']['labels'], config['num_points']\n", "    )\n", "    test_patches, test_labels = preprocess_patches_enhanced(\n", "        datasets['test']['patches'], datasets['test']['labels'], config['num_points']\n", "    )\n", "\n", "    # Create datasets and loaders\n", "    train_dataset = PileDataset(train_patches, train_labels)\n", "    val_dataset = PileDataset(val_patches, val_labels)\n", "    test_dataset = PileDataset(test_patches, test_labels)\n", "\n", "    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True, drop_last=True)\n", "    val_loader = DataLoader(val_dataset, batch_size=config['batch_size'], shuffle=False)\n", "    test_loader = DataLoader(test_dataset, batch_size=config['batch_size'], shuffle=False)\n", "\n", "    # Initialize enhanced model\n", "    model = EnhancedPointNetPlusPlus(num_classes=2).to(device)\n", "\n", "    # Enhanced training setup\n", "    criterion, optimizer, scheduler = setup_enhanced_training(model, train_labels)\n", "    scaler = GradScaler() if config['use_amp'] else None\n", "\n", "    # Model info\n", "    param_count = sum(p.numel() for p in model.parameters())\n", "    print(f\"Enhanced model parameters: {param_count:,}\")\n", "\n", "    # Enhanced training loop\n", "    print(f\"Starting enhanced training for {config['num_epochs']} epochs...\")\n", "    start_time = time.time()\n", "\n", "    train_losses = []\n", "    val_losses = []\n", "    train_accs = []\n", "    val_accs = []\n", "    learning_rates = []\n", "    best_val_acc = 0\n", "    patience_counter = 0\n", "\n", "    for epoch in range(config['num_epochs']):\n", "        # Training\n", "        train_loss, train_acc = train_epoch_enhanced(model, train_loader, criterion, optimizer, device, scaler)\n", "\n", "        # Validation\n", "        val_loss, val_acc = validate_epoch_enhanced(model, val_loader, criterion, device)\n", "\n", "        # Learning rate scheduling\n", "        current_lr = optimizer.param_groups[0]['lr']\n", "        learning_rates.append(current_lr)\n", "\n", "        if scheduler is not None:\n", "            scheduler.step()\n", "\n", "        # Store metrics\n", "        train_losses.append(train_loss)\n", "        val_losses.append(val_loss)\n", "        train_accs.append(train_acc)\n", "        val_accs.append(val_acc)\n", "\n", "        if (epoch + 1) % 10 == 0:\n", "            print(f\"Epoch {epoch+1}/{config['num_epochs']}: Train Acc={train_acc:.4f}, Val Acc={val_acc:.4f}, LR={current_lr:.6f}\")\n", "\n", "        # Save best model\n", "        if val_acc > best_val_acc:\n", "            best_val_acc = val_acc\n", "            patience_counter = 0\n", "            torch.save({\n", "                'model_state_dict': model.state_dict(),\n", "                'optimizer_state_dict': optimizer.state_dict(),\n", "                'scheduler_state_dict': scheduler.state_dict() if scheduler else None,\n", "                'epoch': epoch,\n", "                'val_acc': val_acc,\n", "                'config': config\n", "            }, models_path / 'best_pointnet_iter2.pth')\n", "        else:\n", "            patience_counter += 1\n", "            if patience_counter >= config['patience']:\n", "                print(f\"Early stopping at epoch {epoch+1}\")\n", "                break\n", "\n", "    training_time = time.time() - start_time\n", "    print(f\"Enhanced training completed in {training_time:.1f}s\")\n", "    print(f\"Best validation accuracy: {best_val_acc:.4f}\")\n", "\n", "    # Load best model for evaluation\n", "    checkpoint = torch.load(models_path / 'best_pointnet_iter2.pth')\n", "    model.load_state_dict(checkpoint['model_state_dict'])\n", "\n", "    # Enhanced evaluation\n", "    model.eval()\n", "    all_preds = []\n", "    all_targets = []\n", "    all_probs = []\n", "\n", "    with torch.no_grad():\n", "        for data, target in test_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            probs = torch.softmax(output, dim=1)\n", "\n", "            all_preds.extend(output.argmax(dim=1).cpu().numpy())\n", "            all_targets.extend(target.cpu().numpy())\n", "            all_probs.extend(probs[:, 1].cpu().numpy())\n", "\n", "    # Calculate comprehensive metrics\n", "    test_accuracy = accuracy_score(all_targets, all_preds)\n", "    test_f1 = f1_score(all_targets, all_preds, average='binary')\n", "    test_precision = precision_score(all_targets, all_preds, average='binary')\n", "    test_recall = recall_score(all_targets, all_preds, average='binary')\n", "\n", "    print(\"\\nITERATION 2 RESULTS:\")\n", "    print(f\"Test Accuracy: {test_accuracy:.4f}\")\n", "    print(f\"Test F1-Score: {test_f1:.4f}\")\n", "    print(f\"Test Precision: {test_precision:.4f}\")\n", "    print(f\"Test Recall: {test_recall:.4f}\")\n", "\n", "    # Load Iteration 1 results for comparison\n", "    iter1_results_path = project_path / \"results_iter1\" / \"iteration1_results.json\"\n", "    if iter1_results_path.exists():\n", "        with open(iter1_results_path, 'r') as f:\n", "            iter1_results = json.load(f)\n", "\n", "        iter1_f1 = iter1_results['test_metrics']['f1_score']\n", "        iter1_acc = iter1_results['test_metrics']['accuracy']\n", "\n", "        f1_improvement = ((test_f1 - iter1_f1) / iter1_f1) * 100\n", "        acc_improvement = ((test_accuracy - iter1_acc) / iter1_acc) * 100\n", "\n", "        print(f\"\\nCOMPARISON WITH ITERATION 1:\")\n", "        print(f\"F1-Score:  {iter1_f1:.4f} → {test_f1:.4f} ({f1_improvement:+.1f}%)\")\n", "        print(f\"Accuracy:  {iter1_acc:.4f} → {test_accuracy:.4f} ({acc_improvement:+.1f}%)\")\n", "\n", "    # Save results\n", "    results = {\n", "        'iteration': 2,\n", "        'model': 'Enhanced PointNet++',\n", "        'architecture': 'Standard Set Abstraction layers with ball query',\n", "        'enhancements': [\n", "            'Proper Set Abstraction layers',\n", "            'Data augmentation',\n", "            'Learning rate scheduling',\n", "            'Gradient clipping',\n", "            'Enhanced optimizer (AdamW)',\n", "            'Deeper classification head'\n", "        ],\n", "        'training_time': training_time,\n", "        'best_val_acc': best_val_acc,\n", "        'test_metrics': {\n", "            'accuracy': test_accuracy,\n", "            'f1_score': test_f1,\n", "            'precision': test_precision,\n", "            'recall': test_recall\n", "        },\n", "        'config': {k: str(v) if not isinstance(v, (int, float, bool, str)) else v\n", "                  for k, v in config.items()},\n", "        'training_history': {\n", "            'train_losses': train_losses,\n", "            'val_losses': val_losses,\n", "            'train_accs': train_accs,\n", "            'val_accs': val_accs,\n", "            'learning_rates': learning_rates\n", "        }\n", "    }\n", "\n", "    with open(results_path / 'iteration2_results.json', 'w') as f:\n", "        json.dump(results, f, indent=2, default=str)\n", "\n", "    print(f\"Results saved to: {results_path / 'iteration2_results.json'}\")\n", "\n", "    return model, results\n", "\n", "# =============================================================================\n", "# COMPARISON AND VISUALIZATION\n", "# =============================================================================\n", "\n", "def plot_comparison():\n", "    \"\"\"Plot comparison between iterations\"\"\"\n", "\n", "    # Load results from both iterations\n", "    iter1_path = project_path / \"results_iter1\" / \"iteration1_results.json\"\n", "    iter2_path = results_path / \"iteration2_results.json\"\n", "\n", "    if iter1_path.exists() and iter2_path.exists():\n", "        with open(iter1_path, 'r') as f:\n", "            iter1 = json.load(f)\n", "        with open(iter2_path, 'r') as f:\n", "            iter2 = json.load(f)\n", "\n", "        # Create comparison plot\n", "        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))\n", "\n", "        # Training accuracy comparison\n", "        ax1.plot(iter1['training_history']['train_accs'], label='Iteration 1', alpha=0.7)\n", "        ax1.plot(iter2['training_history']['train_accs'], label='Iteration 2', alpha=0.7)\n", "        ax1.set_title('Training Accuracy')\n", "        ax1.set_xlabel('Epoch')\n", "        ax1.set_ylabel('Accuracy')\n", "        ax1.legend()\n", "        ax1.grid(True, alpha=0.3)\n", "\n", "        # Validation accuracy comparison\n", "        ax2.plot(iter1['training_history']['val_accs'], label='Iteration 1', alpha=0.7)\n", "        ax2.plot(iter2['training_history']['val_accs'], label='Iteration 2', alpha=0.7)\n", "        ax2.set_title('Validation Accuracy')\n", "        ax2.set_xlabel('Epoch')\n", "        ax2.set_ylabel('Accuracy')\n", "        ax2.legend()\n", "        ax2.grid(True, alpha=0.3)\n", "\n", "        # Learning rate (Iteration 2 only)\n", "        if 'learning_rates' in iter2['training_history']:\n", "            ax3.plot(iter2['training_history']['learning_rates'])\n", "            ax3.set_title('Learning Rate Schedule (Iteration 2)')\n", "            ax3.set_xlabel('Epoch')\n", "            ax3.set_ylabel('Learning Rate')\n", "            ax3.grid(True, alpha=0.3)\n", "\n", "        # Test metrics comparison\n", "        metrics = ['accuracy', 'f1_score', 'precision', 'recall']\n", "        iter1_values = [iter1['test_metrics'][m] for m in metrics]\n", "        iter2_values = [iter2['test_metrics'][m] for m in metrics]\n", "\n", "        x = np.arange(len(metrics))\n", "        width = 0.35\n", "\n", "        ax4.bar(x - width/2, iter1_values, width, label='Iteration 1', alpha=0.7)\n", "        ax4.bar(x + width/2, iter2_values, width, label='Iteration 2', alpha=0.7)\n", "        ax4.set_title('Test Metrics Comparison')\n", "        ax4.set_ylabel('Score')\n", "        ax4.set_xticks(x)\n", "        ax4.set_xticklabels([m.replace('_', ' ').title() for m in metrics])\n", "        ax4.legend()\n", "        ax4.grid(True, alpha=0.3)\n", "\n", "        plt.tight_layout()\n", "        plt.savefig(results_path / 'iteration_comparison.png', dpi=300, bbox_inches='tight')\n", "        plt.show()\n", "\n", "        print(f\"Comparison plot saved to: {results_path / 'iteration_comparison.png'}\")\n", "\n", "# =============================================================================\n", "# EXECUTION\n", "# =============================================================================\n", "\n", "if __name__ == \"__main__\":\n", "    model, results = train_pointnet_enhanced()\n", "\n", "    if model is not None:\n", "        print(\"\\nIteration 2 Complete!\")\n", "        print(\"Generating comparison plots...\")\n", "        plot_comparison()\n", "        print(\"\\nEvolution demonstrated successfully!\")"]}]}