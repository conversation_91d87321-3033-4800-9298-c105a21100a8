{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# DGCNN Fair Comparison with Classical ML (RES→RCPS)\n", "\n", "This notebook implements DGCNN using **EXACTLY the same data pipeline** as Classical ML:\n", "- **Same point clouds**: `Block_11_2m.las` and `Point_Cloud.las`\n", "- **Same ground truth**: `Buffer_2m.kml` files (not CSV results)\n", "- **Same validation protocol**: Train on RES → Test on RCPS\n", "- **Same patch parameters**: 3m radius (like Classical ML)\n", "\n", "**Key Difference from Classical ML:**\n", "- ✅ **Classical ML**: Extracts 22 statistical features from 3D coordinates\n", "- ✅ **DGCNN**: Uses raw 3D coordinates with graph neural networks\n", "- ✅ **Fair comparison**: Both use identical data sources and validation\n", "\n", "**Research Question**: Can graph-based deep learning match classical ML performance using raw coordinates vs engineered features?\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Fair Deep Learning vs Classical ML Comparison\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b5992dd2-2d6b-47b4-a02f-541b7b063c40"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "d6597791-7325-4201-a698-d43f3e66b23e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.42)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.62.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.8)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.3)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m1.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m44.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m99.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m76.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m30.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m18.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m15.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m10.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.62.0-py3-none-any.whl (681 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m681.8/681.8 kB\u001b[0m \u001b[31m58.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m21.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m8.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m17.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m28.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.62.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## Data Loading (EXACT SAME as Classical ML)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N (SAME as Classical ML)\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "\n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "\n", "    pile_locations = np.array(pile_coords)\n", "\n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "\n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "\n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "\n", "    return pile_locations_utm\n", "\n", "def subsample_patch(patch_points, target_size=256):\n", "    \"\"\"Subsample patch to target size (Classical ML uses 64, DGCNN uses 256)\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        # Upsample with noise if needed\n", "        if len(patch_points) < target_size:\n", "            extra_needed = target_size - len(patch_points)\n", "            extra_indices = np.random.choice(len(patch_points), extra_needed, replace=True)\n", "            extra_points = patch_points[extra_indices] + np.random.normal(0, 0.005, (extra_needed, 3))\n", "            return np.vstack([patch_points, extra_points])\n", "        return patch_points\n", "\n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=3.0, min_points=20, target_size=256):\n", "    \"\"\"Extract patches around locations (SAME as Classical ML but optimized for DGCNN)\"\"\"\n", "    print(f\"Extracting patches (radius={radius}m, min_points={min_points}, target_size={target_size})\")\n", "\n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    original_sizes = []\n", "\n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            original_sizes.append(len(patch_points))\n", "\n", "            # Subsample to target size\n", "            patch_points = subsample_patch(patch_points, target_size)\n", "\n", "            # Center patch (SAME as Classical ML)\n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "\n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "\n", "    print(f\"Extracted {len(patches)} valid patches\")\n", "    if original_sizes:\n", "        print(f\"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}\")\n", "        final_sizes = [len(p) for p in patches]\n", "        print(f\"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}\")\n", "\n", "    return patches, np.array(valid_locs)\n", "\n", "def create_negative_samples(points, pile_locations, n_negative, radius=3.0, min_points=20, target_size=256):\n", "    \"\"\"Create negative samples (SAME as Classical ML)\"\"\"\n", "    print(f\"Creating {n_negative} negative samples...\")\n", "\n", "    np.random.seed(42)\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    # Generate random locations (avoiding pile areas)\n", "    random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative * 3)  # Generate extra\n", "    random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative * 3)\n", "    random_locations = np.column_stack([random_x, random_y])\n", "\n", "    # Filter out locations too close to piles\n", "    pile_tree = cKDTree(pile_locations)\n", "    valid_negatives = []\n", "\n", "    for loc in random_locations:\n", "        distances, _ = pile_tree.query(loc, k=1)\n", "        if distances > radius * 2:  # At least 2x radius away from any pile\n", "            valid_negatives.append(loc)\n", "            if len(valid_negatives) >= n_negative:\n", "                break\n", "\n", "    valid_negatives = np.array(valid_negatives[:n_negative])\n", "\n", "    # Extract patches\n", "    neg_patches, _ = extract_patches(points, valid_negatives, radius, min_points, target_size)\n", "\n", "    return neg_patches"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_main", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "18df8764-ff7d-4c23-9bbf-0f9569023a09"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== LOADING RES DATA (TRAINING SITE) - SAME AS CLASSICAL ML ===\n", "Loading RES point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "Loaded 35,565,352 points\n", "Loading RES pile locations: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Buffer_2m.kml\n", "Loaded 368 pile locations\n", "Extracting positive patches from RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=256)\n", "Extracted 368 valid patches\n", "Original sizes: min=86195, max=152283, mean=96645.0\n", "Final sizes: min=256, max=256, mean=256.0\n", "Extracted 368 positive patches\n", "Creating 368 negative samples...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=256)\n", "Extracted 0 valid patches\n", "Created 0 negative patches\n", "\n", "RES training dataset: 368 samples (368 positive, 0 negative)\n", "\n", "=== LOADING RCPS DATA (TEST SITE) - SAME AS CLASSICAL ML ===\n", "Loading RCPS point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "Loading RCPS pile locations: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Buffer_2m.kml\n", "Loaded 1359 pile locations\n", "Extracting positive patches from RCPS...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=256)\n", "Extracted 1359 valid patches\n", "Original sizes: min=35562, max=67519, mean=38898.0\n", "Final sizes: min=256, max=256, mean=256.0\n", "Extracted 1359 positive patches\n", "Creating 1359 negative samples...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=256)\n", "Extracted 0 valid patches\n", "Created 0 negative patches\n", "\n", "RCPS test dataset: 1359 samples (1359 positive, 0 negative)\n", "\n", "=== DATA SUMMARY (FAIR COMPARISON) ===\n", "✅ SAME point clouds as Classical ML\n", "✅ SAME ground truth (Buffer_2m.kml) as Classical ML\n", "✅ SAME patch radius (3m) as Classical ML\n", "✅ SAME validation protocol (RES→RCPS) as Classical ML\n", "📊 Classical ML: 22 statistical features from 64 points\n", "📊 DGCNN: Raw 3D coordinates with graph networks from 256 points\n", "🎯 Research Question: Graph neural networks vs engineered features\n"]}], "source": ["# STEP 1: LOAD RES DATA (TRAINING SITE) - EXACT SAME as Classical ML\n", "print(\"=== LOADING RES DATA (TRAINING SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RES point cloud\n", "print(f\"Loading RES point cloud: {data_path}/nortan_res/Block_11_2m.las\")\n", "res_las = laspy.read(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"Loaded {len(res_points):,} points\")\n", "\n", "# Load RES pile locations (SAME file as Classical ML)\n", "print(f\"Loading RES pile locations: {data_path}/nortan_res/Buffer_2m.kml\")\n", "res_pile_locations = load_and_reproject_kml(f\"{data_path}/nortan_res/Buffer_2m.kml\")\n", "print(f\"Loaded {len(res_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius, but 256 points for DGCNN)\n", "print(f\"Extracting positive patches from RES...\")\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations, radius=3.0, min_points=20, target_size=256)\n", "print(f\"Extracted {len(res_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "res_neg_patches = create_negative_samples(res_points, res_pile_locations, len(res_pos_patches), radius=3.0, min_points=20, target_size=256)\n", "print(f\"Created {len(res_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRES training dataset: {len(res_pos_patches) + len(res_neg_patches)} samples ({len(res_pos_patches)} positive, {len(res_neg_patches)} negative)\")\n", "\n", "# STEP 2: LOAD RCPS DATA (TEST SITE) - EXACT SAME as Classical ML\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RCPS point cloud\n", "print(f\"Loading RCPS point cloud: {data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_las = laspy.read(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"Loaded {len(rcps_points):,} points\")\n", "\n", "# Load RCPS pile locations (SAME file as Classical ML)\n", "print(f\"Loading RCPS pile locations: {data_path}/althea_rpcs/Buffer_2m.kml\")\n", "rcps_pile_locations = load_and_reproject_kml(f\"{data_path}/althea_rpcs/Buffer_2m.kml\")\n", "print(f\"Loaded {len(rcps_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius, but 256 points for DGCNN)\n", "print(f\"Extracting positive patches from RCPS...\")\n", "rcps_pos_patches, _ = extract_patches(rcps_points, rcps_pile_locations, radius=3.0, min_points=20, target_size=256)\n", "print(f\"Extracted {len(rcps_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "rcps_neg_patches = create_negative_samples(rcps_points, rcps_pile_locations, len(rcps_pos_patches), radius=3.0, min_points=20, target_size=256)\n", "print(f\"Created {len(rcps_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRCPS test dataset: {len(rcps_pos_patches) + len(rcps_neg_patches)} samples ({len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative)\")\n", "\n", "print(\"\\n=== DATA SUMMARY (FAIR COMPARISON) ===\")\n", "print(f\"✅ SAME point clouds as Classical ML\")\n", "print(f\"✅ SAME ground truth (Buffer_2m.kml) as Classical ML\")\n", "print(f\"✅ SAME patch radius (3m) as Classical ML\")\n", "print(f\"✅ SAME validation protocol (RES→RCPS) as Classical ML\")\n", "print(f\"📊 Classical ML: 22 statistical features from 64 points\")\n", "print(f\"📊 DGCNN: Raw 3D coordinates with graph networks from 256 points\")\n", "print(f\"🎯 Research Question: Graph neural networks vs engineered features\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for DGCNN"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_datasets", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b67c1403-18e8-4b77-8f69-5b6750faff34"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Final dataset shapes:\n", "Training: (368, 256, 3), labels: (368,)\n", "Testing: (1359, 256, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n", "\n", "Data integrity checks:\n", "Training patches - min: -1.999, max: 1.998\n", "Test patches - min: -1.998, max: 19.453\n", "No NaN values: True\n"]}], "source": ["# Prepare training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Add positive patches\n", "for patch in res_pos_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in res_neg_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(0)\n", "\n", "# Prepare test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Add positive patches\n", "for patch in rcps_pos_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in rcps_neg_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 256, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 256, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")\n", "\n", "# Verify data integrity\n", "print(f\"\\nData integrity checks:\")\n", "print(f\"Training patches - min: {train_patches.min():.3f}, max: {train_patches.max():.3f}\")\n", "print(f\"Test patches - min: {test_patches.min():.3f}, max: {test_patches.max():.3f}\")\n", "print(f\"No NaN values: {not np.isnan(train_patches).any() and not np.isnan(test_patches).any()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "dgcnn_architecture"}, "source": ["## DGCNN Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dgcnn_model"}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Construct edge features\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "\n", "    if idx is None:\n", "        idx = knn(x, k=k)   # (batch_size, num_points, k)\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "\n", "    idx = idx + idx_base\n", "\n", "    idx = idx.view(-1)\n", "\n", "    _, num_dims, _ = x.size()\n", "\n", "    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "\n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "\n", "    return feature      # (batch_size, 2*num_dims, num_points, k)\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20, dropout=0.5):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.<PERSON>chNorm1d(1024)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "\n", "        # Classification head\n", "        self.linear1 = nn.Linear(1024*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=dropout)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=dropout)\n", "        self.linear3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # EdgeConv layers\n", "        x = get_graph_feature(x, k=self.k)      # (batch_size, 3, num_points) -> (batch_size, 3*2, num_points, k)\n", "        x = self.conv1(x)                       # (batch_size, 3*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x1, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv2(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x2, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv3(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 128, num_points, k)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 128, num_points, k) -> (batch_size, 128, num_points)\n", "\n", "        x = get_graph_feature(x3, k=self.k)     # (batch_size, 128, num_points) -> (batch_size, 128*2, num_points, k)\n", "        x = self.conv4(x)                       # (batch_size, 128*2, num_points, k) -> (batch_size, 256, num_points, k)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 256, num_points, k) -> (batch_size, 256, num_points)\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (batch_size, 64+64+128+256, num_points)\n", "\n", "        x = self.conv5(x)                       # (batch_size, 512, num_points) -> (batch_size, 1024, num_points)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x = torch.cat((x1, x2), 1)              # (batch_size, 1024*2)\n", "\n", "        # Classification\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2) # (batch_size, 1024*2) -> (batch_size, 512)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2) # (batch_size, 512) -> (batch_size, 256)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)                                             # (batch_size, 256) -> (batch_size, num_classes)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "create_dataset_class", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "ad753f13-1669-4760-a1ae-97a83d335c03"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 8\n", "  Input shape: (batch_size, 3, 256) for DGCNN\n"]}], "source": ["class FairComparisonDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # DGCNN expects (batch_size, num_features, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 256)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = FairComparisonDataset(train_patches, train_labels)\n", "test_dataset = FairComparisonDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 8  # Smaller batch size for DGCNN (more memory intensive)\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")\n", "print(f\"  Input shape: (batch_size, 3, 256) for DGCNN\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "setup_training", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "bb8cc415-5a9d-4cc4-e401-cd7945ee3c65"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model initialized with 1,799,810 parameters\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  K-neighbors: 20\n", "  Device: cuda\n", "  Data source: SAME as Classical ML (Buffer_2m.kml)\n", "  Validation: SAME as Classical ML (RES→RCPS)\n"]}], "source": ["# Initialize model\n", "model = DGCNN(num_classes=2, k=20, dropout=0.5).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  K-neighbors: 20\")\n", "print(f\"  Device: {device}\")\n", "print(f\"  Data source: SAME as Classical ML (Buffer_2m.kml)\")\n", "print(f\"  Validation: SAME as Classical ML (RES→RCPS)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_loop", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "0063a69a-b8eb-466c-adbc-ec4b553ae504"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== STARTING DGCNN FAIR COMPARISON TRAINING ===\n", "DGCNN vs Classical ML using IDENTICAL data sources\n", "  Epoch 1/50, <PERSON><PERSON> 0/37, Loss: 0.6246\n", "  Epoch 1/50, <PERSON><PERSON> 10/37, Loss: 0.2612\n", "  Epoch 1/50, <PERSON><PERSON> 20/37, Loss: 0.1166\n", "  Epoch 1/50, <PERSON><PERSON> 30/37, Loss: 0.0426\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.2060, Train Acc: 94.56%, Val Loss: 0.0307, Val Acc: 100.00%, Time: 2.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/37, Loss: 0.0596\n", "  Epoch 2/50, <PERSON><PERSON> 10/37, Loss: 0.0215\n", "  Epoch 2/50, <PERSON><PERSON> 20/37, Loss: 0.0168\n", "  Epoch 2/50, <PERSON><PERSON> 30/37, Loss: 0.0075\n", "Epoch 2/50: Train Loss: 0.0233, Train Acc: 100.00%, Val Loss: 0.0315, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/37, Loss: 0.0155\n", "  Epoch 3/50, <PERSON><PERSON> 10/37, Loss: 0.0083\n", "  Epoch 3/50, <PERSON><PERSON> 20/37, Loss: 0.0066\n", "  Epoch 3/50, <PERSON><PERSON> 30/37, Loss: 0.0099\n", "Epoch 3/50: Train Loss: 0.0113, Train Acc: 100.00%, Val Loss: 0.0137, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/37, Loss: 0.0073\n", "  Epoch 4/50, <PERSON><PERSON> 10/37, Loss: 0.0133\n", "  Epoch 4/50, <PERSON><PERSON> 20/37, Loss: 0.0044\n", "  Epoch 4/50, <PERSON><PERSON> 30/37, Loss: 0.0044\n", "Epoch 4/50: Train Loss: 0.0077, Train Acc: 100.00%, Val Loss: 0.0093, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/37, Loss: 0.0038\n", "  Epoch 5/50, <PERSON><PERSON> 10/37, Loss: 0.0156\n", "  Epoch 5/50, <PERSON><PERSON> 20/37, Loss: 0.0083\n", "  Epoch 5/50, <PERSON><PERSON> 30/37, Loss: 0.0095\n", "Epoch 5/50: Train Loss: 0.0056, Train Acc: 100.00%, Val Loss: 0.0090, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/37, Loss: 0.0057\n", "  Epoch 6/50, <PERSON><PERSON> 10/37, Loss: 0.0030\n", "  Epoch 6/50, <PERSON><PERSON> 20/37, Loss: 0.0049\n", "  Epoch 6/50, <PERSON><PERSON> 30/37, Loss: 0.0036\n", "Epoch 6/50: Train Loss: 0.0045, Train Acc: 100.00%, Val Loss: 0.0078, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/37, Loss: 0.0053\n", "  Epoch 7/50, <PERSON><PERSON> 10/37, Loss: 0.0025\n", "  Epoch 7/50, <PERSON><PERSON> 20/37, Loss: 0.0031\n", "  Epoch 7/50, <PERSON><PERSON> 30/37, Loss: 0.0048\n", "Epoch 7/50: Train Loss: 0.0031, Train Acc: 100.00%, Val Loss: 0.0036, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/37, Loss: 0.0025\n", "  Epoch 8/50, <PERSON><PERSON> 10/37, Loss: 0.0033\n", "  Epoch 8/50, <PERSON><PERSON> 20/37, Loss: 0.0018\n", "  Epoch 8/50, <PERSON><PERSON> 30/37, Loss: 0.0026\n", "Epoch 8/50: Train Loss: 0.0027, Train Acc: 100.00%, Val Loss: 0.0035, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/37, Loss: 0.0027\n", "  Epoch 9/50, <PERSON><PERSON> 10/37, Loss: 0.0033\n", "  Epoch 9/50, <PERSON><PERSON> 20/37, Loss: 0.0011\n", "  Epoch 9/50, <PERSON><PERSON> 30/37, Loss: 0.0011\n", "Epoch 9/50: Train Loss: 0.0019, Train Acc: 100.00%, Val Loss: 0.0029, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/37, Loss: 0.0019\n", "  Epoch 10/50, <PERSON><PERSON> 10/37, Loss: 0.0014\n", "  Epoch 10/50, <PERSON><PERSON> 20/37, Loss: 0.0030\n", "  Epoch 10/50, <PERSON><PERSON> 30/37, Loss: 0.0016\n", "Epoch 10/50: Train Loss: 0.0021, Train Acc: 100.00%, Val Loss: 0.0035, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/37, Loss: 0.0023\n", "  Epoch 11/50, <PERSON><PERSON> 10/37, Loss: 0.0041\n", "  Epoch 11/50, <PERSON><PERSON> 20/37, Loss: 0.0013\n", "  Epoch 11/50, <PERSON><PERSON> 30/37, Loss: 0.0013\n", "Epoch 11/50: Train Loss: 0.0017, Train Acc: 100.00%, Val Loss: 0.0025, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/37, Loss: 0.0015\n", "  Epoch 12/50, <PERSON><PERSON> 10/37, Loss: 0.0007\n", "  Epoch 12/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 12/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 12/50: Train Loss: 0.0015, Train Acc: 100.00%, Val Loss: 0.0015, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 13/50, <PERSON><PERSON> 10/37, Loss: 0.0010\n", "  Epoch 13/50, <PERSON><PERSON> 20/37, Loss: 0.0026\n", "  Epoch 13/50, <PERSON><PERSON> 30/37, Loss: 0.0030\n", "Epoch 13/50: Train Loss: 0.0014, Train Acc: 100.00%, Val Loss: 0.0019, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/37, Loss: 0.0013\n", "  Epoch 14/50, <PERSON><PERSON> 10/37, Loss: 0.0017\n", "  Epoch 14/50, <PERSON><PERSON> 20/37, Loss: 0.0008\n", "  Epoch 14/50, <PERSON><PERSON> 30/37, Loss: 0.0006\n", "Epoch 14/50: Train Loss: 0.0011, Train Acc: 100.00%, Val Loss: 0.0018, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 15/50, <PERSON><PERSON> 10/37, Loss: 0.0017\n", "  Epoch 15/50, <PERSON><PERSON> 20/37, Loss: 0.0011\n", "  Epoch 15/50, <PERSON><PERSON> 30/37, Loss: 0.0013\n", "Epoch 15/50: Train Loss: 0.0009, Train Acc: 100.00%, Val Loss: 0.0013, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/37, Loss: 0.0022\n", "  Epoch 16/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 16/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 16/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 16/50: Train Loss: 0.0011, Train Acc: 100.00%, Val Loss: 0.0011, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/37, Loss: 0.0009\n", "  Epoch 17/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 17/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 17/50, <PERSON><PERSON> 30/37, Loss: 0.0024\n", "Epoch 17/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0013, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/37, Loss: 0.0013\n", "  Epoch 18/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 18/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 18/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 18/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0009, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 19/50, <PERSON><PERSON> 10/37, Loss: 0.0012\n", "  Epoch 19/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 19/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 19/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0010, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/37, Loss: 0.0008\n", "  Epoch 20/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 20/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 20/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 20/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0011, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/37, Loss: 0.0008\n", "  Epoch 21/50, <PERSON><PERSON> 10/37, Loss: 0.0014\n", "  Epoch 21/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 21/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 21/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 22/50, <PERSON><PERSON> 10/37, Loss: 0.0016\n", "  Epoch 22/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 22/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 22/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 23/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 23/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 23/50, <PERSON><PERSON> 30/37, Loss: 0.0011\n", "Epoch 23/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0009, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 24/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 24/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 24/50, <PERSON><PERSON> 30/37, Loss: 0.0011\n", "Epoch 24/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 25/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 25/50, <PERSON><PERSON> 20/37, Loss: 0.0012\n", "  Epoch 25/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 25/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 26/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 26/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 26/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 26/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 27/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 27/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 27/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 27/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 28/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 28/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 28/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 28/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/37, Loss: 0.0010\n", "  Epoch 29/50, <PERSON><PERSON> 10/37, Loss: 0.0009\n", "  Epoch 29/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 29/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 29/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/37, Loss: 0.0010\n", "  Epoch 30/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 30/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 30/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 30/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 31/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 31/50, <PERSON><PERSON> 20/37, Loss: 0.0013\n", "  Epoch 31/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 31/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 32/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 32/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 32/50, <PERSON><PERSON> 30/37, Loss: 0.0006\n", "Epoch 32/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 33/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 33/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 33/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 33/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 34/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 34/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 34/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 34/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 35/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 35/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 35/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 35/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 36/50, <PERSON><PERSON> 10/37, Loss: 0.0007\n", "  Epoch 36/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 36/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 36/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 37/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 37/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 37/50, <PERSON><PERSON> 30/37, Loss: 0.0006\n", "Epoch 37/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 38/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 38/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 38/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 38/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 39/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 39/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 39/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 39/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 40/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 40/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 40/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 40/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 41/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 41/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 41/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 41/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 42/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 42/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 42/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 42/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 43/50, <PERSON><PERSON> 10/37, Loss: 0.0010\n", "  Epoch 43/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 43/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 43/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 44/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 44/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 44/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 44/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/37, Loss: 0.0001\n", "  Epoch 45/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 45/50, <PERSON><PERSON> 20/37, Loss: 0.0001\n", "  Epoch 45/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 45/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/37, Loss: 0.0001\n", "  Epoch 46/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 46/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 46/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 46/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 47/50, <PERSON><PERSON> 10/37, Loss: 0.0001\n", "  Epoch 47/50, <PERSON><PERSON> 20/37, Loss: 0.0011\n", "  Epoch 47/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 47/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 48/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 48/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 48/50, <PERSON><PERSON> 30/37, Loss: 0.0001\n", "Epoch 48/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 49/50, <PERSON><PERSON> 10/37, Loss: 0.0001\n", "  Epoch 49/50, <PERSON><PERSON> 20/37, Loss: 0.0010\n", "  Epoch 49/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 49/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 50/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 50/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 50/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 50/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.1s\n", "--------------------------------------------------------------------------------\n", "\n", "DGCNN Training completed in 0.9 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING DGCNN FAIR COMPARISON TRAINING ===\")\n", "print(\"DGCNN vs Classical ML using IDENTICAL data sources\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/dgcnn_fair_comparison_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nDGCNN Training completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Fair Cross-Site Evaluation (RES→RCPS)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "test_evaluation", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "ab0149b7-cf58-4e9e-be67-ccaff9bf6bf8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== DGCNN FAIR CROSS-SITE EVALUATION (RES→RCPS) ===\n", "Testing DGCNN trained on RES data on RCPS data...\n", "SAME validation protocol as Classical ML\n", "\n", "DGCNN Cross-Site Test Results (RES→RCPS):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Predicted classes: [1]\n", "True classes: [1]\n", "\n", "Note: Model predicted only one class\n", "All predictions were: <PERSON><PERSON>\n", "Accuracy: 100.00%\n", "\n", "Actual distribution:\n", "  Piles: 1359\n", "  Non-Piles: 0\n", "\n", "Confusion Matrix:\n", "[[1359]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAApIAAAIjCAYAAACwHvu2AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAAWohJREFUeJzt3XlcVdX+//H3AeSAICAoIqk4oGTmXJmaM4papmWDU6I5VQ6lWWaloqZY5pSVXS2H/Fpqk5ZWSqlppWYaallOOXRLnIdwYFy/P/xxrkdAYQselNfzPvbjdvZeZ+/P3nAOHz9r7bVtxhgjAAAAIJfcXB0AAAAAbkwkkgAAALCERBIAAACWkEgCAADAEhJJAAAAWEIiCQAAAEtIJAEAAGAJiSQAAAAsIZEEAACAJSSScLmmTZuqadOmrg7jhtejRw+VL1/e1WEUWLt371arVq3k7+8vm82mJUuW5On+9+/fL5vNprlz5+bpfm9k+fHZ/uuvv+Tl5aUffvghT/eLK3vnnXdUrlw5JSUluToUFDAkkrkwd+5c2Ww2x+Ll5aXQ0FBFRUXpjTfe0L///pvte7dt26aePXuqQoUK8vLykq+vr2rVqqXnn39ef/75Z5bvWbNmjR588EGFhITI09NTwcHBateunT799FNHm4w/XjabTZ988kmmfcTExMhms+nYsWOOdT169JDNZlONGjWU1RMybTabBgwYcNXrUb58eafrcely4cKFq74/r124cEFTpkxRvXr15O/vLy8vL1WpUkUDBgzQrl27rns8yGzv3r3q16+fKlasKC8vL/n5+alhw4aaNm2azp8/n6/Hjo6O1vbt2zVu3DjNnz9fd9xxR74e73rK+Ez7+flleR13797t+Gy+/vrrud7/P//8o5iYGMXHx+dBtNdmzJgxqlevnho2bOhYl3H+GYvdbleVKlU0cuTILL+LsvvestlseuKJJ5zafvHFF2rSpImCg4NVtGhRVaxYUY888oi+/vrraz6XNWvWOB3b3d1dwcHBeuihh/T7779n+Z74+Hh169ZNZcuWld1uV2BgoCIjIzVnzhylpaVleY5ubm4KDQ1Vq1attGbNGqf9JScna9q0aapdu7b8/PwUEBCgatWqqW/fvvrjjz8c7Xr06KHk5GT95z//uebzxs3Fw9UB3IjGjBmjChUqKCUlRQkJCVqzZo2eeeYZTZ48WZ9//rlq1Kjh1H7WrFl68sknVaJECXXt2lW33nqrUlNT9euvv+r999/X1KlTdf78ebm7uzveM2rUKI0ZM0aVK1dWv379FBYWpuPHj+vLL79Ux44dtWDBAnXp0iVTXA8++KBsNluOzmP79u369NNP1bFjR8vXolatWnr22Wczrff09MzxPlauXGn5+BmOHTum1q1ba/PmzbrvvvvUpUsX+fr6aufOnVq4cKFmzpyp5OTkaz5OQTZr1iylp6e7OoxsLV++XA8//LDsdru6d++u22+/XcnJyfr+++/13HPP6bffftPMmTPz5djnz5/X+vXr9dJLL+XoH0lWhIWF6fz58ypSpEi+7P9qPDw8dO7cOX3xxRd65JFHnLYtWLBAXl5elv+B988//2j06NEqX768atWqleP35cVn+1JHjx7VvHnzNG/evEzb7Ha73n33XUnS6dOntXTpUo0dO1Z79+7VggULMrVv2bKlunfvnml9lSpVHP/9+uuv67nnnlOTJk00fPhwFS1aVHv27NE333yjhQsXqnXr1nlyXoMGDdKdd96plJQUbdu2Te+8847WrFmjX3/9VSEhIY527777rp544gmVKlVKjz32mCpXrqx///1X3377rXr16qVDhw7pxRdfzHSOxhjt27dPb7/9tpo3b67ly5erTZs2kqSOHTvqq6++UufOndWnTx+lpKTojz/+0LJly9SgQQPdeuutkiQvLy9FR0dr8uTJGjhwYI7/zqAQMMixOXPmGElm06ZNmbZ9++23xtvb24SFhZlz58451v/www/G3d3dNG7c2Jw5cybT+86fP29efvllk5qa6lj30UcfGUnmoYceMsnJyZne8/XXX5svvvjCGGPMvn37jCRTq1YtI8l88sknTm1HjRplJJmjR4861kVHRxtvb29TpUoVU6NGDZOenu70Hkmmf//+V70eYWFh5t57771qu7xw/vx5k5aWlu32e++917i5uZmPP/4407YLFy6YZ599Nj/Dc6nExERXh3BVf/75p/H19TW33nqr+eeffzJt3717t5k6dWq+Hf/AgQNGkpk4cWK+HcOVoqOjjY+Pj2nVqpXp0KFDpu2VK1c2HTt2tHwNNm3aZCSZOXPm5Kj92bNnc32MnJg8ebLx9vY2//77r9P6jPO/VHp6urn77ruNzWYzCQkJTtty8h2XkpJi/Pz8TMuWLbPcfvjwYQtn4Gz16tVGkvnoo4+c1s+YMcNIMq+++qpj3fr16427u7u55557svxbsmnTJqefT1bnuG3bNiPJtGrVyhhjzE8//WQkmXHjxmXaX2pqqjl27JjTup9//tlIMt9++22uzxU3L7q280jz5s01YsQIHThwQP/3f//nWD969GjZbDYtWLBAxYoVy/Q+Ly8vjR071qkaOWLECAUGBmr27NlZVjeioqJ03333Oa3r1KmTqlSpojFjxmTZXX05Nzc3vfzyy9q2bZs+++yz3Jxqjs2ZM0fNmzdXcHCw7Ha7brvtNs2YMSNTu8vHUWV09yxcuFAvv/yybrnlFhUtWlRnzpzJ8jgbN27U8uXL1atXryyrq3a7PVN33qpVq9SoUSP5+PgoICBA7du3z9SVlDEsYNeuXerWrZv8/f1VsmRJjRgxQsYY/fXXX2rfvr38/PwUEhKiSZMmOb0/4zwWLVqkF198USEhIfLx8dH999+vv/76y6ntunXr9PDDD6tcuXKy2+0qW7asBg8enKmbskePHvL19dXevXvVtm1bFStWTF27dnVsu3yM5MKFC1W3bl0VK1ZMfn5+ql69uqZNm+bU5s8//9TDDz+swMBAFS1aVHfffbeWL1+e5bksXrxY48aNU5kyZeTl5aUWLVpoz549Wf5cLvXaa68pMTFR7733nkqXLp1pe3h4uJ5++mnH69TUVI0dO1aVKlWS3W5X+fLl9eKLL2Yan1W+fHndd999+v7773XXXXfJy8tLFStW1Pvvv+9oExMTo7CwMEnSc889J5vN5rhO2Y0rzfjZXyouLk733HOPAgIC5Ovrq4iICKfqT3ZjJHPzu7Znzx716NFDAQEB8vf3V8+ePXXu3LnsL+xlunTpoq+++kqnTp1yrNu0aZN2796dqQdDkk6cOKGhQ4eqevXq8vX1lZ+fn9q0aaOtW7c62qxZs0Z33nmnJKlnz56O7tKM82zatKluv/12bd68WY0bN1bRokUd1+Xyz3Z0dLS8vLwynX9UVJSKFy+uf/7554rnt2TJEtWrV0++vr5XvRY2m0333HOPjDHZDh+6kmPHjunMmTNOXeiXCg4OzvU+c6pRo0aSLg4FyXC1vyV33HGHevToccX9Vq9eXSVKlNC+ffuc9p/VObq7uysoKMhpXd26dRUYGKilS5fm6nxwcyORzEOPPfaYpP9155w7d06rVq1S06ZNVaZMmRztY/fu3frjjz/UoUOHLL8ssuPu7q6XX35ZW7duzXFi2KVLF1WuXDnHyWdWUlJSdOzYMacl4w/fjBkzFBYWphdffFGTJk1S2bJl9dRTT+mtt97K0b7Hjh2r5cuXa+jQoRo/fny23eWff/65pP9d/6v55ptvFBUVpSNHjigmJkZDhgzRjz/+qIYNG2r//v2Z2j/66KNKT0/XhAkTVK9ePb3yyiuaOnWqWrZsqVtuuUWvvvqqwsPDNXToUK1duzbT+8eNG6fly5dr2LBhGjRokOLi4hQZGemUJH700Uc6d+6cnnzySU2fPl1RUVGaPn16ll1vqampioqKUnBwsF5//fVshybExcWpc+fOKl68uF599VVNmDBBTZs2dbpJ4fDhw2rQoIFWrFihp556SuPGjdOFCxd0//33Z/l7NGHCBH322WcaOnSohg8frg0bNjgS2Sv54osvVLFiRTVo0OCqbSWpd+/eGjlypOrUqaMpU6aoSZMmio2NVadOnTK13bNnjx566CG1bNlSkyZNUvHixdWjRw/99ttvkqQHH3xQU6ZMkSR17txZ8+fP19SpU3MUR4bffvtN9913n5KSkjRmzBhNmjRJ999//1Vv+Mjt79ojjzyif//9V7GxsXrkkUc0d+5cjR49OsdxZgxtuXQc9QcffKBbb71VderUydT+zz//1JIlS3Tfffdp8uTJeu6557R9+3Y1adLEkdRVrVpVY8aMkST17dtX8+fP1/z589W4cWPHfo4fP642bdqoVq1amjp1qpo1a5ZlfNOmTVPJkiUVHR3tGM/3n//8RytXrtT06dMVGhqa7bmlpKRo06ZNWZ5HdjKucfHixTNtu3DhQqbvrmPHjjmGwAQHB8vb21tffPGFTpw4keNjZuXLL79U7969czz05PK4z507p2+//VaNGzdWuXLlLMdx8uRJnTx50pEgZvwDa8GCBUpNTc3RPurUqcONTnDm2oLojeVKXdsZ/P39Te3atY0xxmzdutVIMs8880ymdsePHzdHjx51LElJScYYY5YuXWokmSlTpuQopoyu7YkTJ5rU1FRTuXJlU7NmTUd3dXZd2xndQPPmzTOSzKeffurYrlx0bUvKtIwaNcoYY5y6+DNERUWZihUrOq1r0qSJadKkieN1RndPxYoVs9zH5R544AEjyZw8efKqbY0xplatWiY4ONgcP37csW7r1q3Gzc3NdO/e3bEu49r17dvXsS41NdWUKVPG2Gw2M2HCBMf6kydPGm9vbxMdHZ3pPG655RanrqjFixcbSWbatGmOdVmdZ2xsrLHZbObAgQOOddHR0UaSeeGFFzK1j46ONmFhYY7XTz/9tPHz83MaNnG5Z555xkgy69atc6z7999/TYUKFUz58uUdwwkyzqVq1aqO31VjjJk2bZqRZLZv357tMU6fPm0kmfbt22fb5lLx8fFGkundu7fT+qFDhxpJZtWqVY51Gb+Da9eudaw7cuSIsdvtTsMZLv2cXOrya5Yh42efYcqUKZk+R5fLOMal3Yu5/V17/PHHnfb5wAMPmKCgoGyPeel5ZHymH3roIdOiRQtjjDFpaWkmJCTEjB49OstrcOHChUxDRvbt22fsdrsZM2aMY92VurabNGliJJl33nkny22XfraNMWbFihVGknnllVccQx6y6o6/3J49e4wkM3369GzPP+P7dM+ePeb11183NpvN3H777VkO38lu+fDDDx3tRo4caSQZHx8f06ZNGzNu3DizefPmq8Z6uUWLFhl3d3fTq1cvp1gyPlezZ882R48eNf/884/5+uuvTXh4uLHZbOann34yxvzvb8nTTz+d42NKMr169TJHjx41R44cMRs3bjQtWrQwksykSZOMMRe7/zN+fqVKlTKdO3c2b731ltN3zuX69u1rvL29c30NcPOiIpnHfH19HXdvZ3TFZtUNU7FiRZUsWdKxZFTVMt6Tm2pkhkurkjmd2qRr167XVJWsV6+e4uLinJaMKpq3t7ej3enTp3Xs2DE1adJEf/75p06fPn3VfUdHRzvtIzu5uWaHDh1SfHy8evToocDAQMf6GjVqqGXLlvryyy8zvad3796O/3Z3d9cdd9whY4x69erlWB8QEKCIiIgsu9C6d+/uFNtDDz2k0qVLOx3r0vM8e/asjh07pgYNGsgYo19++SXTPp988smrnmtAQIDOnj2ruLi4bNt8+eWXuuuuu3TPPfc41vn6+qpv377av3+/duzY4dS+Z8+eTpXhjC64K3Ud5vZ3OuO6DBkyxGl9xk1dl3e733bbbY44JKlkyZLZ/iysCggIkCQtXbo0x1UlK79rl98x3KhRIx0/fjzbYR1Z6dKli9asWaOEhAStWrVKCQkJWXZrSxeHfbi5XfwzkJaWpuPHjzu67bds2ZLjY9rtdvXs2TNHbVu1aqV+/fo5bg708vLK0Z3Ax48fl5R1dVG6+LnJ+D7N6CFo2LChli5dmuWNIe3bt8/03RUXF+dUTR09erQ++OAD1a5dWytWrNBLL72kunXrqk6dOk7d88nJybpw4UK2y/3336///Oc/mj17tvr3758plscff1wlS5ZUaGioWrdurdOnT2v+/PmOIQVW/y689957KlmypIKDg1WvXj398MMPGjJkiJ555hlJF7v/V6xYoVdeeUXFixfXhx9+qP79+yssLEyPPvqo0xCJDMWLF9f58+dzNeQCNzcSyTyWmJjo+LBn/H9iYmKmdkuXLlVcXFymsXt+fn6SdMWphK6ka9euCg8Pz3FimJF8xsfHW5pXr0SJEoqMjHRaKlasKEn64YcfFBkZ6RgbVrJkScfYqZwkkhUqVMhRDLm5ZgcOHJAkRUREZNpWtWpVHTt2TGfPnnVaf3lXUsbUQiVKlMi0/uTJk5n2W7lyZafXNptN4eHhTl2bBw8edCQcvr6+KlmypJo0aSIp87Xy8PDI0VCJp556SlWqVFGbNm1UpkwZPf7445mmLDlw4EC21yJj+6UuvxYZf9SzOu8Muf2dPnDggNzc3BQeHu60PiQkRAEBAVeNKSOuK8WUW48++qgaNmyo3r17q1SpUurUqZMWL158xaQyL37XcnJ9L5cxdnbRokVasGCB7rzzzkzXMkN6erqmTJmiypUry263q0SJEipZsqS2bduWo89ohltuuSVXMzW8/vrrCgwMVHx8vN54441cjTfM7nvNy8vLkQzOmTNHVatW1ZEjR7L9x2iZMmUyfXdFRkaqVKlSTu06d+6sdevW6eTJk1q5cqW6dOmiX375Re3atXPcBd+gQQN5e3tfcendu7eMMZoxY4Y+/vhjp2OMHDlScXFx+uyzz9S9e3edPn3akeBL1v8uZCTL33zzjTZu3Khjx45p0qRJTvu22+166aWX9Pvvv+uff/7Rhx9+qLvvvluLFy/OcoaDjOvPXdvIwPQ/eei///2vTp8+7fjSDg8Pl4eHh3799ddMbTOSBA8P5x9BxlQL27dvtxRDRmLYo0ePHA+I7tq1q8aOHasxY8aoQ4cOlo57ub1796pFixa69dZbNXnyZJUtW1aenp768ssvNWXKlBxVdXJSjZScr9mllam8cumNUFdaJ2X/R+5K0tLS1LJlS504cULDhg3TrbfeKh8fH/3999/q0aNHpmt1aRXpSoKDgxUfH68VK1boq6++0ldffaU5c+aoe/fuWU6fkhNWztvPz0+hoaFZfg6uJKd/qK7lZ5HdMS6dj0+6+Lu4du1arV69WsuXL9fXX3+tRYsWqXnz5lq5cmW2MeRWXvxe2e12Pfjgg5o3b57+/PNPxcTEZNt2/PjxGjFihB5//HGNHTtWgYGBcnNz0zPPPJOrqaRy+lnN8Msvv+jIkSOSLn5uO3fufNX3ZIzryy6pdnd3V2RkpON1VFSUbr31VvXr18/R43Mt/Pz81LJlS7Vs2VJFihTRvHnztHHjRjVp0kQxMTFOc/VmZd++fRo7dqwiIiIyfU9Vr17dEXuHDh107tw59enTR/fcc4/Kli3r+FuS278LGclyTpUuXVqdOnVSx44dVa1aNS1evFhz5851+jt18uRJFS1aNNc/c9y8qEjmofnz50u6+AUmST4+PmratKm+++47/f333znaR5UqVRQREaGlS5dmWcnMiW7duik8PFyjR4/OdVUyr+7G++KLL5SUlKTPP/9c/fr1U9u2bRUZGZkvXz7t2rWTJKe75bOTMbh8586dmbb98ccfKlGihHx8fPI0vt27dzu9NsZoz549jruFt2/frl27dmnSpEkaNmyY2rdvr8jIyCveeJBTnp6eateund5++23HZODvv/++407rsLCwbK9Fxva8cN9992nv3r1av379VduGhYUpPT0903U7fPiwTp06lWcxSRcrfll1311e9ZQuznTQokULTZ48WTt27NC4ceO0atUqrV69Ost9u+J3LUNG1ezff//N8galDB9//LGaNWum9957T506dVKrVq0UGRmZ6ZrkZfXp7Nmz6tmzp2677Tb17dtXr732mjZt2nTV95UrV07e3t6OO46vpnTp0ho8eLC++OILbdiw4VrDdpIxmf2hQ4ckXfz97tGjR7ZLixYt9P777ys8PFyrVq3KVPW83IQJE3ThwgWNGzdOklS0aFE1b95ca9euzTTjQ34oUqSIatSo4biZ8lL79u1z9FgAEolknlm1apXGjh2rChUqON3FOnLkSKWlpalbt25ZJoZZJXqjR4/W8ePH1bt37yzvpFu5cqWWLVuWbSyXJoY5/Zf4pclnXsiorFx6fqdPn9acOXPyZP+Xql+/vlq3bq133303y+755ORkDR06VNLFPy61atXSvHnznP5Y/vrrr1q5cqXatm2b5/G9//77Tl1SH3/8sQ4dOuSYEDira2WMyTRNT25ljCnL4Obm5pgsP2ManbZt2+qnn35ySvDOnj2rmTNnqnz58rrtttuuKYYMzz//vHx8fNS7d28dPnw40/a9e/c6zjfjZ3D5ndWTJ0+WJN177715EpMkVapUSadPn9a2bdsc6w4dOpTpjvWs7trNmJg7u0fGueJ3LUOzZs00duxYvfnmm04TWl/O3d0903fQRx99lOkfvhkJb1ZJd24NGzZMBw8e1Lx58zR58mSVL19e0dHRV330XpEiRXTHHXfo559/zvGxBg4cqKJFi2rChAm5jvPcuXPZ/sPnq6++kpT1sIWsvPvuu7LZbFq1alWW019drlKlSurYsaPmzp2rhIQESRcfUmGM0WOPPZbl35LNmzfnuqdh9+7dOnjwYKb1p06d0vr161W8eHGVLFnSaduWLVtyPPsCCge6ti346quv9Mcffyg1NVWHDx/WqlWrFBcXp7CwMH3++efy8vJytG3UqJHefPNNDRw4UJUrV3Y82SY5OVm7du3SggUL5Onp6fRl/+ijjzoe5fbLL7+oc+fOjifbfP311/r222/1wQcfXDHGjO7qnD7SzN3dXS+99FKOB8xfTatWrRzVsH79+ikxMVGzZs1ScHCw41/xeen9999Xq1at9OCDD6pdu3Zq0aKFfHx8tHv3bi1cuFCHDh1yjEedOHGi2rRpo/r166tXr146f/68pk+fLn9//yt2A1oVGBioe+65Rz179tThw4c1depUhYeHq0+fPpIuds1XqlRJQ4cO1d9//y0/Pz998skn1zzGr3fv3jpx4oSaN2+uMmXK6MCBA5o+fbpq1arlqCi88MIL+vDDD9WmTRsNGjRIgYGBmjdvnvbt26dPPvkkR13oOVGpUiV98MEHevTRR1W1alWnJ9v8+OOP+uijjxxz4NWsWVPR0dGaOXOmTp06pSZNmuinn37SvHnz1KFDh2ynlrGiU6dOGjZsmB544AENGjRI586d04wZM1SlShWnm03GjBmjtWvX6t5771VYWJiOHDmit99+W2XKlHG6Uely1/t3LUPGPLFXc99992nMmDHq2bOnGjRooO3bt2vBggWOcc4ZKlWqpICAAL3zzjsqVqyYfHx8VK9evRyPY86watUqvf322xo1apRjGp85c+aoadOmGjFihF577bUrvr99+/Z66aWXdObMGce4wSsJCgpSz5499fbbb+v33393qqTt2rUry16MUqVKqWXLljp37pwaNGigu+++W61bt1bZsmV16tQpLVmyROvWrVOHDh1Uu3btHJ13TEyM+vfvn6uxoM8995wWL16sqVOnasKECWrQoIHeeustPfXUU7r11ludnmyzZs0aff7553rllVdyvH9J2rp1q7p06aI2bdqoUaNGCgwM1N9//6158+bpn3/+0dSpU52GW2zevFknTpxQ+/btc3Uc3OSu813iN7SM6X8yFk9PTxMSEmJatmxppk2bluXTBjL88ssvpnv37qZcuXLG09PT+Pj4mBo1aphnn33W7NmzJ8v3fPvtt6Z9+/YmODjYeHh4mJIlS5p27dqZpUuXOtpkN63J5fFmN/3PpVJSUkylSpXy7Mk2n3/+ualRo4bx8vIy5cuXN6+++qqZPXu2kWT27dvnaJfd9D+XP+3has6dO2def/11c+eddxpfX1/j6elpKleubAYOHJjpGn/zzTemYcOGxtvb2/j5+Zl27dqZHTt2OLXJauokY7K/fk2aNDHVqlXLdB4ffvihGT58uAkODjbe3t7m3nvvzTS9xo4dO0xkZKTx9fU1JUqUMH369HFM+XHplCvZHTtj26VT2Xz88cemVatWJjg42Hh6eppy5cqZfv36mUOHDjm9b+/eveahhx4yAQEBxsvLy9x1111m2bJlTm2y+5lkNeXNlezatcv06dPHlC9f3nh6eppixYqZhg0bmunTp5sLFy442qWkpJjRo0ebChUqmCJFipiyZcua4cOHO7UxJvvfwct/p670OVm5cqW5/fbbjaenp4mIiDD/93//l2n6n4zPYmhoqPH09DShoaGmc+fOZteuXVe9Ftfyu5bxGb7085KVK/1eXOkaZDz1qXTp0sbb29s0bNjQrF+/Pstpe5YuXWpuu+024+Hh4XSel//eX+rS/Zw5c8aEhYWZOnXqmJSUFKd2gwcPNm5ubmb9+vVXPIfDhw8bDw8PM3/+/Byf/969e427u7vT1FyXfo9fvmTEm5KSYmbNmmU6dOhgwsLCjN1uN0WLFjW1a9c2EydOdJoGy6qrfdc1bdrU+Pn5mVOnTjnWbd682XTp0sWEhoaaIkWKmOLFi5sWLVqYefPmOU3llJPv8cOHD5sJEyaYJk2amNKlSxsPDw9TvHhx07x58yyfEjZs2DBTrly5TNMpoXCzGWNxJmoAV7RmzRo1a9ZMH330kR566CFXhwPcFHr16qVdu3Zp3bp1rg6lUElKSlL58uX1wgsvOD2FCmCMJADghjFq1Cht2rSJp6tcZ3PmzFGRIkUyzXUKkEgCAG4Y5cqV04ULF7J9BjbyxxNPPKGDBw/Kbre7OhQUMCSSAAAAsIQxkgAAALCEiiQAAAAsIZEEAACAJSSSAAAAsOSmfLJN6lN59wg1ALhZeby93NUhAAXGE7arPy3JqnfMmXzbt6tRkQQAAIAlN2VFEgAAIDeorFlDIgkAAAo9N5vN1SHckEjAAQAAYAkVSQAAUOhRWbOG6wYAAABLqEgCAIBCz40hkpZQkQQAAIAlVCQBAEChR2XNGq4bAAAALKEiCQAACj3mkbSGRBIAABR6dNFaw3UDAACAJVQkAQBAocf0P9ZQkQQAAIAlVCQBAEChR2XNGq4bAAAALKEiCQAACj0b0/9YQkUSAAAAllCRBAAAhR6VNWtIJAEAQKHH9D/WkIADAADAEiqSAACg0KOyZg3XDQAAAJZQkQQAAIWeG9P/WEJFEgAAAJZQkQQAAIUelTVruG4AAACwhIokAAAo9JhH0hoSSQAAUOjRRWsN1w0AAACWUJEEAACFnpvo27aCiiQAAAAsoSIJAAAKPW62sYaKJAAAACyhIgkAAAo9KmvWcN0AAABgCRVJAABQ6DFG0hoSSQAAUOgx/Y81dG0DAADAEiqSAACg0KNr2xoqkgAAALCEiiQAACj0qKxZw3UDAACAJVQkAQBAoccYSWuoSAIAAMASKpIAAKDQYx5Ja0gkAQBAoUfXtjV0bQMAAMASKpIAAKDQoyBpDRVJAAAAWEJFEgAAFHqMkbSGiiQAAAAsoSIJAAAKPab/sYaKJAAAACyhIgkAAAo9xkhaQyIJAAAKPbporeG6AQAAwBIqkgAAoNCjZ9saKpIAAACwhIokAAAo9Nxs1CStoCIJAAAAS6hIAgCAQo96pDVUJAEAAGAJFUkAAFDoUZG0hkQSAAAUeiSS1tC1DQAAAEuoSAIAgELPxvQ/llCRBAAAgCVUJAEAQKFHPdIaKpIAAACwhEQSAAAUem75uOTW2rVr1a5dO4WGhspms2nJkiXZtn3iiSdks9k0depUp/Xly5eXzWZzWiZMmODUZtu2bWrUqJG8vLxUtmxZvfbaa7mOlUQSAACgADl79qxq1qypt95664rtPvvsM23YsEGhoaFZbh8zZowOHTrkWAYOHOjYdubMGbVq1UphYWHavHmzJk6cqJiYGM2cOTNXsTJGEgAAFHoF6abtNm3aqE2bNlds8/fff2vgwIFasWKF7r333izbFCtWTCEhIVluW7BggZKTkzV79mx5enqqWrVqio+P1+TJk9W3b98cx0pFEgAAIB8lJSXpzJkzTktSUpLl/aWnp+uxxx7Tc889p2rVqmXbbsKECQoKClLt2rU1ceJEpaamOratX79ejRs3lqenp2NdVFSUdu7cqZMnT+Y4FhJJAABQ6Nny8X+xsbHy9/d3WmJjYy3H+uqrr8rDw0ODBg3Kts2gQYO0cOFCrV69Wv369dP48eP1/PPPO7YnJCSoVKlSTu/JeJ2QkJDjWOjaBgAAhV5+9mwPHz5cQ4YMcVpnt9st7Wvz5s2aNm2atmzZcsVJ1C89Xo0aNeTp6al+/fopNjbW8rGzQkUSAAAgH9ntdvn5+TktVpO5devW6ciRIypXrpw8PDzk4eGhAwcO6Nlnn1X58uWzfV+9evWUmpqq/fv3S5JCQkJ0+PBhpzYZr7MbV5kVKpIAAKDQK0D32lzRY489psjISKd1UVFReuyxx9SzZ89s3xcfHy83NzcFBwdLkurXr6+XXnpJKSkpKlKkiCQpLi5OERERKl68eI7jIZEEAAAoQBITE7Vnzx7H63379ik+Pl6BgYEqV66cgoKCnNoXKVJEISEhioiIkHTxRpqNGzeqWbNmKlasmNavX6/BgwerW7dujiSxS5cuGj16tHr16qVhw4bp119/1bRp0zRlypRcxUoiCQAACj23AlSS/Pnnn9WsWTPH64zxjtHR0Zo7d+5V32+327Vw4ULFxMQoKSlJFSpU0ODBg53GTfr7+2vlypXq37+/6tatqxIlSmjkyJG5mvpHkmzGGJOrd9wAUp/Kej4lAMD/eLy93NUhAAXGlyVK59u+2x47lG/7djUqkgAAoNCz3TCjJAsW7toGAACAJVQkAQBAoUc90hoSSQAAUOgVpGdt30jo2gYAAIAlVCQBAEChR0HSGiqSAAAAsISKJAAAKPTcqElaQkUSAAAAllCRBAAAhR71SGuoSAIAAMASKpIAAKDQYx5Ja0gkAQBAoUceaQ1d2wAAALCEiiQAACj0bNQkLaEiCQAAAEuoSAIAgELPjYKkJVQkAQAAYAkVSQAAUOhRkLSGiiQAAAAsoSIJAAAKPSqS1pBIAgCAQo/pf6yhaxsAAACWUJEEAACFHs/atoaKJAAAACyhIgkAAAo9KmvWcN0AAABgCRVJAABQ6DFE0hoqkgAAALCEiiQAACj0bNy2bQmJJAAAKPRII62haxsAAACWUJEEAACFHhVJa6hIAgAAwBIqkgAAoNDjZhtrqEgCAADAEiqSAACg0HOjIGkJFUkAAABYQkUSAAAUejZKkpaQSAIAgEKPe22soWsbAAAAllCRBAAAhR4VSWuoSAIAAMASKpIAAKDQY0Jya6hIAgAAwBIqkgAAoNCjIGlNgapIJicna+fOnUpNTXV1KAAAALiKApFInjt3Tr169VLRokVVrVo1HTx4UJI0cOBATZgwwcXRAQCAm53NZsu35WZWIBLJ4cOHa+vWrVqzZo28vLwc6yMjI7Vo0SIXRgYAAAoDmy3/lptZgRgjuWTJEi1atEh33323U+ZerVo17d2714WRAQAAIDsFIpE8evSogoODM60/e/bsTV8SBgAArudGvmFJgejavuOOO7R8+XLH64zk8d1331X9+vVdFRYAAACuoEBUJMePH682bdpox44dSk1N1bRp07Rjxw79+OOP+u6771wdHgAAuMlRkLSmQFQk77nnHsXHxys1NVXVq1fXypUrFRwcrPXr16tu3bquDg8AAABZKBAVSUmqVKmSZs2a5eowAABAIcQ9Gda4LJE8c+ZMjtv6+fnlYyQAAACwwmWJZEBAwFWzf2OMbDab0tLSrlNUAACgMLIViMF+Nx6XJZKrV6921aEBAACc0LVtjcsSySZNmrjq0AAAAMgDLkskt23bpttvv11ubm7atm3bFdvWqFHjOkUFAAAKIwqS1rgskaxVq5YSEhIUHBysWrVqyWazyRiTqR1jJAEAAAomlyWS+/btU8mSJR3/DQAA4CqMkbTGZYlkWFiYpIvTAO3atUvJycm66667HMklAAAACjaXTkgeHx+vtm3b6vDhwzLGqFixYlq8eLGioqJcGRYAAChkKEha49JZk4YNG6YKFSro+++/1+bNm9WiRQsNGDDAlSEBAAAgh1xakdy8ebNWrlypOnXqSJJmz56twMBAnTlzhqfZoOAJrya3lh1lKxsuW0CQ0v4zVmbrBsdmt3u7yFa3sVS8pJSWKnNwj9I/f1/av9PRxn3sbNmCSjntNm3JXJmVHzle2+rcI7eoR6VSodK/Z5T+3Rcy33ya/+cHAIWYGyVJS1yaSJ44cUJlypRxvA4ICJCPj4+OHz9OIokCx+bpJf13n9J/jJN7v5czbTeH/5ZZ9I7MsQTJ01NuzTvIfeBYpY3qLSX+75GgaV/Ml/lhxf/eeOHc/45xW1259XxO6Yvfkdnxi2yly8qty0ClpyTLfLcsX88PAIDccmkiKUk7duxQQkKC47UxRr///rv+/fdfxzrmkURBYHZsltmxOfvtP3/n9Dr9k1nyaBgl2y0VZHZu/d+GC+elMyez3IetXnOZrRtk1n11cZ/HE5S+8iO5tXpIaSSSAJBvKEha4/JEskWLFpnmj7zvvvsc80oyjyRuSO4est3TRuZcosx/nae3cmv1sNSmk3TyqNI3rZFZtURKT7+40aOIlJzkvK/kJNmKl5QCg6UTR65P/ABQyDD9jzUuTSSZPxI3G9vtd8rt8WGSp106c0Jp01+Wzv6vWzt99efSX3tlzv0rW8WqcmvfQ8Y/UOmfvCtJMju2yO2hPrJt+EZm1zapZGm5RT548c3+gSSSAIACxaWJZMZcktciKSlJSUnOFRz3tDTZ3d2ved9Abpld25QWO1Dy8ZPbPa3l3usFpb02REo8fXH7qiX/a/v3fqWnpsqtywBp6VwpNVXmh69lSpaW25OjJHcP6cI5pa9eKvf7uklZPPkJAJA3KEha49Lpf7JSvXp1/fXXXzluHxsbK39/f6fl1S178zFC4AqSk6Sjh6T9O5X+f9Ok9DTZGrbKtrnZv1M2dw8p8H93cqcvmaO0wQ8p7eWeSnuhm7R/18UNxw7ld/QAAORKgUsk9+/fr5SUlBy3Hz58uE6fPu20DKtTKR8jBHLB5iabR5HsN5epKJOeJv172nmDSZdOH5fSUmW7s4nMn7873fkNAMhbNlv+LTczl99sc63sdrvsdrvTulS6tZEf7F5SydD/vQ4KkcpUlM7+K509I7fWjyp920bpzAnJx19uTe6VAoKUvuX7i+0r3Cpb+YiLYx8vnJet4q1ye6iPzE+rpfOJF9v4+MlWp6HMru1SEU+51Y+UrfY9SpvywvU/XwAArqLAVSQbNWokb29vV4cBZGIrV1keL06Xx4vTJUnuD/WRx4vT5XZft4t3XYeUlXvfF+U+apbcnxwp+fgpbfLz0qGDF3eQmiK3OxrLffAEuY94+2LiuWqJ0j+Y7nQct3ot5D5sqtyfnSiVDlPa1OHSgV3X+3QBoFCxudnybcmttWvXql27dgoNDZXNZtOSJUuybfvEE0/IZrNp6tSpTutPnDihrl27ys/PTwEBAerVq5cSExOd2mzbtk2NGjWSl5eXypYtq9deey3XsRa4iuSXX37p6hCALJnd25X61L3Zbk+fOe7KO/hrr9ImPnvlNmfPKO31oRaiAwDcLM6ePauaNWvq8ccf14MPPphtu88++0wbNmxQaGhopm1du3bVoUOHFBcXp5SUFPXs2VN9+/bVBx98IEk6c+aMWrVqpcjISL3zzjvavn27Hn/8cQUEBKhv3745jrXAJJK7d+/W6tWrdeTIEaVnzKn3/40cOdJFUQEAgMKgII1lbNOmjdq0aXPFNn///bcGDhyoFStW6N57nYscv//+u77++mtt2rRJd9xxhyRp+vTpatu2rV5//XWFhoZqwYIFSk5O1uzZs+Xp6alq1aopPj5ekydPvvESyVmzZunJJ59UiRIlFBIS4jQpqM1mI5EEAAD5Kj+ftZ3VVIVZ3eORU+np6Xrsscf03HPPqVq1apm2r1+/XgEBAY4kUpIiIyPl5uamjRs36oEHHtD69evVuHFjeXp6OtpERUXp1Vdf1cmTJ1W8ePEcxVIgxki+8sorGjdunBISEhQfH69ffvnFsWzZssXV4QEAAFiW1VSFsbGxlvf36quvysPDQ4MGDcpye0JCgoKDg53WeXh4KDAw0PFY6oSEBJUqVcqpTcbrSx9dfTUFoiJ58uRJPfzww64OAwAAFFL52bU9fPhwDRkyxGmd1Wrk5s2bNW3aNG3ZsqVAPNaxQFQkH374Ya1cudLVYQAAAOQ5u90uPz8/p8VqIrlu3TodOXJE5cqVk4eHhzw8PHTgwAE9++yzKl++vCQpJCRER444P1I3NTVVJ06cUEhIiKPN4cOHndpkvM5okxMFoiIZHh6uESNGaMOGDapevbqKFHGewDm70i0AAEBeKAjVvZx47LHHFBkZ6bQuKipKjz32mHr27ClJql+/vk6dOqXNmzerbt26kqRVq1YpPT1d9erVc7R56aWXlJKS4si74uLiFBERkePxkVIBSSRnzpwpX19ffffdd/ruu++cttlsNhJJAABQaCQmJmrPnj2O1/v27VN8fLwCAwNVrlw5BQUFObUvUqSIQkJCFBERIUmqWrWqWrdurT59+uidd95RSkqKBgwYoE6dOjmmCurSpYtGjx6tXr16adiwYfr11181bdo0TZkyJVexFohEct++fa4OAQAAFGIFqSD5888/q1mzZo7XGeMro6OjNXfu3BztY8GCBRowYIBatGghNzc3dezYUW+88YZju7+/v1auXKn+/furbt26KlGihEaOHJmrqX8kyWaMMbl6Rz7LCOdaSsxXmjQaAHCRx9vLXR0CUGAcuzvzNDp5pcSG3/Jt365WIG62kaT3339f1atXl7e3t7y9vVWjRg3Nnz/f1WEBAIBCwGaz5dtyMysQXduTJ0/WiBEjNGDAADVs2FCS9P333+uJJ57QsWPHNHjwYBdHCAAAbmY3eb6XbwpEIjl9+nTNmDFD3bt3d6y7//77Va1aNcXExJBIAgAAFEAFIpE8dOiQGjRokGl9gwYNdOjQIRdEBAAACpObvQs6vxSIMZLh4eFavHhxpvWLFi1S5cqVXRARAAAArqZAVCRHjx6tRx99VGvXrnWMkfzhhx/07bffZplgAgAA5CVbgSit3XgKxGXr2LGjNm7cqKCgIC1ZskRLlixRiRIl9NNPP+mBBx5wdXgAAADIQoGoSEpS3bp1tWDBAleHAQAACiHGSFrj0kTSzc3tqj84m82m1NTU6xQRAAAAcsqlieRnn32W7bb169frjTfeUHp6+nWMCAAAFEpuVCStcGki2b59+0zrdu7cqRdeeEFffPGFunbtqjFjxrggMgAAUKjQtW1JgbjZRpL++ecf9enTR9WrV1dqaqri4+M1b948hYWFuTo0AAAAZMHlN9ucPn1a48eP1/Tp01WrVi19++23atSokavDAgAAhQg321jj0kTytdde06uvvqqQkBB9+OGHWXZ1AwAAoGCyGWOMqw7u5uYmb29vRUZGyt3dPdt2n376aa72m/rUvdcaGgDc9DzeXu7qEIAC40xknXzbt983W/Jt367m0opk9+7dKSUDAADcoFyaSM6dO9eVhwcAALiIwpYlBeaubQAAANxYXH7XNgAAgKvZmJDcEhJJAAAAurYtoWsbAAAAllCRBAAAhR5d29ZQkQQAAIAlVCQBAAAYI2kJFUkAAABYQkUSAACAMZKWUJEEAACAJVQkAQBAoWdjjKQlJJIAAAB0bVuSo0Ry27ZtOd5hjRo1LAcDAACAG0eOEslatWrJZrPJGJPl9oxtNptNaWlpeRogAABAvqNr25IcJZL79u3L7zgAAABwg8lRIhkWFpbfcQAAALiMjXlsLLF02ebPn6+GDRsqNDRUBw4ckCRNnTpVS5cuzdPgAAAAUHDlOpGcMWOGhgwZorZt2+rUqVOOMZEBAQGaOnVqXscHAACQ/2y2/FtuYrlOJKdPn65Zs2bppZdekru7u2P9HXfcoe3bt+dpcAAAACi4cj2P5L59+1S7du1M6+12u86ePZsnQQEAAFxPNuaRtCTXFckKFSooPj4+0/qvv/5aVatWzYuYAAAAri+6ti3JdUVyyJAh6t+/vy5cuCBjjH766Sd9+OGHio2N1bvvvpsfMQIAAKAAynUi2bt3b3l7e+vll1/WuXPn1KVLF4WGhmratGnq1KlTfsQIAACQv+jatsTSs7a7du2qrl276ty5c0pMTFRwcHBexwUAAIACzlIiKUlHjhzRzp07JV18RGLJkiXzLCgAAIDryXaTj2XML7m+2ebff//VY489ptDQUDVp0kRNmjRRaGiounXrptOnT+dHjAAAACiAcp1I9u7dWxs3btTy5ct16tQpnTp1SsuWLdPPP/+sfv365UeMAAAA+cvNln/LTSzXXdvLli3TihUrdM899zjWRUVFadasWWrdunWeBgcAAICCK9eJZFBQkPz9/TOt9/f3V/HixfMkKAAAgOuKMZKW5Lpr++WXX9aQIUOUkJDgWJeQkKDnnntOI0aMyNPgAAAArgebzZZvy80sRxXJ2rVrO12I3bt3q1y5cipXrpwk6eDBg7Lb7Tp69CjjJAEAAAqJHCWSHTp0yOcwAAAAXOgmvykmv+QokRw1alR+xwEAAIAbjOUJyQEAAG4WN/tYxvyS60QyLS1NU6ZM0eLFi3Xw4EElJyc7bT9x4kSeBQcAAICCK9d3bY8ePVqTJ0/Wo48+qtOnT2vIkCF68MEH5ebmppiYmHwIEQAAIJ8xIbkluU4kFyxYoFmzZunZZ5+Vh4eHOnfurHfffVcjR47Uhg0b8iNGAAAAFEC5TiQTEhJUvXp1SZKvr6/j+dr33Xefli9fnrfRAQAAXA82W/4tN7FcJ5JlypTRoUOHJEmVKlXSypUrJUmbNm2S3W7P2+gAAABQYOU6kXzggQf07bffSpIGDhyoESNGqHLlyurevbsef/zxPA8QAAAgv9ncbPm23Mxyfdf2hAkTHP/96KOPKiwsTD/++KMqV66sdu3a5WlwAAAA18VN3gWdX3Jdkbzc3XffrSFDhqhevXoaP358XsQEAACAG8A1J5IZDh06pBEjRuTV7gAAAK4fpv+xJM8SSQAAABQuPCIRAAAUejwi0RoqkgAAALAkxxXJIUOGXHH70aNHrzkYAAAAl7jJxzLmlxwnkr/88stV2zRu3PiaggEAAMCNI8eJ5OrVq/MzDgAAANdhjKQl3GwDAABAImkJN9sAAADAEiqSAAAAVCQtoSIJAAAAS6hIAgAAuFFbs8LSVVu3bp26deum+vXr6++//5YkzZ8/X99//32eBgcAAICCK9eJ5CeffKKoqCh5e3vrl19+UVJSkiTp9OnTGj9+fJ4HCAAAkO9stvxbbmK5TiRfeeUVvfPOO5o1a5aKFCniWN+wYUNt2bIlT4MDAABAwZXrRHLnzp1ZPsHG399fp06dyouYAAAArq8CVJFcu3at2rVrp9DQUNlsNi1ZssRpe0xMjG699Vb5+PioePHiioyM1MaNG53alC9fXjabzWmZMGGCU5tt27apUaNG8vLyUtmyZfXaa6/lOtZcJ5IhISHas2dPpvXff/+9KlasmOsAAAAAXK4AJZJnz55VzZo19dZbb2W5vUqVKnrzzTe1fft2ff/99ypfvrxatWqlo0ePOrUbM2aMDh065FgGDhzo2HbmzBm1atVKYWFh2rx5syZOnKiYmBjNnDkzV7Hm+q7tPn366Omnn9bs2bNls9n0zz//aP369Ro6dKhGjBiR290BAADgEm3atFGbNm2y3d6lSxen15MnT9Z7772nbdu2qUWLFo71xYoVU0hISJb7WLBggZKTkzV79mx5enqqWrVqio+P1+TJk9W3b98cx5rriuQLL7ygLl26qEWLFkpMTFTjxo3Vu3dv9evXzynTBQAAuGG4ueXbkpSUpDNnzjgtGTcrX6vk5GTNnDlT/v7+qlmzptO2CRMmKCgoSLVr19bEiROVmprq2LZ+/Xo1btxYnp6ejnVRUVHauXOnTp48mePj5zqRtNlseumll3TixAn9+uuv2rBhg44ePaqxY8fmdlcAAAA3vdjYWPn7+zstsbGx17TPZcuWydfXV15eXpoyZYri4uJUokQJx/ZBgwZp4cKFWr16tfr166fx48fr+eefd2xPSEhQqVKlnPaZ8TohISHHcViekNzT01O33Xab1bcDAAAUHPk4Tc/w4cM1ZMgQp3V2u/2a9tmsWTPFx8fr2LFjmjVrlh555BFt3LhRwcHBkuR0vBo1asjT01P9+vVTbGzsNR/7UrlOJJs1aybbFS72qlWrrikgAACAm4ndbs/T5E2SfHx8FB4ervDwcN19992qXLmy3nvvPQ0fPjzL9vXq1VNqaqr279+viIgIhYSE6PDhw05tMl5nN64yK7lOJGvVquX0OiUlRfHx8fr1118VHR2d290BAAC43g0+cXh6evoVx13Gx8fLzc3NUbGsX7++XnrpJaWkpDjmBY+Li1NERISKFy+e4+PmOpGcMmVKlutjYmKUmJiY290BAADgEomJiU5TLe7bt0/x8fEKDAxUUFCQxo0bp/vvv1+lS5fWsWPH9NZbb+nvv//Www8/LOnijTQbN25Us2bNVKxYMa1fv16DBw9Wt27dHElily5dNHr0aPXq1UvDhg3Tr7/+qmnTpmWb52XH8hjJy3Xr1k133XWXXn/99bzaJQAAwPVRgCqSP//8s5o1a+Z4nTHeMTo6Wu+8847++OMPzZs3T8eOHVNQUJDuvPNOrVu3TtWqVZN0sSt94cKFiomJUVJSkipUqKDBgwc7jZv09/fXypUr1b9/f9WtW1clSpTQyJEjczX1j5SHieT69evl5eWVV7sDAAC4ftxyPZFNvmnatKmMMdlu//TTT6/4/jp16mjDhg1XPU6NGjW0bt26XMd3qVwnkg8++KDTa2OMDh06pJ9//pkJyQEAAAqRXCeS/v7+Tq/d3NwUERGhMWPGqFWrVnkWGAAAwHVTgLq2byS5SiTT0tLUs2dPVa9ePVd39AAAAODmk6sBAe7u7mrVqpVOnTqVT+EAAAC4gM2Wf8tNLNcjS2+//Xb9+eef+RELAAAAbiC5TiRfeeUVDR06VMuWLdOhQ4cyPYQcAADghkNF0pIcj5EcM2aMnn32WbVt21aSdP/99zs9KtEYI5vNprS0tLyPEgAAAAVOjhPJ0aNH64knntDq1avzMx4AAIDrzlaA5pG8keQ4kcyYGLNJkyb5FgwAAIBL3ORd0PklV+m3jYsMAACA/y9X80hWqVLlqsnkiRMnrikgAACA645imSW5SiRHjx6d6ck2AAAAKJxylUh26tRJwcHB+RULAACAa1CRtCTHYyQZHwkAAIBL5fqubQAAgJsO0/9YkuNEMj09PT/jAAAAwA0mV2MkAQAAbkoM4bOERBIAAIBE0hIGBAAAAMASKpIAAABUJC2hIgkAAABLqEgCAAAw/Y8lXDUAAABYQkUSAACAMZKWUJEEAACAJVQkAQAAqEhaQiIJAADAzTaWcNUAAABgCRVJAAAAurYtoSIJAAAAS6hIAgAAUJG0hIokAAAALKEiCQAAQEXSEiqSAAAAsISKJAAAAPNIWkIiCQAAQNe2JaTfAAAAsISKJAAAABVJS6hIAgAAwBIqkgAAADZqa1Zw1QAAAGAJFUkAAAA3xkhaQUUSAAAAllCRBAAAYIykJSSSAAAATP9jCek3AAAALKEiCQAAwLO2LeGqAQAAwBIqkgAAAIyRtISKJAAAACyhIgkAAMD0P5Zw1QAAAGAJFUkAAADGSFpCRRIAAACWUJEEAABgHklLbspE0uPt5a4OAQAA3Ejo2raE9BsAAACW3JQVSQAAgFxh+h9LuGoAAACwhIokAACAG2MkraAiCQAAAEuoSAIAADBG0hKuGgAAACyhIgkAAMA8kpaQSAIAANC1bQlXDQAAAJZQkQQAAGD6H0uoSAIAAMASKpIAAADcbGMJFUkAAABYQkUSAACAu7Yt4aoBAADAEiqSAAAA3LVtCYkkAAAAXduWcNUAAAAKkLVr16pdu3YKDQ2VzWbTkiVLnLbHxMTo1ltvlY+Pj4oXL67IyEht3LjRqc2JEyfUtWtX+fn5KSAgQL169VJiYqJTm23btqlRo0by8vJS2bJl9dprr+U6VhJJAAAAmy3/llw6e/asatasqbfeeivL7VWqVNGbb76p7du36/vvv1f58uXVqlUrHT161NGma9eu+u233xQXF6dly5Zp7dq16tu3r2P7mTNn1KpVK4WFhWnz5s2aOHGiYmJiNHPmzNxdNmOMyfUZAgAA3ETSlryZb/t27zDA8nttNps+++wzdejQIds2Z86ckb+/v7755hu1aNFCv//+u2677TZt2rRJd9xxhyTp66+/Vtu2bfXf//5XoaGhmjFjhl566SUlJCTI09NTkvTCCy9oyZIl+uOPP3IcHxVJAAAAm1u+LUlJSTpz5ozTkpSUlCdhJycna+bMmfL391fNmjUlSevXr1dAQIAjiZSkyMhIubm5ObrA169fr8aNGzuSSEmKiorSzp07dfLkyRwfn0QSAAAgH8XGxsrf399piY2NvaZ9Llu2TL6+vvLy8tKUKVMUFxenEiVKSJISEhIUHBzs1N7Dw0OBgYFKSEhwtClVqpRTm4zXGW1ygru2AQAA8nH6n+HDh2vIkCFO6+x2+zXts1mzZoqPj9exY8c0a9YsPfLII9q4cWOmBDK/UZEEAADIR3a7XX5+fk7LtSaSPj4+Cg8P191336333ntPHh4eeu+99yRJISEhOnLkiFP71NRUnThxQiEhIY42hw8fdmqT8TqjTU6QSAIAAOTjGMnrIT093THusn79+jp16pQ2b97s2L5q1Sqlp6erXr16jjZr165VSkqKo01cXJwiIiJUvHjxHB+XRBIAAKAATf+TmJio+Ph4xcfHS5L27dun+Ph4HTx4UGfPntWLL76oDRs26MCBA9q8ebMef/xx/f3333r44YclSVWrVlXr1q3Vp08f/fTTT/rhhx80YMAAderUSaGhoZKkLl26yNPTU7169dJvv/2mRYsWadq0aZm64K+GMZIAAAAFyM8//6xmzZo5Xmckd9HR0XrnnXf0xx9/aN68eTp27JiCgoJ05513at26dapWrZrjPQsWLNCAAQPUokULubm5qWPHjnrjjTcc2/39/bVy5Ur1799fdevWVYkSJTRy5EinuSZzgnkkAQBAoZf21bv5tm/3Nr3zbd+uRtc2AAAALKFrGwAAwMJYRlCRBAAAgEVUJAEAAK7TND03G64aAAAALKEiCQAAwBhJS0gkAQAA3OiktYKrBgAAAEuoSAIAANC1bQkVSQAAAFhCRRIAAIDpfyzhqgEAAMASKpIAAACMkbSEiiQAAAAsoSIJAADAGElLSCQBAADc6Nq2gvQbAAAAllCRBAAAoGvbEq4aAAAALKEiCQAAwPQ/llCRBAAAgCVUJAEAABgjaQlXDQAAAJZQkQQAAIWejTGSlpBIAgAA0LVtCVcNAAAAllCRBAAAoCJpCVcNAAAAllCRBAAAcONmGyuoSAIAAMASKpIAAACMkbSEqwYAAABLqEgCAAAwIbklJJIAAAB0bVvCVQMAAIAlVCQBAADo2raEiiQAAAAsoSIJAADAGElLuGoAAACwhIokAAAAj0i0hIokAAAALKEiCQAAwBhJS7hqAAAAsISKJAAAAPNIWkIiCQAAQNe2JVw1AAAAWEJFEgAAgK5tS6hIAgAAwBIqkgAAAIyRtISrBgAAAEuoSAIAALhRW7OCqwYAAABLqEgCAIBCz8Zd25aQSAIAAHCzjSVcNQAAAFhCRRIAAICubUuoSAIAAMASKpIAAACMkbSEqwYAAABLqEgCAAAwRtISKpIAAACwhIokAAAAj0i0hEQSAACArm1LSL8BAABgCRVJAAAApv+xhKsGAAAAS6hIAgAAMEbSEiqSAAAAsISKJAAAgKhIWkFFEgAAAJZQkQQAAGCMpCUkkgAAACSSltC1DQAAAEuoSAIAAHCzjSVUJAEAAAqQtWvXql27dgoNDZXNZtOSJUsc21JSUjRs2DBVr15dPj4+Cg0NVffu3fXPP/847aN8+fKy2WxOy4QJE5zabNu2TY0aNZKXl5fKli2r1157LdexkkgCAADYbPm35NLZs2dVs2ZNvfXWW5m2nTt3Tlu2bNGIESO0ZcsWffrpp9q5c6fuv//+TG3HjBmjQ4cOOZaBAwc6tp05c0atWrVSWFiYNm/erIkTJyomJkYzZ87MVax0bQMAABQgbdq0UZs2bbLc5u/vr7i4OKd1b775pu666y4dPHhQ5cqVc6wvVqyYQkJCstzPggULlJycrNmzZ8vT01PVqlVTfHy8Jk+erL59++Y4ViqSAAAAtvxbkpKSdObMGaclKSkpz0I/ffq0bDabAgICnNZPmDBBQUFBql27tiZOnKjU1FTHtvXr16tx48by9PR0rIuKitLOnTt18uTJHB+bRBIAACAfxcbGyt/f32mJjY3Nk31fuHBBw4YNU+fOneXn5+dYP2jQIC1cuFCrV69Wv379NH78eD3//POO7QkJCSpVqpTTvjJeJyQk5Pj4dG0DAADk413bw4cP15AhQ5zW2e32a95vSkqKHnnkERljNGPGDKdtlx6vRo0a8vT0VL9+/RQbG5snx85AIgkAAJCPE5Lb7fY8Td6k/yWRBw4c0KpVq5yqkVmpV6+eUlNTtX//fkVERCgkJESHDx92apPxOrtxlVmhaxsAAOAGkpFE7t69W998842CgoKu+p74+Hi5ubkpODhYklS/fn2tXbtWKSkpjjZxcXGKiIhQ8eLFcxwLFUkAAIAC9IjExMRE7dmzx/F63759io+PV2BgoEqXLq2HHnpIW7Zs0bJly5SWluYY0xgYGChPT0+tX79eGzduVLNmzVSsWDGtX79egwcPVrdu3RxJYpcuXTR69Gj16tVLw4YN06+//qpp06ZpypQpuYrVZowxeXfqAAAANx6TsOfqjSyyhYTnqv2aNWvUrFmzTOujo6MVExOjChUqZPm+1atXq2nTptqyZYueeuop/fHHH0pKSlKFChX02GOPaciQIU5d7Nu2bVP//v21adMmlShRQgMHDtSwYcNyd24kkgAAoLAzCXvzbd+2kEr5tm9XY4wkAAAALGGMJAAAQAEaI3kjoSIJAAAAS6hIAgAA5OOE5DczEkkAAAC6ti2haxsAAACWUJEEAACgImkJFUkAAABYQkUSAACAm20soSIJAAAAS6hIAgCAQs/GGElLqEgCAADAEiqSAAAAVCQtIZEEAADgZhtL6NoGAACAJVQkAQAA6Nq2hIokAAAALKEiCQAAQEXSEiqSAAAAsISKJAAAAHdtW0JFEgAAAJZQkQQAAGCMpCUkkgAAAOSRltC1DQAAAEuoSAIAAFCStISKJAAAACyhIgkAAMDNNpZQkQQAAIAlVCQBAACoSFpCRRIAAACWUJEEAADgrm1LCkxFcu/evXr55ZfVuXNnHTlyRJL01Vdf6bfffnNxZAAAAMhKgUgkv/vuO1WvXl0bN27Up59+qsTEREnS1q1bNWrUKBdHBwAAbno2W/4tN7ECkUi+8MILeuWVVxQXFydPT0/H+ubNm2vDhg0ujAwAABQKJJKWFIhEcvv27XrggQcyrQ8ODtaxY8dcEBEAAACupkAkkgEBATp06FCm9b/88otuueUWF0QEAAAKF1s+LjevApFIdurUScOGDVNCQoJsNpvS09P1ww8/aOjQoerevburwwMAAEAWbMYY4+ogkpOT1b9/f82dO1dpaWny8PBQWlqaunTporlz58rd3d3VIQIAgJvZudP5t++i/vm3bxcrEIlkhoMHD+rXX39VYmKiateurcqVK1/1PUlJSUpKSnJaZ7fbZbfb8ytMAABwsyGRtKRAdG1nKFeunNq2batHHnkkR0mkJMXGxsrf399piY2NzedIgdxJSkpSTExMpn/0AAAucvn3ZFH//FtuYi6rSA4ZMiTHbSdPnpztNiqSuBGcOXNG/v7+On36tPz8/FwdDgAUOHxP3phc9ojEX375JUftbFeZf4mkEQAAwDVclkiuXr3aVYcGAABAHihQYyQBAABw43BZRfLBBx/U3Llz5efnpwceeOCKXdiffvrpdYwMyHt2u12jRo1iGAYAZIPvyRuTyxJJf39/R/IYEBAgm82mAjQTEZCn7Ha7YmJiXB0GABRYfE/emFw6j2RaWppef/11ff7550pOTlbz5s0VExMjb29vV4UEAACAHHLpGMnx48frxRdflK+vr2655Ra98cYb6t+/vytDAgAAQA65tCJZuXJlDR06VP369ZMkffPNN7r33nt1/vx5ublxHxAAAEBB5tJs7eDBg2rbtq3jdWRkpGw2m/755x8XRgUUDPv375fNZlN8fLwkac2aNbLZbDp16pRL4wKAvNKjRw916NDB8bpp06Z65plnXBYPcs+liWRqaqq8vLyc1hUpUkQpKSkuigiFTY8ePWSz2TRhwgSn9UuWLLnqZPjXKiNRzFiCgoLUqlUrx2T9ZcuW1aFDh3T77bfnaxwAkJ8yvmdtNps8PT0VHh6uMWPGKDU1VdOmTdPcuXNdHSKugcvu2pYkY4x69OjhdKv/hQsX9MQTT8jHx8exjul/kJ+8vLz06quvql+/fipevPh1P/4333yjatWq6b///a8GDRqkNm3a6I8//lBAQIBCQkKuezwAkNdat26tOXPmKCkpSV9++aX69++vIkWKaPjw4a4ODdfIpRXJ6OhoBQcHy9/f37F069ZNoaGhTuuA/BQZGamQkBDFxsZm2+aTTz5RtWrVZLfbVb58eU2aNMlpe/ny5TV+/Hg9/vjjKlasmMqVK6eZM2fm6PhBQUEKCQnRHXfcoddff12HDx/Wxo0bM3VtZ+X7779Xo0aN5O3trbJly2rQoEE6e/Zsjo4LANeL3W5XSEiIwsLC9OSTTyoyMlKff/55pq7tyyUlJWno0KG65ZZb5OPjo3r16mnNmjXXLW5cnUsrknPmzHHl4QFJkru7u8aPH68uXbpo0KBBKlOmjNP2zZs365FHHlFMTIweffRR/fjjj3rqqacUFBSkHj16ONpNmjRJY8eO1YsvvqiPP/5YTz75pJo0aaKIiIgcx5Ix9VVycvJV2+7du1etW7fWK6+8otmzZ+vo0aMaMGCABgwYwGcLQIHm7e2t48ePX7XdgAEDtGPHDi1cuFChoaH67LPP1Lp1a23fvl2VK1e+DpHiarg1GpD0wAMPqFatWho1alSmbZMnT1aLFi00YsQIValSRT169NCAAQM0ceJEp3Zt27bVU089pfDwcA0bNkwlSpTI1TPlT506pbFjx8rX11d33XXXVdvHxsaqa9eueuaZZ1S5cmU1aNBAb7zxht5//31duHAhx8cFgOvFGKNvvvlGK1asUPPmza/Y9uDBg5ozZ44++ugjNWrUSJUqVdLQoUN1zz338I/lAoREEvj/Xn31Vc2bN0+///670/rff/9dDRs2dFrXsGFD7d69W2lpaY51NWrUcPy3zWZTSEiIjhw5Iklq06aNfH195evrq2rVqjntq0GDBvL19VXx4sW1detWLVq0SKVKlbpqvFu3btXcuXMd+/X19VVUVJTS09O1b9++XJ8/AOSXZcuWydfXV15eXmrTpo0effTRqz7FZvv27UpLS1OVKlWcvue+++477d279/oEjqtyadc2UJA0btxYUVFRGj58uFOXdU4VKVLE6bXNZlN6erok6d1339X58+ezbLdo0SLddtttCgoKUkBAQI6Pl5iYqH79+mnQoEGZtpUrVy6X0QNA/mnWrJlmzJghT09PhYaGysPj6ulHYmKi3N3dtXnzZrm7uztt8/X1za9QkUskksAlJkyYoFq1ajmNa6xatap++OEHp3Y//PCDqlSpkunLLTu33HJLttvKli2rSpUq5TrWOnXqaMeOHQoPD8/1ewHgevLx8cn1d1Xt2rWVlpamI0eOqFGjRvkUGa4VXdvAJapXr66uXbvqjTfecKx79tln9e2332rs2LHatWuX5s2bpzfffFNDhw51YaTSsGHD9OOPP2rAgAGKj4/X7t27tXTpUg0YMMClcQFAXqhSpYq6du2q7t2769NPP9W+ffv0008/KTY2VsuXL3d1ePj/SCSBy4wZM8bRJS1drPwtXrxYCxcu1O23366RI0dqzJgxlrq/81KNGjX03XffadeuXWrUqJFq166tkSNHKjQ01KVxAUBemTNnjrp3765nn31WERER6tChgzZt2sTwnQLEpc/aBgAAwI2LiiQAAAAsIZEEAACAJSSSAAAAsIREEgAAAJaQSAIAAMASEkkAAABYQiIJAAAAS0gkAQAAYAmJJIA806NHD3Xo0MHxumnTpnrmmWeuexxr1qyRzWbTqVOn8u0Yl5+rFdcjTgDITySSwE2uR48estlsstls8vT0VHh4uMaMGaPU1NR8P/ann36qsWPH5qjt9U6qypcvr6lTp16XYwHAzcrD1QEAyH+tW7fWnDlzlJSUpC+//FL9+/dXkSJFNHz48Extk5OT5enpmSfHDQwMzJP9AAAKJiqSQCFgt9sVEhKisLAwPfnkk4qMjNTnn38u6X9dtOPGjVNoaKgiIiIkSX/99ZceeeQRBQQEKDAwUO3bt9f+/fsd+0xLS9OQIUMUEBCgoKAgPf/88zLGOB338q7tpKQkDRs2TGXLlpXdbld4eLjee+897d+/X82aNZMkFS9eXDabTT169JAkpaenKzY2VhUqVJC3t7dq1qypjz/+2Ok4X375papUqSJvb281a9bMKU4r0tLS1KtXL8cxIyIiNG3atCzbjh49WiVLlpSfn5+eeOIJJScnO7blJHYAuJFRkQQKIW9vbx0/ftzx+ttvv5Wfn5/i4uIkSSkpKYqKilL9+vW1bt06eXh46JVXXlHr1q21bds2eXp6atKkSZo7d65mz56tqlWratKkSfrss8/UvHnzbI/bvXt3rV+/Xm+88YZq1qypffv26dixYypbtqw++eQTdezYUTt37pSfn5+8vb0lSbGxsfq///s/vfPOO6pcubLWrl2rbt26qWTJkmrSpIn++usvPfjgg+rfv7/69u2rn3/+Wc8+++w1XZ/09HSVKVNGH330kYKCgvTjjz+qb9++Kl26tB555BGn6+bl5aU1a9Zo//796tmzp4KCgjRu3LgcxQ4ANzwD4KYWHR1t2rdvb4wxJj093cTFxRm73W6GDh3q2F6qVCmTlJTkeM/8+fNNRESESU9Pd6xLSkoy3t7eZsWKFcYYY0qXLm1ee+01x/aUlBRTpkwZx7GMMaZJkybm6aefNsYYs3PnTiPJxMXFZRnn6tWrjSRz8uRJx7oLFy6YokWLmh9//NGpba9evUznzp2NMcYMHz7c3HbbbU7bhw0blmlflwsLCzNTpkzJdvvl+vfvbzp27Oh4HR0dbQIDA83Zs2cd62bMmGF8fX1NWlpajmLP6pwB4EZCRRIoBJYtWyZfX1+lpKQoPT1dXbp0UUxMjGN79erVncZFbt26VXv27FGxYsWc9nPhwgXt3btXp0+f1qFDh1SvXj3HNg8PD91xxx2ZurczxMfHy93dPVeVuD179ujcuXNq2bKl0/rk5GTVrl1bkvT77787xSFJ9evXz/ExsvPWW29p9uzZOnjwoM6fP6/k5GTVqlXLqU3NmjVVtGhRp+MmJibqr7/+UmJi4lVjB4AbHYkkUAg0a9ZMM2bMkKenp0JDQ+Xh4fzR9/HxcXqdmJiounXrasGCBZn2VbJkSUsxZHRV50ZiYqIkafny5brlllucttntdktx5MTChQs1dOhQTZo0SfXr11exYsU0ceJEbdy4Mcf7cFXsAHA9kUgChYCPj4/Cw8Nz3L5OnTpatGiRgoOD5efnl2Wb0qVLa+PGjWrcuLEkKTU1VZs3b1adOnWybF+9enWlp6fru+++U2RkZKbtGRXRtLQ0x7rbbrtNdrtdBw8ezLaSWbVqVceNQxk2bNhw9ZO8gh9++EENGjTQU0895Vi3d+/eTO22bt2q8+fPO5LkDRs2yNfXV2XLllVgYOBVYweAGx13bQPIpGvXripRooTat2+vdevWad++fVqzZo0GDRqk//73v5Kkp59+WhMmTNCSJUv0xx9/6KmnnrriHJDly5dXdHS0Hn/8cS1ZssSxz8WLF0uSwsLCZLPZtGzZMh09elSJiYkqVqyYhg4dqsGDB2vevHnau3evtmzZounTp2vevHmSpCeeeEK7d+/Wc889p507d+qDDz7Q3Llzc3Sef//9t+Lj452WkydPqnLlyvr555+1YsUK7dq1SyNGjNCmTZsyvT85OVm9evXSjh079OWXX2rUqFEaMGCA3NzcchQ7ANzwXD1IE0D+uvRmm9xsP3TokOnevbspUaKEsdvtpmLFiqZPnz7m9OnTxpiLN9c8/fTTxs/PzwQEBJghQ4aY7t27Z3uzjTHGnD9/3gwePNiULl3aeHp6mvDwcDN79mzH9jFjxpiQkBBjs9lMdHS0MebiDUJTp041ERERpkiRIqZkyZImKirKfPfdd473ffHFFyY8PNzY7XbTqFEjM3v27BzdbCMp0zJ//nxz4cIF06NHD+Pv728CAgLMk08+aV544QVTs2bNTNdt5MiRJigoyPj6+po+ffqYCxcuONpcLXZutgFwo7MZk83IeAAAAOAK6NoGAACAJSSSAAAAsIREEgAAAJaQSAIAAMASEkkAAABYQiIJAAAAS0gkAQAAYAmJJAAAACwhkQQAAIAlJJIAAACwhEQSAAAAlvw/wZ4qaHMyIAQAAAAASUVORK5CYII=\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Results saved:\n", "  Training history: dgcnn_fair_comparison_training_history_20250809_141613.json\n", "  Predictions: dgcnn_fair_comparison_rcps_predictions_20250809_141613.csv\n", "  Model: dgcnn_fair_comparison_best_model.pth\n", "\n", "=== DGCNN FAIR COMPARISON COMPLETE ===\n", "DGCNN (Graph Neural Networks): 100.00% accuracy\n", "Classical ML (22 Features): TBD% accuracy (from their results)\n", "SimplePointNet (Raw Coordinates): TBD% accuracy (from other notebook)\n", "\n", "🎯 Research Question Answered:\n", "   Can graph-based deep learning match classical ML using raw coordinates vs engineered features?\n", "   Answer: DGCNN achieved 100.00% vs Classical ML's performance\n", "\n", "✅ FAIR COMPARISON ACHIEVED:\n", "   ✅ Same point clouds (LAS files)\n", "   ✅ Same ground truth (Buffer_2m.kml)\n", "   ✅ Same validation protocol (RES→RCPS)\n", "   ✅ Same patch extraction (3m radius)\n", "   📊 Different approaches: Graph networks vs Statistical features\n"]}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/dgcnn_fair_comparison_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== DGCNN FAIR CROSS-SITE EVALUATION (RES→RCPS) ===\")\n", "print(\"Testing DGCNN trained on RES data on RCPS data...\")\n", "print(\"SAME validation protocol as Classical ML\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nDGCNN Cross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "\n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Reds',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('DGCNN Fair Comparison Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'k_neighbors': 20,\n", "    'data_source': 'Buffer_2m.kml_same_as_classical_ml',\n", "    'validation_protocol': 'RES_to_RCPS_same_as_classical_ml',\n", "    'patch_radius': '3.0m_same_as_classical_ml'\n", "}\n", "\n", "with open(f'{models_path}/dgcnn_fair_comparison_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/dgcnn_fair_comparison_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: dgcnn_fair_comparison_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: dgcnn_fair_comparison_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: dgcnn_fair_comparison_best_model.pth\")\n", "\n", "print(\"\\n=== DGCNN FAIR COMPARISON COMPLETE ===\")\n", "print(f\"DGCNN (Graph Neural Networks): {test_acc:.2f}% accuracy\")\n", "print(f\"Classical ML (22 Features): TBD% accuracy (from their results)\")\n", "print(f\"SimplePointNet (Raw Coordinates): TBD% accuracy (from other notebook)\")\n", "print(f\"\\n🎯 Research Question Answered:\")\n", "print(f\"   Can graph-based deep learning match classical ML using raw coordinates vs engineered features?\")\n", "print(f\"   Answer: DGCNN achieved {test_acc:.2f}% vs Classical ML's performance\")\n", "print(f\"\\n✅ FAIR COMPARISON ACHIEVED:\")\n", "print(f\"   ✅ Same point clouds (LAS files)\")\n", "print(f\"   ✅ Same ground truth (Buffer_2m.kml)\")\n", "print(f\"   ✅ Same validation protocol (RES→RCPS)\")\n", "print(f\"   ✅ Same patch extraction (3m radius)\")\n", "print(f\"   📊 Different approaches: Graph networks vs Statistical features\")"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4", "machine_shape": "hm"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}