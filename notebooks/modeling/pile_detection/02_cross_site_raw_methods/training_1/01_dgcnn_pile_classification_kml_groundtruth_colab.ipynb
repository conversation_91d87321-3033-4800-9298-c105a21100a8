{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# DGCNN Cross-Site Pile Detection with Buffer KML Ground Truth (RES→RCPS)", "", "This notebook implements DGCNN for **true cross-site generalization** using **Buffer KML ground truth**:", "- **Train on**: Nortan RES site data with Buffer KML pile locations", "- **Test on**: Althea RCPS site data with Buffer KML pile locations", "- **Goal**: Fair comparison using same independent ground truth", "", "**Key Features:**", "- ✅ **Uses Buffer KML files** as ground truth (polygon centroids)", "- ✅ **Fair comparison** - all methods validated against same independent data", "- ✅ **Research integrity** - no circular validation", "", "**Architecture:**", "- DGCNN with EdgeConv layers", "- Input: (N, 3, 256) - 3D coordinates only", "- Binary classification (pile vs non-pile)", "- Cross-site validation protocol", "", "**Author**: <PERSON><PERSON><PERSON>", "**Date**: August 2025", "**Project**: Fair Cross-Site Construction AI Comparison"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mount_drive"}, "outputs": [], "source": ["from google.colab import drive", "drive.mount('/content/drive')", "", "# Project paths", "GDRIVE_BASE = \"/content/drive/MyDrive\"", "PROJECT_FOLDER = \"pointnet_pile_detection\"", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"", "data_path = f\"{project_path}/data\"", "models_path = f\"{project_path}/models\"", "", "print(f\"Project path: {project_path}\")", "print(f\"Data path: {data_path}\")", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "install_deps"}, "outputs": [], "source": ["# Install required packages", "!pip install laspy geopandas scikit-learn mlflow", "", "import numpy as np", "import pandas as pd", "import torch", "import torch.nn as nn", "import torch.optim as optim", "import torch.nn.functional as F", "from torch.utils.data import Dataset, DataLoader", "import laspy", "import geopandas as gpd", "from scipy.spatial import cKDTree", "from sklearn.metrics import accuracy_score, f1_score, classification_report", "import matplotlib.pyplot as plt", "from pathlib import Path", "from datetime import datetime", "import warnings", "warnings.filterwarnings('ignore')", "", "# Set random seeds", "np.random.seed(42)", "torch.manual_seed(42)", "if torch.cuda.is_available():", "    torch.cuda.manual_seed(42)", "", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')", "print(f'Using device: {device}')"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## Buffer KML Data Loading Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):", "    \"\"\"Load point cloud from LAS file\"\"\"", "    print(f'Loading point cloud: {las_path}')", "    las_file = laspy.read(las_path)", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T", "    print(f'  Loaded {len(points):,} points')", "    print(f'  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]')", "    return points", "", "def load_pile_locations_from_kml(kml_path, target_crs, site_name):", "    \"\"\"Load pile locations from Buffer KML ground truth\"\"\"", "    print(f'Loading Buffer KML ground truth: {kml_path}')", "    ", "    try:", "        # Load KML file", "        gdf = gpd.read_file(kml_path, driver='KML')", "        print(f'  Loaded {len(gdf)} features from KML')", "        ", "        # Convert to target CRS", "        gdf = gdf.to_crs(target_crs)", "        ", "        # Extract coordinates from polygon centroids (Buffer KML contains polygons)", "        pile_coords = []", "        for geom in gdf.geometry:", "            if hasattr(geom, 'centroid'):", "                # For polygons (Buffer KML), get centroid", "                centroid = geom.centroid", "                pile_coords.append([centroid.x, centroid.y])", "            <PERSON><PERSON> hasattr(geom, 'x') and hasattr(geom, 'y'):", "                # For points, get coordinates directly", "                pile_coords.append([geom.x, geom.y])", "            else:", "                print(f'  Warning: Unknown geometry type: {type(geom)}')", "        ", "        pile_coords = np.array(pile_coords)", "        print(f'  Extracted {len(pile_coords)} pile coordinates from centroids')", "        print(f'  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]')", "        ", "        return pile_coords", "        ", "    except Exception as e:", "        print(f'  Error loading Buffer KML: {e}')", "        print(f'  Make sure Buffer_2m.kml files exist in the data directories')", "        print(f'  Expected path: {kml_path}')", "        raise e", "", "def extract_patches_for_dgcnn(points, pile_coords, site_name, patch_radius=10.0, min_points=20):", "    \"\"\"Extract smaller patches optimized for DGCNN (256 points)\"\"\"", "    print(f'Extracting DGCNN patches for {site_name}:')", "    print(f'  Patch radius: {patch_radius}m (smaller for DGCNN)')", "    print(f'  Minimum points per patch: {min_points}')", "    ", "    kdtree = cKDTree(points[:, :2])", "    positive_patches = []", "    ", "    # Extract positive patches around known pile locations", "    for i, (pile_x, pile_y) in enumerate(pile_coords):", "        if i % 100 == 0:", "            print(f'  Processing pile {i+1}/{len(pile_coords)}')", "        ", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)", "        if len(indices) >= min_points:", "            patch_points = points[indices]", "            # Center the patch around pile location", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])", "            positive_patches.append(centered_patch)", "    ", "    print(f'  Extracted {len(positive_patches)} positive patches')", "    ", "    # Extract negative patches", "    negative_patches = []", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()", "    ", "    target_negatives = len(positive_patches)", "    attempts = 0", "    max_attempts = target_negatives * 10", "    ", "    print(f'  Extracting {target_negatives} negative patches...')", "    ", "    while len(negative_patches) < target_negatives and attempts < max_attempts:", "        rand_x = np.random.uniform(x_min, x_max)", "        rand_y = np.random.uniform(y_min, y_max)", "        ", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)", "        ", "        if distances.min() > patch_radius * 2.0:", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)", "            if len(indices) >= min_points:", "                patch_points = points[indices]", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])", "                negative_patches.append(centered_patch)", "        ", "        attempts += 1", "        ", "        if attempts % 1000 == 0:", "            print(f'    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})')", "    ", "    print(f'  Extracted {len(negative_patches)} negative patches')", "    print(f'  Total patches: {len(positive_patches) + len(negative_patches)}')", "    ", "    return positive_patches, negative_patches"]}, {"cell_type": "markdown", "metadata": {"id": "load_data"}, "source": ["## Load Data with <PERSON><PERSON><PERSON> KML Ground Truth"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_data_main"}, "outputs": [], "source": ["# Load RES data (training site) with Buffer KML ground truth", "print('=== LOADING RES DATA (TRAINING SITE) WITH BUFFER KML GROUND TRUTH ===')", "res_points = load_site_point_cloud(f'{data_path}/nortan_res/Block_11_2m.las')", "res_pile_coords = load_pile_locations_from_kml(", "    f'{data_path}/nortan_res/Buffer_2m.kml',  # Buffer KML ground truth", "    'EPSG:32614',  # UTM Zone 14N", "    'nortan_res'", ")", "", "# Extract RES patches (smaller for DGCNN)", "res_pos_patches, res_neg_patches = extract_patches_for_dgcnn(", "    res_points, res_pile_coords, 'nortan_res', patch_radius=10.0, min_points=20", ")", "", "print('=== LOADING RCPS DATA (TEST SITE) WITH BUFFER KML GROUND TRUTH ===')", "rcps_points = load_site_point_cloud(f'{data_path}/althea_rpcs/Point_Cloud.las')", "rcps_pile_coords = load_pile_locations_from_kml(", "    f'{data_path}/althea_rpcs/Buffer_2m.kml',  # Buffer KML ground truth", "    'EPSG:32615',  # UTM Zone 15N", "    'althea_rcps'", ")", "", "# Extract RCPS patches (smaller for DGCNN)", "rcps_pos_patches, rcps_neg_patches = extract_patches_for_dgcnn(", "    rcps_points, rcps_pile_coords, 'althea_rcps', patch_radius=10.0, min_points=20", ")", "", "print('=== DATA SUMMARY (BUFFER KML GROUND TRUTH) ===')", "print(f'RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative')", "print(f'RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative')", "print(f'Total training samples: {len(res_pos_patches) + len(res_neg_patches)}')", "print(f'Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}')", "print(f'Note: Using Buffer KML ground truth (not Classical ML results)')", "print(f'This ensures fair comparison across all methods.')"]}, {"cell_type": "markdown", "metadata": {"id": "model_definition"}, "source": ["## DGCNN Model Definition"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "dgcnn_model"}, "outputs": [], "source": ["class EdgeConv(nn.Module):", "    def __init__(self, in_channels, out_channels, k=20):", "        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()", "        self.k = k", "        self.conv = nn.Sequential(", "            nn.Conv2d(in_channels * 2, out_channels, kernel_size=1, bias=False),", "            nn.BatchNorm2d(out_channels),", "            nn.LeakyReLU(negative_slope=0.2)", "        )", "    ", "    def knn(self, x, k):", "        inner = -2 * torch.matmul(x.transpose(2, 1), x)", "        xx = torch.sum(x**2, dim=1, keepdim=True)", "        pairwise_distance = -xx - inner - xx.transpose(2, 1)", "        idx = pairwise_distance.topk(k=k, dim=-1)[1]", "        return idx", "    ", "    def get_graph_feature(self, x, k, idx=None):", "        batch_size = x.size(0)", "        num_points = x.size(2)", "        x = x.view(batch_size, -1, num_points)", "        ", "        if idx is None:", "            idx = self.knn(x, k=k)", "        ", "        device = torch.device('cuda')", "        idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1) * num_points", "        idx = idx + idx_base", "        idx = idx.view(-1)", "        ", "        _, num_dims, _ = x.size()", "        x = x.transpose(2, 1).contiguous()", "        feature = x.view(batch_size * num_points, -1)[idx, :]", "        feature = feature.view(batch_size, num_points, k, num_dims)", "        x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)", "        ", "        feature = torch.cat((feature - x, x), dim=3).permute(0, 3, 1, 2).contiguous()", "        return feature", "    ", "    def forward(self, x):", "        x = self.get_graph_feature(x, k=self.k)", "        x = self.conv(x)", "        x = x.max(dim=-1, keepdim=False)[0]", "        return x", "", "class DGCNN(nn.Module):", "    def __init__(self, num_classes=2, k=20):", "        super(DGC<PERSON><PERSON>, self).__init__()", "        self.k = k", "        ", "        self.edge_conv1 = EdgeConv(3, 64, k)", "        self.edge_conv2 = EdgeConv(64, 64, k)", "        self.edge_conv3 = EdgeConv(64, 128, k)", "        self.edge_conv4 = EdgeConv(128, 256, k)", "        ", "        self.conv5 = nn.Sequential(", "            nn.Conv1d(512, 1024, kernel_size=1, bias=False),", "            nn.<PERSON>ch<PERSON>orm1d(1024),", "            nn.LeakyReLU(negative_slope=0.2)", "        )", "        ", "        self.classifier = nn.Sequential(", "            nn.<PERSON>(1024, 512),", "            nn.BatchNorm1d(512),", "            nn.LeakyReLU(negative_slope=0.2),", "            nn.Dropout(0.5),", "            nn.<PERSON><PERSON>(512, 256),", "            nn.BatchNorm1d(256),", "            nn.LeakyReLU(negative_slope=0.2),", "            nn.Dropout(0.5),", "            nn.Linear(256, num_classes)", "        )", "    ", "    def forward(self, x):", "        x1 = self.edge_conv1(x)", "        x2 = self.edge_conv2(x1)", "        x3 = self.edge_conv3(x2)", "        x4 = self.edge_conv4(x3)", "        ", "        x = torch.cat((x1, x2, x3, x4), dim=1)", "        x = self.conv5(x)", "        x = F.adaptive_max_pool1d(x, 1).view(x.size(0), -1)", "        x = self.classifier(x)", "        return x", "", "print('DGCNN model defined')", "print(f'Using device: {device}')"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_data"}, "source": ["## Prepare Data for Training"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "prepare_datasets"}, "outputs": [], "source": ["class DGCNNDataset(Dataset):", "    def __init__(self, patches, labels):", "        # DGCNN expects (batch_size, 3, num_points)", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 256)", "        self.labels = torch.LongTensor(labels)", "    ", "    def __len__(self):", "        return len(self.patches)", "    ", "    def __getitem__(self, idx):", "        return self.patches[idx], self.labels[idx]", "", "# Resample all patches to 256 points for DGCNN", "def resample_patch_to_256(patch):", "    if len(patch) == 0:", "        return np.zeros((256, 3))", "    ", "    if len(patch) >= 256:", "        indices = np.random.choice(len(patch), 256, replace=False)", "        return patch[indices]", "    else:", "        # Upsample with repetition", "        indices = np.random.choice(len(patch), 256, replace=True)", "        return patch[indices]", "", "print('Resampling patches to 256 points for DGCNN...')", "", "# Training data (RES)", "train_patches = []", "train_labels = []", "", "# Positive patches", "for patch in res_pos_patches:", "    resampled = resample_patch_to_256(patch)", "    train_patches.append(resampled)", "    train_labels.append(1)", "", "# Negative patches", "for patch in res_neg_patches:", "    resampled = resample_patch_to_256(patch)", "    train_patches.append(resampled)", "    train_labels.append(0)", "", "# Test data (RCPS)", "test_patches = []", "test_labels = []", "", "# Positive patches", "for patch in rcps_pos_patches:", "    resampled = resample_patch_to_256(patch)", "    test_patches.append(resampled)", "    test_labels.append(1)", "", "# Negative patches", "for patch in rcps_neg_patches:", "    resampled = resample_patch_to_256(patch)", "    test_patches.append(resampled)", "    test_labels.append(0)", "", "# Convert to numpy arrays", "train_patches = np.array(train_patches, dtype=np.float32)", "train_labels = np.array(train_labels, dtype=np.int64)", "test_patches = np.array(test_patches, dtype=np.float32)", "test_labels = np.array(test_labels, dtype=np.int64)", "", "print(f'Training: {train_patches.shape}, labels: {train_labels.shape}')", "print(f'Testing: {test_patches.shape}, labels: {test_labels.shape}')", "", "# Create datasets and data loaders", "train_dataset = DGCNNDataset(train_patches, train_labels)", "test_dataset = DGCNNDataset(test_patches, test_labels)", "", "batch_size = 8  # Smaller batch size for DGCNN", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)", "", "print(f'Data loaders created with batch size: {batch_size}')"]}, {"cell_type": "markdown", "metadata": {"id": "train_model"}, "source": ["## Train DGCNN Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "training_loop"}, "outputs": [], "source": ["# Initialize model", "model = DGCNN(num_classes=2, k=20).to(device)", "print(f'Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters')", "", "# Training configuration", "criterion = nn.CrossEntropyLoss()", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)", "", "# Training parameters", "num_epochs = 50", "best_test_acc = 0.0", "train_losses = []", "test_accs = []", "", "print(f'Training configuration:')", "print(f'  Epochs: {num_epochs}')", "print(f'  Learning rate: 0.001')", "print(f'  Weight decay: 1e-4')", "print(f'  Device: {device}')", "", "# Training loop", "for epoch in range(num_epochs):", "    # Training phase", "    model.train()", "    running_loss = 0.0", "    correct_train = 0", "    total_train = 0", "    ", "    for batch_idx, (data, target) in enumerate(train_loader):", "        data, target = data.to(device), target.to(device)", "        ", "        optimizer.zero_grad()", "        output = model(data)", "        loss = criterion(output, target)", "        loss.backward()", "        optimizer.step()", "        ", "        running_loss += loss.item()", "        pred = output.argmax(dim=1, keepdim=True)", "        correct_train += pred.eq(target.view_as(pred)).sum().item()", "        total_train += target.size(0)", "        ", "        if batch_idx % 10 == 0:", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')", "    ", "    # Testing phase", "    model.eval()", "    correct_test = 0", "    total_test = 0", "    ", "    with torch.no_grad():", "        for data, target in test_loader:", "            data, target = data.to(device), target.to(device)", "            output = model(data)", "            pred = output.argmax(dim=1, keepdim=True)", "            correct_test += pred.eq(target.view_as(pred)).sum().item()", "            total_test += target.size(0)", "    ", "    train_acc = 100. * correct_train / total_train", "    test_acc = 100. * correct_test / total_test", "    avg_loss = running_loss / len(train_loader)", "    ", "    train_losses.append(avg_loss)", "    test_accs.append(test_acc)", "    ", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {avg_loss:.4f}, Train Acc: {train_acc:.2f}%, Test Acc: {test_acc:.2f}%')", "    ", "    # Save best model", "    if test_acc > best_test_acc:", "        best_test_acc = test_acc", "        torch.save(model.state_dict(), f'{models_path}/dgcnn_kml_best_model.pth')", "        print(f'  New best model saved! Test accuracy: {test_acc:.2f}%')", "    ", "    scheduler.step()", "", "print(f'Training completed!')", "print(f'Best test accuracy: {best_test_acc:.2f}%')"]}, {"cell_type": "markdown", "metadata": {"id": "save_results"}, "source": ["## Save Predictions to CSV"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_predictions"}, "outputs": [], "source": ["# SAVE PREDICTIONS TO CSV", "print('=== SAVING PREDICTIONS TO CSV ===')", "", "# Create timestamp for unique filenames", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')", "", "# Create models directory if it doesn't exist", "import os", "os.makedirs(models_path, exist_ok=True)", "", "# Get predictions and probabilities for RCPS test data", "model.eval()", "all_predictions = []", "all_probabilities = []", "", "with torch.no_grad():", "    for batch_data, _ in test_loader:", "        batch_data = batch_data.to(device)", "        outputs = model(batch_data)", "        probabilities = torch.softmax(outputs, dim=1)", "        predictions = torch.argmax(outputs, dim=1)", "        ", "        all_predictions.extend(predictions.cpu().numpy())", "        all_probabilities.extend(probabilities.cpu().numpy())", "", "# Create results DataFrame", "results_df = pd.DataFrame({", "    'patch_id': range(len(all_predictions)),", "    'predicted_label': all_predictions,", "    'pile_probability': [prob[1] for prob in all_probabilities],", "    'non_pile_probability': [prob[0] for prob in all_probabilities]", "})", "", "results_df.to_csv(f'{models_path}/dgcnn_kml_rcps_predictions_{timestamp}.csv', index=False)", "", "print(f'Results saved:')", "print(f'  Predictions: dgcnn_kml_rcps_predictions_{timestamp}.csv')", "print(f'  Model: dgcnn_kml_best_model.pth')", "", "print('=== DGCNN BUFFER KML GROUND TRUTH TRAINING COMPLETE ===')", "print(f'Successfully trained on RES Buffer KML data and tested on RCPS Buffer KML data')", "print(f'Cross-site generalization accuracy: {best_test_acc:.2f}%')"]}, {"cell_type": "markdown", "metadata": {"id": "summary"}, "source": ["## Summary", "", "This notebook successfully implemented DGCNN for pile detection with:", "- **Architecture**: DGCNN with EdgeConv layers", "- **Patch radius**: 10.0 meters", "- **Points per patch**: 256 points", "- **Training site**: RES (Nortan)", "- **Testing site**: RCPS (Althea)", "- **Ground truth**: Buffer KML files (polygon centroids)", "", "Results are saved to Google Drive for further analysis and comparison with other methods."]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}