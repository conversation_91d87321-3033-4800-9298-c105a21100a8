{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# DGCNN Cross-Site Pile Detection (RES→RCPS)\n", "\n", "This notebook implements DGCNN for **true cross-site generalization**:\n", "- **Train on**: Nortan RES site data\n", "- **Test on**: Althea RCPS site data\n", "- **Goal**: Test if DGCNN can generalize across construction sites\n", "\n", "**Architecture:**\n", "- DGCNN with EdgeConv layers\n", "- Input: (N, 256, 3) - 3D coordinates (simplified from 20 features)\n", "- Binary classification (pile vs non-pile)\n", "- Cross-site validation protocol\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Project**: Cross-Site Construction AI Generalization\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "mount_drive", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "efd41d51-b219-4cf2-99ba-6209d0203df7"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "install_deps", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "07a144a5-504f-46b0-8066-71128c01cb25"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.43)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.63.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.9)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.4)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m72.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m84.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m63.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m23.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m14.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m10.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m8.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.63.0-py3-none-any.whl (688 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m688.0/688.0 kB\u001b[0m \u001b[31m50.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m15.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m6.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m11.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m17.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.63.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## RES/RCPS Data Loading (Same as PointNet++)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"  Loaded {len(points):,} points\")\n", "    print(f\"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_pile_locations_from_csv(csv_path, site_name):\n", "    \"\"\"Load pile locations from Classical ML results CSV\"\"\"\n", "    print(f\"Loading pile locations from: {csv_path}\")\n", "    df = pd.read_csv(csv_path)\n", "\n", "    # Extract coordinates based on CSV format\n", "    if 'utm_x' in df.columns and 'utm_y' in df.columns:\n", "        pile_coords = df[['utm_x', 'utm_y']].values\n", "    elif 'x' in df.columns and 'y' in df.columns:\n", "        pile_coords = df[['x', 'y']].values\n", "    else:\n", "        raise ValueError(f\"Could not find coordinate columns in {csv_path}\")\n", "\n", "    print(f\"  Loaded {len(pile_coords)} pile locations for {site_name}\")\n", "    print(f\"  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]\")\n", "    return pile_coords\n", "\n", "def extract_patches_for_dgcnn(points, pile_coords, site_name, patch_radius=10.0, min_points=20):\n", "    \"\"\"Extract smaller patches optimized for DGCNN (256 points)\"\"\"\n", "    print(f\"\\nExtracting DGCNN patches for {site_name}:\")\n", "    print(f\"  Patch radius: {patch_radius}m (smaller for DGCNN)\")\n", "    print(f\"  Minimum points per patch: {min_points}\")\n", "\n", "    kdtree = cKDTree(points[:, :2])\n", "    positive_patches = []\n", "\n", "    # Extract positive patches around known pile locations\n", "    for i, (pile_x, pile_y) in enumerate(pile_coords):\n", "        if i % 100 == 0:\n", "            print(f\"  Processing pile {i+1}/{len(pile_coords)}\")\n", "\n", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            # Center the patch around pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            positive_patches.append(centered_patch)\n", "\n", "    print(f\"  Extracted {len(positive_patches)} positive patches\")\n", "\n", "    # Extract negative patches\n", "    negative_patches = []\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    target_negatives = len(positive_patches)\n", "    attempts = 0\n", "    max_attempts = target_negatives * 10\n", "\n", "    print(f\"  Extracting {target_negatives} negative patches...\")\n", "\n", "    while len(negative_patches) < target_negatives and attempts < max_attempts:\n", "        rand_x = np.random.uniform(x_min, x_max)\n", "        rand_y = np.random.uniform(y_min, y_max)\n", "\n", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)\n", "\n", "        if distances.min() > patch_radius * 2.0:\n", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)\n", "            if len(indices) >= min_points:\n", "                patch_points = points[indices]\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                negative_patches.append(centered_patch)\n", "\n", "        attempts += 1\n", "\n", "        if attempts % 1000 == 0:\n", "            print(f\"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})\")\n", "\n", "    print(f\"  Extracted {len(negative_patches)} negative patches\")\n", "    print(f\"  Total patches: {len(positive_patches) + len(negative_patches)}\")\n", "\n", "    return positive_patches, negative_patches\n", "\n", "def resample_patch_for_dgcnn(patch, target_points=256):\n", "    \"\"\"Resample patch to fixed size for DGCNN (256 points)\"\"\"\n", "    if len(patch) == 0:\n", "        return np.zeros((target_points, 3))\n", "\n", "    if len(patch) >= target_points:\n", "        # Downsample\n", "        indices = np.random.choice(len(patch), target_points, replace=False)\n", "        resampled = patch[indices]\n", "    else:\n", "        # Upsample with noise\n", "        extra_needed = target_points - len(patch)\n", "        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)\n", "        extra_points = patch[extra_indices] + np.random.normal(0, 0.005, (extra_needed, 3))  # Smaller noise\n", "        resampled = np.vstack([patch, extra_points])\n", "\n", "    return resampled.astype(np.float32)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "load_data_main", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7b4950de-6aa2-4316-9ae4-9c631056937a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== LOADING RES DATA (TRAINING SITE) ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "  Loaded 35,565,352 points\n", "  Bounds: X[385724.0, 385809.6], Y[3529182.8, 3529447.0], Z[553.4, 556.3]\n", "Loading pile locations from: /content/drive/MyDrive/pointnet_pile_detection/data/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv\n", "  Loaded 368 pile locations for nortan_res\n", "  Bounds: X[385725.9, 385807.6], Y[3529184.8, 3529445.0]\n", "\n", "Extracting DGCNN patches for nortan_res:\n", "  Patch radius: 10.0m (smaller for DGCNN)\n", "  Minimum points per patch: 20\n", "  Processing pile 1/368\n", "  Processing pile 101/368\n", "  Processing pile 201/368\n", "  Processing pile 301/368\n", "  Extracted 368 positive patches\n", "  Extracting 368 negative patches...\n", "    Negative patches: 0/368 (attempts: 1000)\n", "    Negative patches: 0/368 (attempts: 2000)\n", "    Negative patches: 0/368 (attempts: 3000)\n", "  Extracted 0 negative patches\n", "  Total patches: 368\n", "\n", "=== LOADING RCPS DATA (TEST SITE) ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "  Loaded 52,862,386 points\n", "  Bounds: X[599595.2, 599866.2], Y[4334366.6, 4334660.8], Z[238.6, 259.2]\n", "Loading pile locations from: /content/drive/MyDrive/pointnet_pile_detection/data/ground_truth/rcps_generalization_results_20250807_221320.csv\n", "  Loaded 1359 pile locations for althea_rcps\n", "  Bounds: X[599597.2, 599864.2], Y[4334368.6, 4334658.8]\n", "\n", "Extracting DGCNN patches for althea_rcps:\n", "  Patch radius: 10.0m (smaller for DGCNN)\n", "  Minimum points per patch: 20\n", "  Processing pile 1/1359\n", "  Processing pile 101/1359\n", "  Processing pile 201/1359\n", "  Processing pile 301/1359\n", "  Processing pile 401/1359\n", "  Processing pile 501/1359\n", "  Processing pile 601/1359\n", "  Processing pile 701/1359\n", "  Processing pile 801/1359\n", "  Processing pile 901/1359\n", "  Processing pile 1001/1359\n", "  Processing pile 1101/1359\n", "  Processing pile 1201/1359\n", "  Processing pile 1301/1359\n", "  Extracted 1359 positive patches\n", "  Extracting 1359 negative patches...\n", "    Negative patches: 0/1359 (attempts: 1000)\n", "    Negative patches: 0/1359 (attempts: 2000)\n", "    Negative patches: 0/1359 (attempts: 3000)\n", "    Negative patches: 0/1359 (attempts: 4000)\n", "    Negative patches: 0/1359 (attempts: 5000)\n", "    Negative patches: 0/1359 (attempts: 6000)\n", "    Negative patches: 0/1359 (attempts: 7000)\n", "    Negative patches: 0/1359 (attempts: 8000)\n", "    Negative patches: 0/1359 (attempts: 9000)\n", "    Negative patches: 0/1359 (attempts: 10000)\n", "    Negative patches: 0/1359 (attempts: 11000)\n", "    Negative patches: 0/1359 (attempts: 12000)\n", "    Negative patches: 0/1359 (attempts: 13000)\n", "  Extracted 0 negative patches\n", "  Total patches: 1359\n", "\n", "=== DATA SUMMARY ===\n", "RES (training): 368 positive, 0 negative\n", "RCPS (testing): 1359 positive, 0 negative\n", "Total training samples: 368\n", "Total test samples: 1359\n"]}], "source": ["# Load RES data (training site)\n", "print(\"=== LOADING RES DATA (TRAINING SITE) ===\")\n", "res_points = load_site_point_cloud(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv\",\n", "    \"nortan_res\"\n", ")\n", "\n", "# Extract RES patches (smaller for DGCNN)\n", "res_pos_patches, res_neg_patches = extract_patches_for_dgcnn(\n", "    res_points, res_pile_coords, \"nortan_res\", patch_radius=10.0, min_points=20\n", ")\n", "\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) ===\")\n", "rcps_points = load_site_point_cloud(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_pile_coords = load_pile_locations_from_csv(\n", "    f\"{data_path}/ground_truth/rcps_generalization_results_20250807_221320.csv\",\n", "    \"althea_rcps\"\n", ")\n", "\n", "# Extract RCPS patches (smaller for DGCNN)\n", "rcps_pos_patches, rcps_neg_patches = extract_patches_for_dgcnn(\n", "    rcps_points, rcps_pile_coords, \"althea_rcps\", patch_radius=10.0, min_points=20\n", ")\n", "\n", "print(\"\\n=== DATA SUMMARY ===\")\n", "print(f\"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative\")\n", "print(f\"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative\")\n", "print(f\"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}\")\n", "print(f\"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for DGCNN"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "create_datasets", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "55cba273-6a53-495c-d23f-98792966ff57"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Resampling patches to 256 points for DGCNN...\n", "\n", "Final dataset shapes:\n", "Training: (368, 256, 3), labels: (368,)\n", "Testing: (1359, 256, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n"]}], "source": ["# Resample all patches to fixed size (256 points for DGCNN)\n", "print(\"Resampling patches to 256 points for DGCNN...\")\n", "\n", "# Training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Positive patches\n", "for patch in res_pos_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    train_patches.append(resampled)\n", "    train_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in res_neg_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    train_patches.append(resampled)\n", "    train_labels.append(0)\n", "\n", "# Test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Positive patches\n", "for patch in rcps_pos_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    test_patches.append(resampled)\n", "    test_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in rcps_neg_patches:\n", "    resampled = resample_patch_for_dgcnn(patch, 256)\n", "    test_patches.append(resampled)\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 256, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 256, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "dgcnn_architecture"}, "source": ["## DGCNN Architecture"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "dgcnn_model"}, "outputs": [], "source": ["def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Construct edge features\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "\n", "    if idx is None:\n", "        idx = knn(x, k=k)   # (batch_size, num_points, k)\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "\n", "    idx = idx + idx_base\n", "\n", "    idx = idx.view(-1)\n", "\n", "    _, num_dims, _ = x.size()\n", "\n", "    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "\n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "\n", "    return feature      # (batch_size, 2*num_dims, num_points, k)\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20, dropout=0.5):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.<PERSON>chNorm1d(1024)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "\n", "        # Classification head\n", "        self.linear1 = nn.Linear(1024*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=dropout)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=dropout)\n", "        self.linear3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # EdgeConv layers\n", "        x = get_graph_feature(x, k=self.k)      # (batch_size, 3, num_points) -> (batch_size, 3*2, num_points, k)\n", "        x = self.conv1(x)                       # (batch_size, 3*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x1, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv2(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x2, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv3(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 128, num_points, k)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 128, num_points, k) -> (batch_size, 128, num_points)\n", "\n", "        x = get_graph_feature(x3, k=self.k)     # (batch_size, 128, num_points) -> (batch_size, 128*2, num_points, k)\n", "        x = self.conv4(x)                       # (batch_size, 128*2, num_points, k) -> (batch_size, 256, num_points, k)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 256, num_points, k) -> (batch_size, 256, num_points)\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (batch_size, 64+64+128+256, num_points)\n", "\n", "        x = self.conv5(x)                       # (batch_size, 512, num_points) -> (batch_size, 1024, num_points)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x = torch.cat((x1, x2), 1)              # (batch_size, 1024*2)\n", "\n", "        # Classification\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2) # (batch_size, 1024*2) -> (batch_size, 512)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2) # (batch_size, 512) -> (batch_size, 256)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)                                             # (batch_size, 256) -> (batch_size, num_classes)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "create_dataset_class", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "27501cf5-d358-417e-e646-415a8ac2b687"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 8\n", "  Input shape: (batch_size, 3, 256) for DGCNN\n"]}], "source": ["class CrossSiteDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # DGCNN expects (batch_size, num_features, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 256)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = CrossSiteDataset(train_patches, train_labels)\n", "test_dataset = CrossSiteDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 8  # Smaller batch size for DGCNN (more memory intensive)\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")\n", "print(f\"  Input shape: (batch_size, 3, 256) for DGCNN\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "setup_training", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c2787f51-c9ef-473b-f3ab-e3c74538f58e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model initialized with 1,799,810 parameters\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  K-neighbors: 20\n", "  Device: cuda\n"]}], "source": ["# Initialize model\n", "model = DGCNN(num_classes=2, k=20, dropout=0.5).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  K-neighbors: 20\")\n", "print(f\"  Device: {device}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "training_loop", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "1df55ea7-fa5c-48df-c21b-2d608cd94449"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== STARTING DGCNN TRAINING ===\n", "  Epoch 1/50, <PERSON><PERSON> 0/37, Loss: 0.5108\n", "  Epoch 1/50, <PERSON><PERSON> 10/37, Loss: 0.2483\n", "  Epoch 1/50, <PERSON><PERSON> 20/37, Loss: 0.1029\n", "  Epoch 1/50, <PERSON><PERSON> 30/37, Loss: 0.0493\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.1858, Train Acc: 96.26%, Val Loss: 0.0519, Val Acc: 100.00%, Time: 3.6s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/37, Loss: 0.0219\n", "  Epoch 2/50, <PERSON><PERSON> 10/37, Loss: 0.0186\n", "  Epoch 2/50, <PERSON><PERSON> 20/37, Loss: 0.0236\n", "  Epoch 2/50, <PERSON><PERSON> 30/37, Loss: 0.0139\n", "Epoch 2/50: Train Loss: 0.0215, Train Acc: 100.00%, Val Loss: 0.0180, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/37, Loss: 0.0127\n", "  Epoch 3/50, <PERSON><PERSON> 10/37, Loss: 0.0088\n", "  Epoch 3/50, <PERSON><PERSON> 20/37, Loss: 0.0054\n", "  Epoch 3/50, <PERSON><PERSON> 30/37, Loss: 0.0143\n", "Epoch 3/50: Train Loss: 0.0095, Train Acc: 100.00%, Val Loss: 0.0113, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/37, Loss: 0.0142\n", "  Epoch 4/50, <PERSON><PERSON> 10/37, Loss: 0.0065\n", "  Epoch 4/50, <PERSON><PERSON> 20/37, Loss: 0.0060\n", "  Epoch 4/50, <PERSON><PERSON> 30/37, Loss: 0.0071\n", "Epoch 4/50: Train Loss: 0.0069, Train Acc: 100.00%, Val Loss: 0.0077, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/37, Loss: 0.0076\n", "  Epoch 5/50, <PERSON><PERSON> 10/37, Loss: 0.0032\n", "  Epoch 5/50, <PERSON><PERSON> 20/37, Loss: 0.0047\n", "  Epoch 5/50, <PERSON><PERSON> 30/37, Loss: 0.0041\n", "Epoch 5/50: Train Loss: 0.0053, Train Acc: 100.00%, Val Loss: 0.0063, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/37, Loss: 0.0041\n", "  Epoch 6/50, <PERSON><PERSON> 10/37, Loss: 0.0055\n", "  Epoch 6/50, <PERSON><PERSON> 20/37, Loss: 0.0024\n", "  Epoch 6/50, <PERSON><PERSON> 30/37, Loss: 0.0022\n", "Epoch 6/50: Train Loss: 0.0036, Train Acc: 100.00%, Val Loss: 0.0043, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/37, Loss: 0.0035\n", "  Epoch 7/50, <PERSON><PERSON> 10/37, Loss: 0.0063\n", "  Epoch 7/50, <PERSON><PERSON> 20/37, Loss: 0.0037\n", "  Epoch 7/50, <PERSON><PERSON> 30/37, Loss: 0.0023\n", "Epoch 7/50: Train Loss: 0.0029, Train Acc: 100.00%, Val Loss: 0.0033, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/37, Loss: 0.0037\n", "  Epoch 8/50, <PERSON><PERSON> 10/37, Loss: 0.0018\n", "  Epoch 8/50, <PERSON><PERSON> 20/37, Loss: 0.0014\n", "  Epoch 8/50, <PERSON><PERSON> 30/37, Loss: 0.0032\n", "Epoch 8/50: Train Loss: 0.0025, Train Acc: 100.00%, Val Loss: 0.0025, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/37, Loss: 0.0020\n", "  Epoch 9/50, <PERSON><PERSON> 10/37, Loss: 0.0055\n", "  Epoch 9/50, <PERSON><PERSON> 20/37, Loss: 0.0033\n", "  Epoch 9/50, <PERSON><PERSON> 30/37, Loss: 0.0017\n", "Epoch 9/50: Train Loss: 0.0020, Train Acc: 100.00%, Val Loss: 0.0023, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/37, Loss: 0.0017\n", "  Epoch 10/50, <PERSON><PERSON> 10/37, Loss: 0.0031\n", "  Epoch 10/50, <PERSON><PERSON> 20/37, Loss: 0.0014\n", "  Epoch 10/50, <PERSON><PERSON> 30/37, Loss: 0.0006\n", "Epoch 10/50: Train Loss: 0.0018, Train Acc: 100.00%, Val Loss: 0.0021, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/37, Loss: 0.0011\n", "  Epoch 11/50, <PERSON><PERSON> 10/37, Loss: 0.0019\n", "  Epoch 11/50, <PERSON><PERSON> 20/37, Loss: 0.0014\n", "  Epoch 11/50, <PERSON><PERSON> 30/37, Loss: 0.0011\n", "Epoch 11/50: Train Loss: 0.0016, Train Acc: 100.00%, Val Loss: 0.0018, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/37, Loss: 0.0014\n", "  Epoch 12/50, <PERSON><PERSON> 10/37, Loss: 0.0011\n", "  Epoch 12/50, <PERSON><PERSON> 20/37, Loss: 0.0013\n", "  Epoch 12/50, <PERSON><PERSON> 30/37, Loss: 0.0011\n", "Epoch 12/50: Train Loss: 0.0012, Train Acc: 100.00%, Val Loss: 0.0015, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 13/50, <PERSON><PERSON> 10/37, Loss: 0.0010\n", "  Epoch 13/50, <PERSON><PERSON> 20/37, Loss: 0.0012\n", "  Epoch 13/50, <PERSON><PERSON> 30/37, Loss: 0.0018\n", "Epoch 13/50: Train Loss: 0.0012, Train Acc: 100.00%, Val Loss: 0.0014, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 14/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 14/50, <PERSON><PERSON> 20/37, Loss: 0.0014\n", "  Epoch 14/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 14/50: Train Loss: 0.0011, Train Acc: 100.00%, Val Loss: 0.0012, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 15/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 15/50, <PERSON><PERSON> 20/37, Loss: 0.0010\n", "  Epoch 15/50, <PERSON><PERSON> 30/37, Loss: 0.0009\n", "Epoch 15/50: Train Loss: 0.0009, Train Acc: 100.00%, Val Loss: 0.0012, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 16/50, <PERSON><PERSON> 10/37, Loss: 0.0007\n", "  Epoch 16/50, <PERSON><PERSON> 20/37, Loss: 0.0012\n", "  Epoch 16/50, <PERSON><PERSON> 30/37, Loss: 0.0010\n", "Epoch 16/50: Train Loss: 0.0009, Train Acc: 100.00%, Val Loss: 0.0010, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/37, Loss: 0.0017\n", "  Epoch 17/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 17/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 17/50, <PERSON><PERSON> 30/37, Loss: 0.0009\n", "Epoch 17/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0010, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 18/50, <PERSON><PERSON> 10/37, Loss: 0.0015\n", "  Epoch 18/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 18/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 18/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0009, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 19/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 19/50, <PERSON><PERSON> 20/37, Loss: 0.0010\n", "  Epoch 19/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 19/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 20/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 20/50, <PERSON><PERSON> 20/37, Loss: 0.0014\n", "  Epoch 20/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 20/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 21/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 21/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 21/50, <PERSON><PERSON> 30/37, Loss: 0.0010\n", "Epoch 21/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 22/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 22/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 22/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 22/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 23/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 23/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 23/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 23/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 24/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 24/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 24/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 24/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0008, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 25/50, <PERSON><PERSON> 10/37, Loss: 0.0009\n", "  Epoch 25/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 25/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 25/50: Train Loss: 0.0007, Train Acc: 100.00%, Val Loss: 0.0007, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 26/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 26/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 26/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 26/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/37, Loss: 0.0005\n", "  Epoch 27/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 27/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 27/50, <PERSON><PERSON> 30/37, Loss: 0.0006\n", "Epoch 27/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/37, Loss: 0.0011\n", "  Epoch 28/50, <PERSON><PERSON> 10/37, Loss: 0.0008\n", "  Epoch 28/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 28/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 28/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/37, Loss: 0.0007\n", "  Epoch 29/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 29/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 29/50, <PERSON><PERSON> 30/37, Loss: 0.0014\n", "Epoch 29/50: Train Loss: 0.0006, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 30/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 30/50, <PERSON><PERSON> 20/37, Loss: 0.0009\n", "  Epoch 30/50, <PERSON><PERSON> 30/37, Loss: 0.0007\n", "Epoch 30/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 31/50, <PERSON><PERSON> 10/37, Loss: 0.0009\n", "  Epoch 31/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 31/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 31/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 32/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 32/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 32/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 32/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/37, Loss: 0.0010\n", "  Epoch 33/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 33/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 33/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 33/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 34/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 34/50, <PERSON><PERSON> 20/37, Loss: 0.0011\n", "  Epoch 34/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 34/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0006, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 35/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 35/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 35/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 35/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 36/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 36/50, <PERSON><PERSON> 20/37, Loss: 0.0007\n", "  Epoch 36/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 36/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 37/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 37/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 37/50, <PERSON><PERSON> 30/37, Loss: 0.0008\n", "Epoch 37/50: Train Loss: 0.0005, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/37, Loss: 0.0003\n", "  Epoch 38/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 38/50, <PERSON><PERSON> 20/37, Loss: 0.0003\n", "  Epoch 38/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 38/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0005, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 39/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 39/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 39/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 39/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 40/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 40/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 40/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 40/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/37, Loss: 0.0004\n", "  Epoch 41/50, <PERSON><PERSON> 10/37, Loss: 0.0003\n", "  Epoch 41/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 41/50, <PERSON><PERSON> 30/37, Loss: 0.0003\n", "Epoch 41/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 42/50, <PERSON><PERSON> 10/37, Loss: 0.0007\n", "  Epoch 42/50, <PERSON><PERSON> 20/37, Loss: 0.0006\n", "  Epoch 42/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 42/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 43/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 43/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 43/50, <PERSON><PERSON> 30/37, Loss: 0.0001\n", "Epoch 43/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 44/50, <PERSON><PERSON> 10/37, Loss: 0.0004\n", "  Epoch 44/50, <PERSON><PERSON> 20/37, Loss: 0.0001\n", "  Epoch 44/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 44/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 45/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 45/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 45/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 45/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/37, Loss: 0.0001\n", "  Epoch 46/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 46/50, <PERSON><PERSON> 20/37, Loss: 0.0005\n", "  Epoch 46/50, <PERSON><PERSON> 30/37, Loss: 0.0002\n", "Epoch 46/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 47/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 47/50, <PERSON><PERSON> 20/37, Loss: 0.0002\n", "  Epoch 47/50, <PERSON><PERSON> 30/37, Loss: 0.0010\n", "Epoch 47/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 48/50, <PERSON><PERSON> 10/37, Loss: 0.0002\n", "  Epoch 48/50, <PERSON><PERSON> 20/37, Loss: 0.0004\n", "  Epoch 48/50, <PERSON><PERSON> 30/37, Loss: 0.0004\n", "Epoch 48/50: Train Loss: 0.0003, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/37, Loss: 0.0006\n", "  Epoch 49/50, <PERSON><PERSON> 10/37, Loss: 0.0005\n", "  Epoch 49/50, <PERSON><PERSON> 20/37, Loss: 0.0011\n", "  Epoch 49/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 49/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/37, Loss: 0.0002\n", "  Epoch 50/50, <PERSON><PERSON> 10/37, Loss: 0.0006\n", "  Epoch 50/50, <PERSON><PERSON> 20/37, Loss: 0.0009\n", "  Epoch 50/50, <PERSON><PERSON> 30/37, Loss: 0.0005\n", "Epoch 50/50: Train Loss: 0.0004, Train Acc: 100.00%, Val Loss: 0.0004, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "\n", "DGCNN Training completed in 0.9 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING DGCNN TRAINING ===\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/dgcnn_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nDGCNN Training completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Cross-Site Evaluation on RCPS"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "test_evaluation", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "af55db9b-125f-4745-e221-4279ca88fb44"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== DGCNN CROSS-SITE EVALUATION ON RCPS ===\n", "Testing DGCNN trained on RES data on RCPS data...\n", "\n", "DGCNN Cross-Site Test Results (RES→RCPS):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Detailed Classification Report:\n", "              precision    recall  f1-score   support\n", "\n", "    Non-Pile       0.00      0.00      0.00         0\n", "        Pile       1.00      1.00      1.00      1359\n", "\n", "    accuracy                           1.00      1359\n", "   macro avg       0.50      0.50      0.50      1359\n", "weighted avg       1.00      1.00      1.00      1359\n", "\n", "\n", "Confusion Matrix:\n", "[[   0    0]\n", " [   0 1359]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Results saved:\n", "  Training history: dgcnn_training_history_20250814_172125.json\n", "  Predictions: dgcnn_rcps_predictions_20250814_172125.csv\n", "  Model: dgcnn_best_model.pth\n", "\n", "=== DGCNN CROSS-SITE TRAINING COMPLETE ===\n", "Successfully trained on RES data and tested on RCPS data\n", "Cross-site generalization accuracy: 100.00%\n", "\n", "Comparison Summary:\n", "  Classical ML (local): 100.0% accuracy\n", "  DGCNN (cross-site): 100.00% accuracy\n", "  Generalization gap: 0.00%\n"]}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/dgcnn_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== DGCNN CROSS-SITE EVALUATION ON RCPS ===\")\n", "print(\"Testing DGCNN trained on RES data on RCPS data...\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nDGCNN Cross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Detailed classification report (robust for single-class case)\n", "print(\"\\nDetailed Classification Report:\")\n", "print(classification_report(\n", "    all_targets,\n", "    all_predictions,\n", "    labels=[0, 1],  # Force both classes\n", "    target_names=['Non-<PERSON><PERSON>', '<PERSON><PERSON>'],\n", "    zero_division=0\n", "))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions, labels=[0, 1])\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Reds',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('DGCNN Cross-Site Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'k_neighbors': 20\n", "}\n", "\n", "with open(f'{models_path}/dgcnn_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/dgcnn_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: dgcnn_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: dgcnn_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: dgcnn_best_model.pth\")\n", "\n", "print(\"\\n=== DGCNN CROSS-SITE TRAINING COMPLETE ===\")\n", "print(f\"Successfully trained on RES data and tested on RCPS data\")\n", "print(f\"Cross-site generalization accuracy: {test_acc:.2f}%\")\n", "print(f\"\\nComparison Summary:\")\n", "print(f\"  Classical ML (local): 100.0% accuracy\")\n", "print(f\"  DGCNN (cross-site): {test_acc:.2f}% accuracy\")\n", "print(f\"  Generalization gap: {100.0 - test_acc:.2f}%\")\n"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4", "machine_shape": "hm"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}