from google.colab import drive
drive.mount('/content/drive')

# Project paths
GDRIVE_BASE = "/content/drive/MyDrive"
PROJECT_FOLDER = "pointnet_pile_detection"
project_path = f"{GDRIVE_BASE}/{PROJECT_FOLDER}"
data_path = f"{project_path}/data"
models_path = f"{project_path}/models"

print(f"Project path: {project_path}")
print(f"Data path: {data_path}")
print(f"Models path: {models_path}")

# Install required packages
!pip install laspy geopandas scikit-learn mlflow

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader, random_split
import laspy
import geopandas as gpd
from scipy.spatial import cKDTree
from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pickle
import json
import time
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set random seeds
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

def load_site_point_cloud(las_path):
    """Load point cloud from LAS file"""
    print(f"Loading point cloud: {las_path}")
    las_file = laspy.read(las_path)
    points = np.vstack([las_file.x, las_file.y, las_file.z]).T
    print(f"  Loaded {len(points):,} points")
    print(f"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")
    return points

def load_pile_locations_from_csv(csv_path, site_name):
    """Load pile locations from Classical ML results CSV"""
    print(f"Loading pile locations from: {csv_path}")
    df = pd.read_csv(csv_path)

    # Extract coordinates based on CSV format
    if 'utm_x' in df.columns and 'utm_y' in df.columns:
        pile_coords = df[['utm_x', 'utm_y']].values
    elif 'x' in df.columns and 'y' in df.columns:
        pile_coords = df[['x', 'y']].values
    else:
        raise ValueError(f"Could not find coordinate columns in {csv_path}")

    print(f"  Loaded {len(pile_coords)} pile locations for {site_name}")
    print(f"  Bounds: X[{pile_coords[:, 0].min():.1f}, {pile_coords[:, 0].max():.1f}], Y[{pile_coords[:, 1].min():.1f}, {pile_coords[:, 1].max():.1f}]")
    return pile_coords

def extract_patches_for_dgcnn(points, pile_coords, site_name, patch_radius=10.0, min_points=20):
    """Extract smaller patches optimized for DGCNN (256 points)"""
    print(f"\nExtracting DGCNN patches for {site_name}:")
    print(f"  Patch radius: {patch_radius}m (smaller for DGCNN)")
    print(f"  Minimum points per patch: {min_points}")

    kdtree = cKDTree(points[:, :2])
    positive_patches = []

    # Extract positive patches around known pile locations
    for i, (pile_x, pile_y) in enumerate(pile_coords):
        if i % 100 == 0:
            print(f"  Processing pile {i+1}/{len(pile_coords)}")

        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)
        if len(indices) >= min_points:
            patch_points = points[indices]
            # Center the patch around pile location
            centered_patch = patch_points - np.array([pile_x, pile_y, 0])
            positive_patches.append(centered_patch)

    print(f"  Extracted {len(positive_patches)} positive patches")

    # Extract negative patches
    negative_patches = []
    x_min, x_max = points[:, 0].min(), points[:, 0].max()
    y_min, y_max = points[:, 1].min(), points[:, 1].max()

    target_negatives = len(positive_patches)
    attempts = 0
    max_attempts = target_negatives * 10

    print(f"  Extracting {target_negatives} negative patches...")

    while len(negative_patches) < target_negatives and attempts < max_attempts:
        rand_x = np.random.uniform(x_min, x_max)
        rand_y = np.random.uniform(y_min, y_max)

        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)

        if distances.min() > patch_radius * 2.0:
            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)
            if len(indices) >= min_points:
                patch_points = points[indices]
                centered_patch = patch_points - np.array([rand_x, rand_y, 0])
                negative_patches.append(centered_patch)

        attempts += 1

        if attempts % 1000 == 0:
            print(f"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})")

    print(f"  Extracted {len(negative_patches)} negative patches")
    print(f"  Total patches: {len(positive_patches) + len(negative_patches)}")

    return positive_patches, negative_patches

def resample_patch_for_dgcnn(patch, target_points=256):
    """Resample patch to fixed size for DGCNN (256 points)"""
    if len(patch) == 0:
        return np.zeros((target_points, 3))

    if len(patch) >= target_points:
        # Downsample
        indices = np.random.choice(len(patch), target_points, replace=False)
        resampled = patch[indices]
    else:
        # Upsample with noise
        extra_needed = target_points - len(patch)
        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)
        extra_points = patch[extra_indices] + np.random.normal(0, 0.005, (extra_needed, 3))  # Smaller noise
        resampled = np.vstack([patch, extra_points])

    return resampled.astype(np.float32)

# Load RES data (training site)
print("=== LOADING RES DATA (TRAINING SITE) ===")
res_points = load_site_point_cloud(f"{data_path}/nortan_res/Block_11_2m.las")
res_pile_coords = load_pile_locations_from_csv(
    f"{data_path}/ground_truth/nortan_res_pile_detection_results_20250807_214148.csv",
    "nortan_res"
)

# Extract RES patches (smaller for DGCNN)
res_pos_patches, res_neg_patches = extract_patches_for_dgcnn(
    res_points, res_pile_coords, "nortan_res", patch_radius=10.0, min_points=20
)

print("\n=== LOADING RCPS DATA (TEST SITE) ===")
rcps_points = load_site_point_cloud(f"{data_path}/althea_rpcs/Point_Cloud.las")
rcps_pile_coords = load_pile_locations_from_csv(
    f"{data_path}/ground_truth/rcps_generalization_results_20250807_221320.csv",
    "althea_rcps"
)

# Extract RCPS patches (smaller for DGCNN)
rcps_pos_patches, rcps_neg_patches = extract_patches_for_dgcnn(
    rcps_points, rcps_pile_coords, "althea_rcps", patch_radius=10.0, min_points=20
)

print("\n=== DATA SUMMARY ===")
print(f"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative")
print(f"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative")
print(f"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}")
print(f"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}")

# Resample all patches to fixed size (256 points for DGCNN)
print("Resampling patches to 256 points for DGCNN...")

# Training data (RES)
train_patches = []
train_labels = []

# Positive patches
for patch in res_pos_patches:
    resampled = resample_patch_for_dgcnn(patch, 256)
    train_patches.append(resampled)
    train_labels.append(1)

# Negative patches
for patch in res_neg_patches:
    resampled = resample_patch_for_dgcnn(patch, 256)
    train_patches.append(resampled)
    train_labels.append(0)

# Test data (RCPS)
test_patches = []
test_labels = []

# Positive patches
for patch in rcps_pos_patches:
    resampled = resample_patch_for_dgcnn(patch, 256)
    test_patches.append(resampled)
    test_labels.append(1)

# Negative patches
for patch in rcps_neg_patches:
    resampled = resample_patch_for_dgcnn(patch, 256)
    test_patches.append(resampled)
    test_labels.append(0)

# Convert to numpy arrays
train_patches = np.array(train_patches, dtype=np.float32)  # (N, 256, 3)
train_labels = np.array(train_labels, dtype=np.int64)      # (N,)
test_patches = np.array(test_patches, dtype=np.float32)    # (M, 256, 3)
test_labels = np.array(test_labels, dtype=np.int64)        # (M,)

print(f"\nFinal dataset shapes:")
print(f"Training: {train_patches.shape}, labels: {train_labels.shape}")
print(f"Testing: {test_patches.shape}, labels: {test_labels.shape}")
print(f"Training class distribution: {np.bincount(train_labels)}")
print(f"Testing class distribution: {np.bincount(test_labels)}")

def knn(x, k):
    """Find k nearest neighbors"""
    inner = -2*torch.matmul(x.transpose(2, 1), x)
    xx = torch.sum(x**2, dim=1, keepdim=True)
    pairwise_distance = -xx - inner - xx.transpose(2, 1)

    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)
    return idx

def get_graph_feature(x, k=20, idx=None):
    """Construct edge features"""
    batch_size = x.size(0)
    num_points = x.size(2)
    x = x.view(batch_size, -1, num_points)

    if idx is None:
        idx = knn(x, k=k)   # (batch_size, num_points, k)

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points

    idx = idx + idx_base

    idx = idx.view(-1)

    _, num_dims, _ = x.size()

    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)
    feature = x.view(batch_size*num_points, -1)[idx, :]
    feature = feature.view(batch_size, num_points, k, num_dims)
    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)

    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()

    return feature      # (batch_size, 2*num_dims, num_points, k)

class DGCNN(nn.Module):
    def __init__(self, num_classes=2, k=20, dropout=0.5):
        super(DGCNN, self).__init__()
        self.k = k

        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(256)
        self.bn5 = nn.BatchNorm1d(1024)

        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),
                                   self.bn1,
                                   nn.LeakyReLU(negative_slope=0.2))
        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),
                                   self.bn2,
                                   nn.LeakyReLU(negative_slope=0.2))
        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),
                                   self.bn3,
                                   nn.LeakyReLU(negative_slope=0.2))
        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),
                                   self.bn4,
                                   nn.LeakyReLU(negative_slope=0.2))
        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),
                                   self.bn5,
                                   nn.LeakyReLU(negative_slope=0.2))

        # Classification head
        self.linear1 = nn.Linear(1024*2, 512, bias=False)
        self.bn6 = nn.BatchNorm1d(512)
        self.dp1 = nn.Dropout(p=dropout)
        self.linear2 = nn.Linear(512, 256)
        self.bn7 = nn.BatchNorm1d(256)
        self.dp2 = nn.Dropout(p=dropout)
        self.linear3 = nn.Linear(256, num_classes)

    def forward(self, x):
        batch_size = x.size(0)

        # EdgeConv layers
        x = get_graph_feature(x, k=self.k)      # (batch_size, 3, num_points) -> (batch_size, 3*2, num_points, k)
        x = self.conv1(x)                       # (batch_size, 3*2, num_points, k) -> (batch_size, 64, num_points, k)
        x1 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)

        x = get_graph_feature(x1, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)
        x = self.conv2(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 64, num_points, k)
        x2 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)

        x = get_graph_feature(x2, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)
        x = self.conv3(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 128, num_points, k)
        x3 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 128, num_points, k) -> (batch_size, 128, num_points)

        x = get_graph_feature(x3, k=self.k)     # (batch_size, 128, num_points) -> (batch_size, 128*2, num_points, k)
        x = self.conv4(x)                       # (batch_size, 128*2, num_points, k) -> (batch_size, 256, num_points, k)
        x4 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 256, num_points, k) -> (batch_size, 256, num_points)

        x = torch.cat((x1, x2, x3, x4), dim=1)  # (batch_size, 64+64+128+256, num_points)

        x = self.conv5(x)                       # (batch_size, 512, num_points) -> (batch_size, 1024, num_points)
        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)
        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)
        x = torch.cat((x1, x2), 1)              # (batch_size, 1024*2)

        # Classification
        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2) # (batch_size, 1024*2) -> (batch_size, 512)
        x = self.dp1(x)
        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2) # (batch_size, 512) -> (batch_size, 256)
        x = self.dp2(x)
        x = self.linear3(x)                                             # (batch_size, 256) -> (batch_size, num_classes)

        return x

class CrossSiteDataset(Dataset):
    def __init__(self, patches, labels):
        # DGCNN expects (batch_size, num_features, num_points)
        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 256)
        self.labels = torch.LongTensor(labels)

    def __len__(self):
        return len(self.patches)

    def __getitem__(self, idx):
        return self.patches[idx], self.labels[idx]

# Create datasets
train_dataset = CrossSiteDataset(train_patches, train_labels)
test_dataset = CrossSiteDataset(test_patches, test_labels)

# Create train/validation split from training data
train_size = int(0.8 * len(train_dataset))
val_size = len(train_dataset) - train_size
train_subset, val_subset = random_split(train_dataset, [train_size, val_size])

# Create data loaders
batch_size = 8  # Smaller batch size for DGCNN (more memory intensive)
train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)
val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)
test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

print(f"Dataset sizes:")
print(f"  Training: {len(train_subset)}")
print(f"  Validation: {len(val_subset)}")
print(f"  Test (RCPS): {len(test_dataset)}")
print(f"  Batch size: {batch_size}")
print(f"  Input shape: (batch_size, 3, 256) for DGCNN")

# Initialize model
model = DGCNN(num_classes=2, k=20, dropout=0.5).to(device)
print(f"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters")

# Training configuration
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001, weight_decay=1e-4)
scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)

# Training parameters
num_epochs = 50  # Reduced for Colab
best_val_acc = 0.0
train_losses = []
val_losses = []
train_accs = []
val_accs = []

print(f"Training configuration:")
print(f"  Epochs: {num_epochs}")
print(f"  Learning rate: 0.001")
print(f"  Weight decay: 1e-4")
print(f"  K-neighbors: 20")
print(f"  Device: {device}")

# Training loop
print("\n=== STARTING DGCNN TRAINING ===")
start_time = time.time()

for epoch in range(num_epochs):
    epoch_start = time.time()

    # Training phase
    model.train()
    train_loss = 0.0
    train_correct = 0
    train_total = 0

    for batch_idx, (data, target) in enumerate(train_loader):
        data, target = data.to(device), target.to(device)

        optimizer.zero_grad()
        output = model(data)
        loss = criterion(output, target)
        loss.backward()
        optimizer.step()

        train_loss += loss.item()
        pred = output.argmax(dim=1)
        train_correct += pred.eq(target).sum().item()
        train_total += target.size(0)

        if batch_idx % 10 == 0:
            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')

    # Validation phase
    model.eval()
    val_loss = 0.0
    val_correct = 0
    val_total = 0

    with torch.no_grad():
        for data, target in val_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            loss = criterion(output, target)

            val_loss += loss.item()
            pred = output.argmax(dim=1)
            val_correct += pred.eq(target).sum().item()
            val_total += target.size(0)

    # Calculate metrics
    train_loss /= len(train_loader)
    val_loss /= len(val_loader)
    train_acc = 100. * train_correct / train_total
    val_acc = 100. * val_correct / val_total

    # Store metrics
    train_losses.append(train_loss)
    val_losses.append(val_loss)
    train_accs.append(train_acc)
    val_accs.append(val_acc)

    # Update learning rate
    scheduler.step()

    # Save best model
    if val_acc > best_val_acc:
        best_val_acc = val_acc
        torch.save(model.state_dict(), f'{models_path}/dgcnn_best_model.pth')
        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')

    epoch_time = time.time() - epoch_start
    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '
          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')
    print('-' * 80)

total_time = time.time() - start_time
print(f"\nDGCNN Training completed in {total_time/60:.1f} minutes")
print(f"Best validation accuracy: {best_val_acc:.2f}%")

# Load best model
model.load_state_dict(torch.load(f'{models_path}/dgcnn_best_model.pth'))
model.eval()

print("=== DGCNN CROSS-SITE EVALUATION ON RCPS ===")
print("Testing DGCNN trained on RES data on RCPS data...")

# Test evaluation
test_correct = 0
test_total = 0
all_predictions = []
all_targets = []
all_probabilities = []

with torch.no_grad():
    for data, target in test_loader:
        data, target = data.to(device), target.to(device)
        output = model(data)

        # Get predictions and probabilities
        probabilities = torch.softmax(output, dim=1)
        pred = output.argmax(dim=1)

        test_correct += pred.eq(target).sum().item()
        test_total += target.size(0)

        all_predictions.extend(pred.cpu().numpy())
        all_targets.extend(target.cpu().numpy())
        all_probabilities.extend(probabilities.cpu().numpy())

# Calculate metrics
test_acc = 100. * test_correct / test_total
test_f1 = f1_score(all_targets, all_predictions)

print(f"\nDGCNN Cross-Site Test Results (RES→RCPS):")
print(f"  Test Accuracy: {test_acc:.2f}%")
print(f"  Test F1-Score: {test_f1:.4f}")
print(f"  Total test samples: {test_total}")
print(f"  Correct predictions: {test_correct}")

# Detailed classification report (robust for single-class case)
print("\nDetailed Classification Report:")
print(classification_report(
    all_targets,
    all_predictions,
    labels=[0, 1],  # Force both classes
    target_names=['Non-Pile', 'Pile'],
    zero_division=0
))

# Confusion matrix
cm = confusion_matrix(all_targets, all_predictions, labels=[0, 1])
print("\nConfusion Matrix:")
print(cm)

# Plot confusion matrix
plt.figure(figsize=(8, 6))
sns.heatmap(cm, annot=True, fmt='d', cmap='Reds',
            xticklabels=['Non-Pile', 'Pile'],
            yticklabels=['Non-Pile', 'Pile'])
plt.title('DGCNN Cross-Site Confusion Matrix (RES→RCPS)')
plt.ylabel('True Label')
plt.xlabel('Predicted Label')
plt.show()

# Save results
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

training_history = {
    'train_losses': train_losses,
    'val_losses': val_losses,
    'train_accs': train_accs,
    'val_accs': val_accs,
    'best_val_acc': best_val_acc,
    'test_acc': test_acc,
    'test_f1': test_f1,
    'num_epochs': num_epochs,
    'batch_size': batch_size,
    'k_neighbors': 20
}

with open(f'{models_path}/dgcnn_training_history_{timestamp}.json', 'w') as f:
    json.dump(training_history, f, indent=2)

results_df = pd.DataFrame({
    'true_label': all_targets,
    'predicted_label': all_predictions,
    'pile_probability': [prob[1] for prob in all_probabilities],
    'non_pile_probability': [prob[0] for prob in all_probabilities]
})

results_df.to_csv(f'{models_path}/dgcnn_rcps_predictions_{timestamp}.csv', index=False)

print(f"\nResults saved:")
print(f"  Training history: dgcnn_training_history_{timestamp}.json")
print(f"  Predictions: dgcnn_rcps_predictions_{timestamp}.csv")
print(f"  Model: dgcnn_best_model.pth")

print("\n=== DGCNN CROSS-SITE TRAINING COMPLETE ===")
print(f"Successfully trained on RES data and tested on RCPS data")
print(f"Cross-site generalization accuracy: {test_acc:.2f}%")
print(f"\nComparison Summary:")
print(f"  Classical ML (local): 100.0% accuracy")
print(f"  DGCNN (cross-site): {test_acc:.2f}% accuracy")
print(f"  Generalization gap: {100.0 - test_acc:.2f}%")
