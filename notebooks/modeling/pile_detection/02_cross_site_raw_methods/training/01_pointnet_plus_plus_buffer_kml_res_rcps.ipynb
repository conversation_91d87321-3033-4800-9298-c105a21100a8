{"cells": [{"cell_type": "markdown", "metadata": {"id": "hf02wH1cKVzW"}, "source": ["# PointNet++ Cross-Site Pile Detection with Buffer KML (RES→RCPS)\n", "\n", "This notebook implements **full PointNet++** for cross-site generalization using **Buffer KML ground truth**:\n", "- **Train on**: Nortan RES site data with Buffer_2m.kml\n", "- **Test on**: Althea RCPS site data with Buffer_2m.kml\n", "- **Goal**: Test PointNet++ cross-site performance using original KML ground truth\n", "\n", "**Architecture:**\n", "- Full PointNet++ with Set Abstraction layers\n", "- Input: (N, 3, 64) - 3D coordinates (XYZ) - FIXED to match successful Classical ML\n", "- Binary classification (pile vs non-pile)\n", "- Cross-site validation protocol\n", "\n", "**Data Source:**\n", "- Uses Buffer_2m.kml files (same as Classical ML)\n", "- No dependency on CSV results\n", "- Fair comparison with Classical ML methods\n", "\n", "**Key Changes:**\n", "- ✅ Patch radius: 3.0m (matches successful Classical ML)\n", "- ✅ Points per patch: 64 (matches successful Classical ML)\n", "- ✅ CSV export for validation\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Purpose**: Dissertation Research - PointNet++ Buffer KML Validation\n"]}, {"cell_type": "markdown", "metadata": {"id": "W0ubzvdCLikt"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s482wPMALjl1", "outputId": "246940e1-5277-4a45-dcbd-0dfb224cb837"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "install_deps", "outputId": "eaa150d4-b4bc-48cd-9e3f-e34a4dd1ef92"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.43)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.63.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.9)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.4)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m7.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m49.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m80.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m59.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m20.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m8.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.63.0-py3-none-any.whl (688 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m688.0/688.0 kB\u001b[0m \u001b[31m46.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m17.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m10.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m15.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.63.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## RES/RCPS Data Loading with Domain-Aware Patch Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "load_res_rcps_data"}, "outputs": [], "source": ["def load_site_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"  Loaded {len(points):,} points\")\n", "    print(f\"  Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N (SAME as Classical ML)\"\"\"\n", "    print(f\"Loading pile locations from Buffer KML: {kml_path}\")\n", "    gdf = gpd.read_file(kml_path)\n", "\n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "\n", "    pile_locations = np.array(pile_coords)\n", "\n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "\n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "\n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "\n", "    print(f\"  Loaded {len(pile_locations_utm)} pile locations\")\n", "    print(f\"  Bounds: X[{pile_locations_utm[:, 0].min():.1f}, {pile_locations_utm[:, 0].max():.1f}], Y[{pile_locations_utm[:, 1].min():.1f}, {pile_locations_utm[:, 1].max():.1f}]\")\n", "\n", "    return pile_locations_utm\n", "\n", "def extract_patches_domain_aware(points, pile_coords, site_name, patch_radius=15.0, min_points=30):\n", "    \"\"\"Extract patches with domain-specific parameters for RES/RCPS\"\"\"\n", "    print(f\"\\nExtracting patches for {site_name}:\")\n", "    print(f\"  Patch radius: {patch_radius}m\")\n", "    print(f\"  Minimum points per patch: {min_points}\")\n", "\n", "    kdtree = cKDTree(points[:, :2])\n", "    positive_patches = []\n", "\n", "    # Extract positive patches around known pile locations\n", "    for i, (pile_x, pile_y) in enumerate(pile_coords):\n", "        if i % 100 == 0:\n", "            print(f\"  Processing pile {i+1}/{len(pile_coords)}\")\n", "\n", "        indices = kdtree.query_ball_point([pile_x, pile_y], patch_radius)\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            # Center the patch around pile location\n", "            centered_patch = patch_points - np.array([pile_x, pile_y, 0])\n", "            positive_patches.append(centered_patch)\n", "\n", "    print(f\"  Extracted {len(positive_patches)} positive patches\")\n", "\n", "    # Extract negative patches (same number as positives)\n", "    negative_patches = []\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    target_negatives = len(positive_patches)\n", "    attempts = 0\n", "    max_attempts = target_negatives * 10\n", "\n", "    print(f\"  Extracting {target_negatives} negative patches...\")\n", "\n", "    while len(negative_patches) < target_negatives and attempts < max_attempts:\n", "        # Random location\n", "        rand_x = np.random.uniform(x_min, x_max)\n", "        rand_y = np.random.uniform(y_min, y_max)\n", "\n", "        # Check distance from all known piles\n", "        distances = np.sqrt((pile_coords[:, 0] - rand_x)**2 + (pile_coords[:, 1] - rand_y)**2)\n", "\n", "        # Ensure negative patch is far from any pile\n", "        if distances.min() > patch_radius * 2.0:  # 2x radius separation\n", "            indices = kdtree.query_ball_point([rand_x, rand_y], patch_radius)\n", "            if len(indices) >= min_points:\n", "                patch_points = points[indices]\n", "                centered_patch = patch_points - np.array([rand_x, rand_y, 0])\n", "                negative_patches.append(centered_patch)\n", "\n", "        attempts += 1\n", "\n", "        if attempts % 1000 == 0:\n", "            print(f\"    Negative patches: {len(negative_patches)}/{target_negatives} (attempts: {attempts})\")\n", "\n", "    print(f\"  Extracted {len(negative_patches)} negative patches\")\n", "    print(f\"  Total patches: {len(positive_patches) + len(negative_patches)}\")\n", "\n", "    return positive_patches, negative_patches\n", "\n", "def resample_patch_to_fixed_size(patch, target_points=64):\n", "    \"\"\"Resample patch to fixed size for PointNet++ - FIXED to 64 points (matches Classical ML)\"\"\"\n", "    if len(patch) == 0:\n", "        return np.zeros((target_points, 3))\n", "\n", "    if len(patch) >= target_points:\n", "        # Downsample\n", "        indices = np.random.choice(len(patch), target_points, replace=False)\n", "        resampled = patch[indices]\n", "    else:\n", "        # Upsample with noise\n", "        extra_needed = target_points - len(patch)\n", "        extra_indices = np.random.choice(len(patch), extra_needed, replace=True)\n", "        extra_points = patch[extra_indices] + np.random.normal(0, 0.01, (extra_needed, 3))\n", "        resampled = np.vstack([patch, extra_points])\n", "\n", "    return resampled.astype(np.float32)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "load_data_main", "outputId": "a6ea372f-a5fc-405f-f6af-3702a40c4e52"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== LOADING RES DATA (TRAINING SITE) - BUFFER KML ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "  Loaded 35,565,352 points\n", "  Bounds: X[385724.0, 385809.6], Y[3529182.8, 3529447.0], Z[553.4, 556.3]\n", "Loading pile locations from Buffer KML: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Buffer_2m.kml\n", "  Loaded 368 pile locations\n", "  Bounds: X[385725.9, 385807.6], Y[3529184.8, 3529445.0]\n", "\n", "Extracting patches for nortan_res:\n", "  Patch radius: 3.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/368\n", "  Processing pile 101/368\n", "  Processing pile 201/368\n", "  Processing pile 301/368\n", "  Extracted 368 positive patches\n", "  Extracting 368 negative patches...\n", "    Negative patches: 0/368 (attempts: 1000)\n", "    Negative patches: 0/368 (attempts: 2000)\n", "    Negative patches: 0/368 (attempts: 3000)\n", "  Extracted 0 negative patches\n", "  Total patches: 368\n", "\n", "=== LOADING RCPS DATA (TEST SITE) - BUFFER KML ===\n", "Loading point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "  Loaded 52,862,386 points\n", "  Bounds: X[599595.2, 599866.2], Y[4334366.6, 4334660.8], Z[238.6, 259.2]\n", "Loading pile locations from Buffer KML: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Buffer_2m.kml\n", "  Loaded 1359 pile locations\n", "  Bounds: X[599597.2, 599864.2], Y[4334368.6, 4334658.8]\n", "\n", "Extracting patches for althea_rcps:\n", "  Patch radius: 3.0m\n", "  Minimum points per patch: 30\n", "  Processing pile 1/1359\n", "  Processing pile 101/1359\n", "  Processing pile 201/1359\n", "  Processing pile 301/1359\n", "  Processing pile 401/1359\n", "  Processing pile 501/1359\n", "  Processing pile 601/1359\n", "  Processing pile 701/1359\n", "  Processing pile 801/1359\n", "  Processing pile 901/1359\n", "  Processing pile 1001/1359\n", "  Processing pile 1101/1359\n", "  Processing pile 1201/1359\n", "  Processing pile 1301/1359\n", "  Extracted 1359 positive patches\n", "  Extracting 1359 negative patches...\n", "    Negative patches: 0/1359 (attempts: 1000)\n", "    Negative patches: 0/1359 (attempts: 2000)\n", "    Negative patches: 0/1359 (attempts: 3000)\n", "    Negative patches: 0/1359 (attempts: 4000)\n", "    Negative patches: 0/1359 (attempts: 5000)\n", "    Negative patches: 0/1359 (attempts: 6000)\n", "    Negative patches: 0/1359 (attempts: 7000)\n", "    Negative patches: 0/1359 (attempts: 8000)\n", "    Negative patches: 0/1359 (attempts: 9000)\n", "    Negative patches: 0/1359 (attempts: 10000)\n", "    Negative patches: 0/1359 (attempts: 11000)\n", "    Negative patches: 0/1359 (attempts: 12000)\n", "    Negative patches: 0/1359 (attempts: 13000)\n", "  Extracted 0 negative patches\n", "  Total patches: 1359\n", "\n", "=== DATA SUMMARY ===\n", "RES (training): 368 positive, 0 negative\n", "RCPS (testing): 1359 positive, 0 negative\n", "Total training samples: 368\n", "Total test samples: 1359\n"]}], "source": ["# Load RES data (training site) - BUFFER KML ONLY\n", "print(\"=== LOADING RES DATA (TRAINING SITE) - BUFFER KML ===\")\n", "res_points = load_site_point_cloud(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_pile_coords = load_and_reproject_kml(f\"{data_path}/nortan_res/Buffer_2m.kml\")\n", "\n", "# Extract RES patches (3m radius to match Classical ML)\n", "res_pos_patches, res_neg_patches = extract_patches_domain_aware(\n", "    res_points, res_pile_coords, \"nortan_res\", patch_radius=3.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) - BUFFER KML ===\")\n", "rcps_points = load_site_point_cloud(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_pile_coords = load_and_reproject_kml(f\"{data_path}/althea_rpcs/Buffer_2m.kml\")\n", "\n", "# Extract RCPS patches (3m radius to match Classical ML)\n", "rcps_pos_patches, rcps_neg_patches = extract_patches_domain_aware(\n", "    rcps_points, rcps_pile_coords, \"althea_rcps\", patch_radius=3.0, min_points=30\n", ")\n", "\n", "print(\"\\n=== DATA SUMMARY ===\")\n", "print(f\"RES (training): {len(res_pos_patches)} positive, {len(res_neg_patches)} negative\")\n", "print(f\"RCPS (testing): {len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative\")\n", "print(f\"Total training samples: {len(res_pos_patches) + len(res_neg_patches)}\")\n", "print(f\"Total test samples: {len(rcps_pos_patches) + len(rcps_neg_patches)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for PointNet++"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_datasets", "outputId": "0b5b00b3-5b56-4889-9bf3-7f482f23fb3e"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Resampling patches to 64 points...\n", "\n", "Final dataset shapes:\n", "Training: (368, 64, 3), labels: (368,)\n", "Testing: (1359, 64, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n"]}], "source": ["# Resample all patches to fixed size (64 points - matches Classical ML)\n", "print(\"Resampling patches to 64 points...\")\n", "\n", "# Training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Positive patches\n", "for patch in res_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 64)\n", "    train_patches.append(resampled)\n", "    train_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in res_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 64)\n", "    train_patches.append(resampled)\n", "    train_labels.append(0)\n", "\n", "# Test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Positive patches\n", "for patch in rcps_pos_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 64)\n", "    test_patches.append(resampled)\n", "    test_labels.append(1)\n", "\n", "# Negative patches\n", "for patch in rcps_neg_patches:\n", "    resampled = resample_patch_to_fixed_size(patch, 64)\n", "    test_patches.append(resampled)\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 64, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 64, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pointnet_architecture"}, "source": ["## PointNet++ Architecture"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "VorD7Gk-A9i_"}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(xyz, points)\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        S = self.npoint\n", "\n", "        # Use random sampling instead of farthest point sampling\n", "        fps_idx = torch.randint(0, N, (B, S), device=xyz.device, dtype=torch.long)\n", "        new_xyz = self.index_points(xyz, fps_idx)\n", "\n", "        # Simplified ball query - use k-nearest neighbors\n", "        idx = self.knn_query(xyz, new_xyz, self.nsample)\n", "        grouped_xyz = self.index_points(xyz, idx)\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = self.index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points\n", "\n", "    def knn_query(self, xyz, new_xyz, k):\n", "        \"\"\"Simplified k-nearest neighbor query\"\"\"\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "\n", "        # Compute pairwise distances\n", "        xyz_expanded = xyz.unsqueeze(2)  # (B, N, 1, C)\n", "        new_xyz_expanded = new_xyz.unsqueeze(1)  # (B, 1, S, C)\n", "\n", "        # Calculate squared distances\n", "        dists = torch.sum((xyz_expanded - new_xyz_expanded) ** 2, dim=-1)  # (B, N, S)\n", "\n", "        # Get k nearest neighbors for each query point\n", "        # FIXED: use dim=1 instead of dim=0, and ensure correct batch handling\n", "        _, idx = torch.topk(dists, k, dim=1, largest=False)  # (B, k, S)\n", "        idx = idx.permute(0, 2, 1)  # (B, S, k)\n", "\n", "        return idx\n", "\n", "    def index_points(self, points, idx):\n", "        \"\"\"\n", "        Input:\n", "            points: input points data, [B, N, C]\n", "            idx: sample index data, [B, S] or [B, S, K]\n", "        Return:\n", "            new_points:, indexed points data, [B, S, C] or [B, S, K, C]\n", "        \"\"\"\n", "        B, N, C = points.shape\n", "        device = points.device\n", "\n", "        # Handle out of bounds indices\n", "        idx = torch.clamp(idx, 0, N-1)\n", "\n", "        # Use torch.gather - most reliable approach\n", "        if len(idx.shape) == 2:  # [B, S]\n", "            idx_expanded = idx.unsqueeze(-1).expand(-1, -1, C)\n", "            return torch.gather(points, 1, idx_expanded)\n", "        else:  # [B, S, K]\n", "            B, <PERSON>, K = idx.shape\n", "            # Flatten to 2D, gather, then reshape back\n", "            idx_2d = idx.reshape(B, S*K)  # Changed from .view() to .reshape()\n", "            idx_expanded = idx_2d.unsqueeze(-1).expand(-1, -1, C)\n", "            gathered = torch.gather(points, 1, idx_expanded)\n", "            return gathered.reshape(B, S, K, C)  # Changed from .view() to .reshape()\n", "\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Simplified set abstraction layers\n", "        # self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels + 3, [32, 32, 64], False)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 16, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 128 + 3, [128, 256, 512], True)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(512, 256)\n", "        self.bn1 = nn.BatchNorm1d(256)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.Linear(256, 128)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "\n", "        # Set abstraction layers\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        # Classification\n", "        x = l3_points.view(B, 512)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "iomASvLaBT8X"}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    \"\"\"Simplified PointNet for pile detection - OPTIMIZED for 64 points\"\"\"\n", "    def __init__(self, num_classes=2, num_points=64):\n", "        super(SimplePointNet, self).__init__()\n", "        self.num_points = num_points\n", "\n", "        # Point-wise MLPs - optimized for smaller patches\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "\n", "        # Classification head - adjusted for 64-point patches\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, 64) - matches Classical ML patch size\n", "        batch_size = x.size(0)\n", "\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "\n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "\n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "create_dataset_class", "outputId": "34bb6fc5-8337-45b3-8be7-50888710b0b0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 16\n"]}], "source": ["class CrossSiteDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # SimplePointNet expects (batch_size, 3, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 64)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = CrossSiteDataset(train_patches, train_labels)\n", "test_dataset = CrossSiteDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 16  # Smaller batch size for Colab\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "setup_training", "outputId": "0d84de9b-b19f-431e-b56c-cebc44fd61cb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["PointNet++ model initialized with 369,442 parameters\n", "Using FIXED PointNet++ with corrected index_points function\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  Device: cuda\n"]}], "source": ["# Initialize PointNet++ model - FIXED VERSION\n", "model = PointNetPlusPlus(num_classes=2, in_channels=3).to(device)\n", "print(f\"PointNet++ model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "print(\"Using FIXED PointNet++ with corrected index_points function\")\n", "\n", "# SimplePointNet alternative (commented out)\n", "# model = SimplePointNet(num_classes=2).to(device)\n", "# print(f\"SimplePointNet model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  Device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "training_loop", "outputId": "1f920b6f-23b9-47e7-cb6f-30e7275de1aa"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== STARTING TRAINING ===\n", "  Epoch 1/50, <PERSON><PERSON> 0/19, Loss: 1.0082\n", "  Epoch 1/50, <PERSON><PERSON> 10/19, Loss: 0.4655\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.5275, Train Acc: 72.79%, Val Loss: 0.2686, Val Acc: 100.00%, Time: 3.9s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/19, Loss: 0.2649\n", "  Epoch 2/50, <PERSON><PERSON> 10/19, Loss: 0.0927\n", "Epoch 2/50: Train Loss: 0.1391, Train Acc: 100.00%, Val Loss: 0.0843, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/19, Loss: 0.0698\n", "  Epoch 3/50, <PERSON><PERSON> 10/19, Loss: 0.0585\n", "Epoch 3/50: Train Loss: 0.0572, Train Acc: 100.00%, Val Loss: 0.0479, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/19, Loss: 0.0477\n", "  Epoch 4/50, <PERSON><PERSON> 10/19, Loss: 0.0401\n", "Epoch 4/50: Train Loss: 0.0330, Train Acc: 100.00%, Val Loss: 0.0418, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/19, Loss: 0.0432\n", "  Epoch 5/50, <PERSON><PERSON> 10/19, Loss: 0.0186\n", "Epoch 5/50: Train Loss: 0.0242, Train Acc: 100.00%, Val Loss: 0.0320, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/19, Loss: 0.0224\n", "  Epoch 6/50, <PERSON><PERSON> 10/19, Loss: 0.0245\n", "Epoch 6/50: Train Loss: 0.0178, Train Acc: 100.00%, Val Loss: 0.0230, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/19, Loss: 0.0192\n", "  Epoch 7/50, <PERSON><PERSON> 10/19, Loss: 0.0113\n", "Epoch 7/50: Train Loss: 0.0138, Train Acc: 100.00%, Val Loss: 0.0189, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/19, Loss: 0.0123\n", "  Epoch 8/50, <PERSON><PERSON> 10/19, Loss: 0.0077\n", "Epoch 8/50: Train Loss: 0.0110, Train Acc: 100.00%, Val Loss: 0.0137, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/19, Loss: 0.0113\n", "  Epoch 9/50, <PERSON><PERSON> 10/19, Loss: 0.0080\n", "Epoch 9/50: Train Loss: 0.0083, Train Acc: 100.00%, Val Loss: 0.0106, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/19, Loss: 0.0062\n", "  Epoch 10/50, <PERSON><PERSON> 10/19, Loss: 0.0045\n", "Epoch 10/50: Train Loss: 0.0079, Train Acc: 100.00%, Val Loss: 0.0100, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/19, Loss: 0.0112\n", "  Epoch 11/50, <PERSON><PERSON> 10/19, Loss: 0.0076\n", "Epoch 11/50: Train Loss: 0.0066, Train Acc: 100.00%, Val Loss: 0.0088, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/19, Loss: 0.0067\n", "  Epoch 12/50, <PERSON><PERSON> 10/19, Loss: 0.0068\n", "Epoch 12/50: Train Loss: 0.0051, Train Acc: 100.00%, Val Loss: 0.0079, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/19, Loss: 0.0047\n", "  Epoch 13/50, <PERSON><PERSON> 10/19, Loss: 0.0038\n", "Epoch 13/50: Train Loss: 0.0050, Train Acc: 100.00%, Val Loss: 0.0069, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/19, Loss: 0.0032\n", "  Epoch 14/50, <PERSON><PERSON> 10/19, Loss: 0.0051\n", "Epoch 14/50: Train Loss: 0.0044, Train Acc: 100.00%, Val Loss: 0.0060, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/19, Loss: 0.0039\n", "  Epoch 15/50, <PERSON><PERSON> 10/19, Loss: 0.0039\n", "Epoch 15/50: Train Loss: 0.0040, Train Acc: 100.00%, Val Loss: 0.0058, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/19, Loss: 0.0040\n", "  Epoch 16/50, <PERSON><PERSON> 10/19, Loss: 0.0029\n", "Epoch 16/50: Train Loss: 0.0037, Train Acc: 100.00%, Val Loss: 0.0047, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/19, Loss: 0.0024\n", "  Epoch 17/50, <PERSON><PERSON> 10/19, Loss: 0.0035\n", "Epoch 17/50: Train Loss: 0.0034, Train Acc: 100.00%, Val Loss: 0.0037, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/19, Loss: 0.0041\n", "  Epoch 18/50, <PERSON><PERSON> 10/19, Loss: 0.0017\n", "Epoch 18/50: Train Loss: 0.0028, Train Acc: 100.00%, Val Loss: 0.0037, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/19, Loss: 0.0034\n", "  Epoch 19/50, <PERSON><PERSON> 10/19, Loss: 0.0027\n", "Epoch 19/50: Train Loss: 0.0026, Train Acc: 100.00%, Val Loss: 0.0035, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/19, Loss: 0.0033\n", "  Epoch 20/50, <PERSON><PERSON> 10/19, Loss: 0.0039\n", "Epoch 20/50: Train Loss: 0.0026, Train Acc: 100.00%, Val Loss: 0.0038, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/19, Loss: 0.0026\n", "  Epoch 21/50, <PERSON><PERSON> 10/19, Loss: 0.0063\n", "Epoch 21/50: Train Loss: 0.0026, Train Acc: 100.00%, Val Loss: 0.0034, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/19, Loss: 0.0022\n", "  Epoch 22/50, <PERSON><PERSON> 10/19, Loss: 0.0020\n", "Epoch 22/50: Train Loss: 0.0022, Train Acc: 100.00%, Val Loss: 0.0030, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/19, Loss: 0.0030\n", "  Epoch 23/50, <PERSON><PERSON> 10/19, Loss: 0.0012\n", "Epoch 23/50: Train Loss: 0.0019, Train Acc: 100.00%, Val Loss: 0.0026, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/19, Loss: 0.0013\n", "  Epoch 24/50, <PERSON><PERSON> 10/19, Loss: 0.0020\n", "Epoch 24/50: Train Loss: 0.0018, Train Acc: 100.00%, Val Loss: 0.0030, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/19, Loss: 0.0015\n", "  Epoch 25/50, <PERSON><PERSON> 10/19, Loss: 0.0013\n", "Epoch 25/50: Train Loss: 0.0018, Train Acc: 100.00%, Val Loss: 0.0028, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/19, Loss: 0.0013\n", "  Epoch 26/50, <PERSON><PERSON> 10/19, Loss: 0.0021\n", "Epoch 26/50: Train Loss: 0.0019, Train Acc: 100.00%, Val Loss: 0.0025, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/19, Loss: 0.0027\n", "  Epoch 27/50, <PERSON><PERSON> 10/19, Loss: 0.0020\n", "Epoch 27/50: Train Loss: 0.0018, Train Acc: 100.00%, Val Loss: 0.0022, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/19, Loss: 0.0013\n", "  Epoch 28/50, <PERSON><PERSON> 10/19, Loss: 0.0016\n", "Epoch 28/50: Train Loss: 0.0017, Train Acc: 100.00%, Val Loss: 0.0022, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/19, Loss: 0.0021\n", "  Epoch 29/50, <PERSON><PERSON> 10/19, Loss: 0.0011\n", "Epoch 29/50: Train Loss: 0.0015, Train Acc: 100.00%, Val Loss: 0.0021, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/19, Loss: 0.0013\n", "  Epoch 30/50, <PERSON><PERSON> 10/19, Loss: 0.0013\n", "Epoch 30/50: Train Loss: 0.0016, Train Acc: 100.00%, Val Loss: 0.0023, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/19, Loss: 0.0012\n", "  Epoch 31/50, <PERSON><PERSON> 10/19, Loss: 0.0016\n", "Epoch 31/50: Train Loss: 0.0016, Train Acc: 100.00%, Val Loss: 0.0021, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/19, Loss: 0.0017\n", "  Epoch 32/50, <PERSON><PERSON> 10/19, Loss: 0.0013\n", "Epoch 32/50: Train Loss: 0.0017, Train Acc: 100.00%, Val Loss: 0.0020, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/19, Loss: 0.0014\n", "  Epoch 33/50, <PERSON><PERSON> 10/19, Loss: 0.0013\n", "Epoch 33/50: Train Loss: 0.0012, Train Acc: 100.00%, Val Loss: 0.0016, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/19, Loss: 0.0011\n", "  Epoch 34/50, <PERSON><PERSON> 10/19, Loss: 0.0018\n", "Epoch 34/50: Train Loss: 0.0015, Train Acc: 100.00%, Val Loss: 0.0019, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/19, Loss: 0.0012\n", "  Epoch 35/50, <PERSON><PERSON> 10/19, Loss: 0.0013\n", "Epoch 35/50: Train Loss: 0.0012, Train Acc: 100.00%, Val Loss: 0.0019, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/19, Loss: 0.0008\n", "  Epoch 36/50, <PERSON><PERSON> 10/19, Loss: 0.0020\n", "Epoch 36/50: Train Loss: 0.0012, Train Acc: 100.00%, Val Loss: 0.0018, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/19, Loss: 0.0020\n", "  Epoch 37/50, <PERSON><PERSON> 10/19, Loss: 0.0012\n", "Epoch 37/50: Train Loss: 0.0013, Train Acc: 100.00%, Val Loss: 0.0020, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/19, Loss: 0.0009\n", "  Epoch 38/50, <PERSON><PERSON> 10/19, Loss: 0.0010\n", "Epoch 38/50: Train Loss: 0.0013, Train Acc: 100.00%, Val Loss: 0.0019, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/19, Loss: 0.0011\n", "  Epoch 39/50, <PERSON><PERSON> 10/19, Loss: 0.0010\n", "Epoch 39/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0017, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/19, Loss: 0.0006\n", "  Epoch 40/50, <PERSON><PERSON> 10/19, Loss: 0.0011\n", "Epoch 40/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0016, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/19, Loss: 0.0014\n", "  Epoch 41/50, <PERSON><PERSON> 10/19, Loss: 0.0007\n", "Epoch 41/50: Train Loss: 0.0011, Train Acc: 100.00%, Val Loss: 0.0018, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/19, Loss: 0.0008\n", "  Epoch 42/50, <PERSON><PERSON> 10/19, Loss: 0.0008\n", "Epoch 42/50: Train Loss: 0.0009, Train Acc: 100.00%, Val Loss: 0.0016, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/19, Loss: 0.0008\n", "  Epoch 43/50, <PERSON><PERSON> 10/19, Loss: 0.0008\n", "Epoch 43/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0014, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/19, Loss: 0.0012\n", "  Epoch 44/50, <PERSON><PERSON> 10/19, Loss: 0.0009\n", "Epoch 44/50: Train Loss: 0.0013, Train Acc: 100.00%, Val Loss: 0.0017, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/19, Loss: 0.0006\n", "  Epoch 45/50, <PERSON><PERSON> 10/19, Loss: 0.0008\n", "Epoch 45/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0014, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/19, Loss: 0.0010\n", "  Epoch 46/50, <PERSON><PERSON> 10/19, Loss: 0.0007\n", "Epoch 46/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0014, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/19, Loss: 0.0009\n", "  Epoch 47/50, <PERSON><PERSON> 10/19, Loss: 0.0012\n", "Epoch 47/50: Train Loss: 0.0010, Train Acc: 100.00%, Val Loss: 0.0014, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/19, Loss: 0.0008\n", "  Epoch 48/50, <PERSON><PERSON> 10/19, Loss: 0.0008\n", "Epoch 48/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0011, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/19, Loss: 0.0014\n", "  Epoch 49/50, <PERSON><PERSON> 10/19, Loss: 0.0006\n", "Epoch 49/50: Train Loss: 0.0011, Train Acc: 100.00%, Val Loss: 0.0015, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/19, Loss: 0.0008\n", "  Epoch 50/50, Bat<PERSON> 10/19, Loss: 0.0007\n", "Epoch 50/50: Train Loss: 0.0008, Train Acc: 100.00%, Val Loss: 0.0012, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "\n", "Training completed in 0.2 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING TRAINING ===\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/pointnet_plus_plus_buffer_kml_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nTraining completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Cross-Site Evaluation on RCPS"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 957}, "id": "test_evaluation", "outputId": "3164b177-e861-4d1a-81c6-36692b5bdb06"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== POINTNET++ CROSS-SITE EVALUATION ON RCPS (BUFFER KML) ===\n", "Testing PointNet++ trained on RES Buffer KML data on RCPS Buffer KML data...\n", "\n", "Cross-Site Test Results (RES→RCPS):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Predicted classes: [1]\n", "True classes: [1]\n", "\n", "Note: Model predicted only one class\n", "All predictions were: <PERSON><PERSON>\n", "Accuracy: 100.00%\n", "\n", "Actual distribution:\n", "  Piles: 1359\n", "  Non-Piles: 0\n", "\n", "Confusion Matrix:\n", "[[1359]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/pointnet_plus_plus_buffer_kml_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== POINTNET++ CROSS-SITE EVALUATION ON RCPS (BUFFER KML) ===\")\n", "print(\"Testing PointNet++ trained on RES Buffer KML data on RCPS Buffer KML data...\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nCross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "\n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix (this should still work)\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('PointNet++ Cross-Site Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "results_analysis"}, "source": ["## Results Analysis and Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "plot_training_curves", "outputId": "51f1de09-2b72-4aaa-b13e-a4e632b9839e"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x1000 with 4 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "=== FINAL RESULTS SUMMARY ===\n", "Training Site: Nortan RES (368 samples)\n", "Test Site: Althea RCPS (1359 samples)\n", "Best Validation Accuracy: 100.00%\n", "Cross-Site Test Accuracy: 100.00%\n", "Cross-Site Test F1-Score: 1.0000\n", "\n", "Comparison with Classical ML:\n", "  Classical ML (local): 100.0% accuracy\n", "  PointNet++ (cross-site): 100.00% accuracy\n", "  Generalization gap: 0.00%\n"]}], "source": ["# Plot training curves\n", "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Loss curves\n", "ax1.plot(train_losses, label='Training Loss', color='blue')\n", "ax1.plot(val_losses, label='Validation Loss', color='red')\n", "ax1.set_title('Training and Validation Loss')\n", "ax1.set_xlabel('Epoch')\n", "ax1.set_ylabel('Loss')\n", "ax1.legend()\n", "ax1.grid(True)\n", "\n", "# Accuracy curves\n", "ax2.plot(train_accs, label='Training Accuracy', color='blue')\n", "ax2.plot(val_accs, label='Validation Accuracy', color='red')\n", "ax2.set_title('Training and Validation Accuracy')\n", "ax2.set_xlabel('Epoch')\n", "ax2.set_ylabel('Accuracy (%)')\n", "ax2.legend()\n", "ax2.grid(True)\n", "\n", "# Class distribution comparison\n", "sites = ['RES (Train)', 'RCPS (Test)']\n", "pile_counts = [np.sum(train_labels), np.sum(test_labels)]\n", "non_pile_counts = [len(train_labels) - np.sum(train_labels), len(test_labels) - np.sum(test_labels)]\n", "\n", "x = np.arange(len(sites))\n", "width = 0.35\n", "\n", "ax3.bar(x - width/2, pile_counts, width, label='Pile', color='orange')\n", "ax3.bar(x + width/2, non_pile_counts, width, label='Non-Pile', color='skyblue')\n", "ax3.set_title('Class Distribution by Site')\n", "ax3.set_xlabel('Site')\n", "ax3.set_ylabel('Number of Samples')\n", "ax3.set_xticks(x)\n", "ax3.set_xticklabels(sites)\n", "ax3.legend()\n", "ax3.grid(True, alpha=0.3)\n", "\n", "# Performance comparison (placeholder for classical ML comparison)\n", "methods = ['Classical ML\\n(Local)', 'PointNet++\\n(Cross-Site)']\n", "accuracies = [100.0, test_acc]  # Classical ML achieved 100% on both sites\n", "f1_scores = [1.0, test_f1]  # Classical ML achieved perfect F1\n", "\n", "x = np.arange(len(methods))\n", "ax4.bar(x - 0.2, accuracies, 0.4, label='Accuracy (%)', color='lightgreen')\n", "ax4.bar(x + 0.2, [f*100 for f in f1_scores], 0.4, label='F1-Score (%)', color='lightcoral')\n", "ax4.set_title('Performance Comparison')\n", "ax4.set_xlabel('Method')\n", "ax4.set_ylabel('Performance (%)')\n", "ax4.set_xticks(x)\n", "ax4.set_xticklabels(methods)\n", "ax4.legend()\n", "ax4.grid(True, alpha=0.3)\n", "ax4.set_ylim(0, 105)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Summary statistics\n", "print(\"\\n=== FINAL RESULTS SUMMARY ===\")\n", "print(f\"Training Site: Nortan RES ({len(train_labels)} samples)\")\n", "print(f\"Test Site: Althea RCPS ({len(test_labels)} samples)\")\n", "print(f\"Best Validation Accuracy: {best_val_acc:.2f}%\")\n", "print(f\"Cross-Site Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"Cross-Site Test F1-Score: {test_f1:.4f}\")\n", "print(f\"\\nComparison with Classical ML:\")\n", "print(f\"  Classical ML (local): 100.0% accuracy\")\n", "print(f\"  PointNet++ (cross-site): {test_acc:.2f}% accuracy\")\n", "print(f\"  Generalization gap: {100.0 - test_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "save_results"}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "save_final_results", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c112d6db-4af1-44aa-f9c7-8fd4501d8f04"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Results saved:\n", "  Training history: pointnet_training_history_20250814_112647.json\n", "  Predictions: pointnet_rcps_predictions_20250814_112647.csv\n", "  Model: pointnet_best_model.pth\n", "\n", "=== POINTNET++ CROSS-SITE TRAINING COMPLETE ===\n", "Successfully trained on RES data and tested on RCPS data\n", "Cross-site generalization accuracy: 100.00%\n"]}], "source": ["# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size\n", "}\n", "\n", "with open(f'{models_path}/pointnet_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/pointnet_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"Results saved:\")\n", "print(f\"  Training history: pointnet_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: pointnet_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: pointnet_best_model.pth\")\n", "\n", "print(\"\\n=== POINTNET++ CROSS-SITE TRAINING COMPLETE ===\")\n", "print(f\"Successfully trained on RES data and tested on RCPS data\")\n", "print(f\"Cross-site generalization accuracy: {test_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "csv_export"}, "source": ["## Enhanced CSV Export for Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "export_validation_csv", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "1126ca35-12eb-4d96-c663-75910d940b6b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== CREATING VALIDATION CSV EXPORT ===\n", "✅ Validation CSV exported: /content/drive/MyDrive/pointnet_pile_detection/models/pointnet_plus_plus_pile_locations_20250814_112647.csv\n", "   Total pile locations: 1727\n", "   RES training piles: 368\n", "   RCPS test piles: 1359\n", "   Patch parameters: 3.0m radius, 64 points (matches Classical ML)\n", "\n", "=== ALL TRAINING OUTPUTS ===\n", "📊 Training metrics: pointnet_training_history_20250814_112647.json\n", "🎯 Model weights: pointnet_plus_plus_buffer_kml_best_model.pth\n", "📈 Test predictions: pointnet_rcps_predictions_20250814_112647.csv\n", "📍 Pile locations: pointnet_plus_plus_pile_locations_20250814_112647.csv\n", "\n", "🎉 Ready for inference testing!\n"]}], "source": ["# Enhanced CSV export with pile coordinates for validation\n", "print(\"\\n=== CREATING VALIDATION CSV EXPORT ===\")\n", "\n", "# Create validation dataset with pile coordinates\n", "validation_data = []\n", "\n", "# Add RES training pile locations with predictions\n", "for i, (pile_x, pile_y) in enumerate(res_pile_coords):\n", "    validation_data.append({\n", "        'pile_id': f'RES_TRAIN_{i+1}',\n", "        'site_name': 'nortan_res',\n", "        'utm_x': pile_x,\n", "        'utm_y': pile_y,\n", "        'data_split': 'training',\n", "        'ground_truth': 'PILE',\n", "        'source': '<PERSON><PERSON>er_KML',\n", "        'patch_radius': 3.0,\n", "        'points_per_patch': 64,\n", "        'model_architecture': 'PointNet++',\n", "        'training_timestamp': timestamp\n", "    })\n", "\n", "# Add RCPS test pile locations with predictions\n", "for i, (pile_x, pile_y) in enumerate(rcps_pile_coords):\n", "    validation_data.append({\n", "        'pile_id': f'RCPS_TEST_{i+1}',\n", "        'site_name': 'althea_rcps',\n", "        'utm_x': pile_x,\n", "        'utm_y': pile_y,\n", "        'data_split': 'testing',\n", "        'ground_truth': 'PILE',\n", "        'source': '<PERSON><PERSON>er_KML',\n", "        'patch_radius': 3.0,\n", "        'points_per_patch': 64,\n", "        'model_architecture': 'PointNet++',\n", "        'training_timestamp': timestamp\n", "    })\n", "\n", "# Create DataFrame and save\n", "validation_df = pd.DataFrame(validation_data)\n", "validation_csv = f'{models_path}/pointnet_plus_plus_pile_locations_{timestamp}.csv'\n", "validation_df.to_csv(validation_csv, index=False)\n", "\n", "print(f\"✅ Validation CSV exported: {validation_csv}\")\n", "print(f\"   Total pile locations: {len(validation_df)}\")\n", "print(f\"   RES training piles: {len(res_pile_coords)}\")\n", "print(f\"   RCPS test piles: {len(rcps_pile_coords)}\")\n", "print(f\"   Patch parameters: 3.0m radius, 64 points (matches Classical ML)\")\n", "\n", "# Summary of all outputs\n", "print(f\"\\n=== ALL TRAINING OUTPUTS ===\")\n", "print(f\"📊 Training metrics: pointnet_training_history_{timestamp}.json\")\n", "print(f\"🎯 Model weights: pointnet_plus_plus_buffer_kml_best_model.pth\")\n", "print(f\"📈 Test predictions: pointnet_rcps_predictions_{timestamp}.csv\")\n", "print(f\"📍 Pile locations: pointnet_plus_plus_pile_locations_{timestamp}.csv\")\n", "print(f\"\\n🎉 Ready for inference testing!\")"]}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "machine_shape": "hm", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}