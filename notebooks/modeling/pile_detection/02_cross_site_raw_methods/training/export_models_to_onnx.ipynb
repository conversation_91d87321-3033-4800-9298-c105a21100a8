{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Export Trained Models to ONNX Format\n", "\n", "This notebook exports all trained PyTorch models to ONNX format for deployment.\n", "ONNX models avoid architecture mismatch issues during inference.\n", "\n", "**Benefits of ONNX:**\n", "- Architecture independence - no need for exact helper functions\n", "- Version compatibility - works across PyTorch versions\n", "- Standardized format - industry standard for model deployment\n", "- Often faster inference than PyTorch"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n", "Models directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/02_cross_site_raw_methods/training\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "from pathlib import Path\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")\n", "\n", "# Model paths\n", "models_path = Path('.')\n", "print(f\"Models directory: {models_path.absolute()}\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Simple PointNet architecture defined\n"]}], "source": ["# Simple PointNet Architecture - EXACT FROM TRAINING\n", "class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2, num_points=64):\n", "        super(SimplePointNet, self).__init__()\n", "        \n", "        # Point-wise MLPs\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "        \n", "        # Batch normalization\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "        \n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, num_points) - matches training\n", "        # Point-wise convolutions\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "        \n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        return x\n", "\n", "print(\"Simple PointNet architecture defined\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PointNet Plus Plus architecture defined\n"]}], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(xyz, points)\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        S = self.npoint\n", "\n", "        # Use random sampling instead of farthest point sampling\n", "        fps_idx = torch.randint(0, N, (B, S), device=xyz.device, dtype=torch.long)\n", "        new_xyz = self.index_points(xyz, fps_idx)\n", "\n", "        # Simplified ball query - use k-nearest neighbors\n", "        idx = self.knn_query(xyz, new_xyz, self.nsample)\n", "        grouped_xyz = self.index_points(xyz, idx)\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = self.index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points\n", "\n", "    def knn_query(self, xyz, new_xyz, k):\n", "        \"\"\"Simplified k-nearest neighbor query\"\"\"\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "\n", "        # Compute pairwise distances\n", "        xyz_expanded = xyz.unsqueeze(2)  # (B, N, 1, C)\n", "        new_xyz_expanded = new_xyz.unsqueeze(1)  # (B, 1, S, C)\n", "\n", "        # Calculate squared distances\n", "        dists = torch.sum((xyz_expanded - new_xyz_expanded) ** 2, dim=-1)  # (B, N, S)\n", "\n", "        # Get k nearest neighbors for each query point\n", "        # FIXED: use dim=1 instead of dim=0, and ensure correct batch handling\n", "        _, idx = torch.topk(dists, k, dim=1, largest=False)  # (B, k, S)\n", "        idx = idx.permute(0, 2, 1)  # (B, S, k)\n", "\n", "        return idx\n", "\n", "    def index_points(self, points, idx):\n", "        \"\"\"\n", "        Input:\n", "            points: input points data, [B, N, C]\n", "            idx: sample index data, [B, S] or [B, S, K]\n", "        Return:\n", "            new_points:, indexed points data, [B, S, C] or [B, S, K, C]\n", "        \"\"\"\n", "        B, N, C = points.shape\n", "        device = points.device\n", "\n", "        # Handle out of bounds indices\n", "        idx = torch.clamp(idx, 0, N-1)\n", "\n", "        # Use torch.gather - most reliable approach\n", "        if len(idx.shape) == 2:  # [B, S]\n", "            idx_expanded = idx.unsqueeze(-1).expand(-1, -1, C)\n", "            return torch.gather(points, 1, idx_expanded)\n", "        else:  # [B, S, K]\n", "            B, <PERSON>, K = idx.shape\n", "            # Flatten to 2D, gather, then reshape back\n", "            idx_2d = idx.reshape(B, S*K)  # Changed from .view() to .reshape()\n", "            idx_expanded = idx_2d.unsqueeze(-1).expand(-1, -1, C)\n", "            gathered = torch.gather(points, 1, idx_expanded)\n", "            return gathered.reshape(B, S, K, C)  # Changed from .view() to .reshape()\n", "\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Simplified set abstraction layers\n", "        # self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels + 3, [32, 32, 64], False)\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 16, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 128 + 3, [128, 256, 512], True)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(512, 256)\n", "        self.bn1 = nn.BatchNorm1d(256)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.Linear(256, 128)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "\n", "        # Set abstraction layers\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        # Classification\n", "        x = l3_points.view(B, 512)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x\n", "\n", "print(\"PointNet Plus Plus architecture defined\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DGCNN architecture defined\n"]}], "source": ["# DGCNN Architecture - EXACT FROM TRAINING\n", "def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Construct edge features\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "\n", "    if idx is None:\n", "        idx = knn(x, k=k)   # (batch_size, num_points, k)\n", "\n", "    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "\n", "    idx = idx + idx_base\n", "\n", "    idx = idx.view(-1)\n", "\n", "    _, num_dims, _ = x.size()\n", "\n", "    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims)\n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "\n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "\n", "    return feature      # (batch_size, 2*num_dims, num_points, k)\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20, dropout=0.5):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.<PERSON>chNorm1d(1024)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "\n", "        # Classification head\n", "        self.linear1 = nn.Linear(1024*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=dropout)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=dropout)\n", "        self.linear3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "\n", "        # EdgeConv layers\n", "        x = get_graph_feature(x, k=self.k)      # (batch_size, 3, num_points) -> (batch_size, 3*2, num_points, k)\n", "        x = self.conv1(x)                       # (batch_size, 3*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x1, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv2(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 64, num_points, k)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 64, num_points, k) -> (batch_size, 64, num_points)\n", "\n", "        x = get_graph_feature(x2, k=self.k)     # (batch_size, 64, num_points) -> (batch_size, 64*2, num_points, k)\n", "        x = self.conv3(x)                       # (batch_size, 64*2, num_points, k) -> (batch_size, 128, num_points, k)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 128, num_points, k) -> (batch_size, 128, num_points)\n", "\n", "        x = get_graph_feature(x3, k=self.k)     # (batch_size, 128, num_points) -> (batch_size, 128*2, num_points, k)\n", "        x = self.conv4(x)                       # (batch_size, 128*2, num_points, k) -> (batch_size, 256, num_points, k)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]    # (batch_size, 256, num_points, k) -> (batch_size, 256, num_points)\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)  # (batch_size, 64+64+128+256, num_points)\n", "\n", "        x = self.conv5(x)                       # (batch_size, 512, num_points) -> (batch_size, 1024, num_points)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)           # (batch_size, 1024, num_points) -> (batch_size, 1024)\n", "        x = torch.cat((x1, x2), 1)              # (batch_size, 1024*2)\n", "\n", "        # Classification\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2) # (batch_size, 1024*2) -> (batch_size, 512)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2) # (batch_size, 512) -> (batch_size, 256)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)                                             # (batch_size, 256) -> (batch_size, num_classes)\n", "\n", "        return x\n", "    \n", "print(\"DGCNN architecture defined\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ONNX export function defined\n"]}], "source": ["def export_model_to_onnx(model, model_name, pth_path, onnx_path, input_shape=(1, 3, 64)):\n", "    \"\"\"Export a PyTorch model to ONNX format\"\"\"\n", "    print(f\"\\n=== EXPORTING {model_name.upper()} TO ONNX ===\")\n", "    \n", "    try:\n", "        # Load trained model\n", "        if not Path(pth_path).exists():\n", "            print(f\"Model file not found: {pth_path}\")\n", "            return False\n", "            \n", "        model.load_state_dict(torch.load(pth_path, map_location=device))\n", "        model.eval()\n", "        print(f\"Loaded model from: {pth_path}\")\n", "        \n", "        # Create dummy input\n", "        dummy_input = torch.randn(*input_shape).to(device)\n", "        print(f\"Created dummy input with shape: {input_shape}\")\n", "        \n", "        # Test PyTorch model first\n", "        with torch.no_grad():\n", "            torch_output = model(dummy_input)\n", "            print(f\"PyTorch model output shape: {torch_output.shape}\")\n", "        \n", "        # Export to ONNX\n", "        torch.onnx.export(\n", "            model,\n", "            dummy_input,\n", "            onnx_path,\n", "            export_params=True,\n", "            opset_version=11,\n", "            do_constant_folding=True,\n", "            input_names=['input'],\n", "            output_names=['output'],\n", "            dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}\n", "        )\n", "        print(f\"Model exported to ONNX: {onnx_path}\")\n", "        \n", "        # Verify ONNX model\n", "        import onnx\n", "        onnx_model = onnx.load(onnx_path)\n", "        onnx.checker.check_model(onnx_model)\n", "        print(f\"ONNX model verification passed\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"ONNX export failed for {model_name}: {e}\")\n", "        return False\n", "\n", "print(\"ONNX export function defined\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EXPORTING ALL TRAINED MODELS TO ONNX FORMAT ===\n", "This will create ONNX versions of all trained PyTorch models.\n", "ONNX models avoid architecture mismatch issues during inference.\n", "\n", "\n", "=== EXPORTING SIMPLE POINTNET TO ONNX ===\n", "Loaded model from: ../inference/simplepointnet_fair_comparison_best_model.pth\n", "Created dummy input with shape: (1, 3, 64)\n", "PyTorch model output shape: torch.Size([1, 2])\n", "Model exported to ONNX: simplepointnet_fair_comparison_best_model.onnx\n", "ONNX model verification passed\n", "\n", "=== EXPORTING DGCNN TO ONNX ===\n", "Loaded model from: ../inference/dgcnn_fair_comparison_best_model.pth\n", "Created dummy input with shape: (1, 3, 256)\n", "PyTorch model output shape: torch.Size([1, 2])\n", "Model exported to ONNX: dgcnn_best_model.onnx\n", "ONNX model verification passed\n", "\n", "=== EXPORT SUMMARY ===\n", "Successfully exported: 2 models\n", "Simple PointNet\n", "DGCNN\n", "\n", "=== BENEFITS OF ONNX MODELS ===\n", "No architecture mismatch issues\n", "No need for exact helper functions\n", "Version compatibility across PyTorch versions\n", "Standardized format for deployment\n", "Often faster inference than PyTorch\n", "\n", "Use ONNX models for inference to avoid tensor shape issues!\n"]}], "source": ["# Export all trained models to ONNX\n", "print(\"=== EXPORTING ALL TRAINED MODELS TO ONNX FORMAT ===\")\n", "print(\"This will create ONNX versions of all trained PyTorch models.\")\n", "print(\"ONNX models avoid architecture mismatch issues during inference.\\n\")\n", "\n", "# Model configurations\n", "models_to_export = [\n", "    {\n", "        'name': 'Simple PointNet',\n", "        'model_class': SimplePointNet,\n", "        'pth_file': '../inference/simplepointnet_fair_comparison_best_model.pth',\n", "        'onnx_file': 'simplepointnet_fair_comparison_best_model.onnx',\n", "        'input_shape': (1, 3, 64)  # All models expect (batch, 3, num_points)\n", "    },\n", "     # {  # PointNet++ removed - not ONNX compatible\n", "        # 'name': 'PointNet++',  # REMOVED - not ONNX compatible\n", "        # 'model_class': PointNetPlusPlus,\n", "        # 'pth_file': '../inference/pointnet_plus_plus_buffer_kml_best_model.pth',\n", "        # 'onnx_file': 'pointnet_plus_plus_buffer_kml_best_model.onnx',\n", "        # 'input_shape': (1, 3, 64)  # All models expect (batch, 3, num_points)\n", "    # },  # PointNet++ end\n", "    {\n", "        'name': 'DGCNN',\n", "        'model_class': DGCNN,\n", "        'pth_file': '../inference/dgcnn_fair_comparison_best_model.pth',\n", "        'onnx_file': 'dgcnn_best_model.onnx',\n", "        'input_shape': (1, 3, 256)  # DGCNN uses 256 points from training\n", "    }\n", "]\n", "\n", "# Export each model\n", "successful_exports = []\n", "failed_exports = []\n", "\n", "for model_config in models_to_export:\n", "    # Initialize model\n", "    model = model_config['model_class']().to(device)\n", "    \n", "    # Set paths\n", "    pth_path = models_path / model_config['pth_file']\n", "    onnx_path = models_path / model_config['onnx_file']\n", "    \n", "    # Export to ONNX\n", "    success = export_model_to_onnx(\n", "        model=model,\n", "        model_name=model_config['name'],\n", "        pth_path=str(pth_path),\n", "        onnx_path=str(onnx_path),\n", "        input_shape=model_config['input_shape']\n", "    )\n", "    \n", "    if success:\n", "        successful_exports.append(model_config['name'])\n", "    else:\n", "        failed_exports.append(model_config['name'])\n", "\n", "# Summary\n", "print(f\"\\n=== EXPORT SUMMARY ===\")\n", "print(f\"Successfully exported: {len(successful_exports)} models\")\n", "for model_name in successful_exports:\n", "    print(f\"{model_name}\")\n", "\n", "if failed_exports:\n", "    print(f\"\\nFailed exports: {len(failed_exports)} models\")\n", "    for model_name in failed_exports:\n", "        print(f\"  {model_name}\")\n", "\n", "print(f\"\\n=== BENEFITS OF ONNX MODELS ===\")\n", "print(f\"No architecture mismatch issues\")\n", "print(f\"No need for exact helper functions\")\n", "print(f\"Version compatibility across PyTorch versions\")\n", "print(f\"Standardized format for deployment\")\n", "print(f\"Often faster inference than PyTorch\")\n", "print(f\"\\nUse ONNX models for inference to avoid tensor shape issues!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}