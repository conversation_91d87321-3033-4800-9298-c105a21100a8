{"cells": [{"cell_type": "markdown", "metadata": {"id": "title"}, "source": ["# SimplePointNet Cross-Site with Buffer KML (RES→RCPS)\n", "\n", "This notebook implements **SimplePointNet** using **Buffer KML ground truth**:\n", "- **Same point clouds**: `Block_11_2m.las` and `Point_Cloud.las`\n", "- **Same ground truth**: `Buffer_2m.kml` files (not CSV results)\n", "- **Same validation protocol**: Train on RES → Test on RCPS\n", "- **Same patch parameters**: 3m radius (like Classical ML)\n", "\n", "**Architecture Comparison:**\n", "- ✅ **Classical ML**: Extracts 22 statistical features from 3D coordinates\n", "- ✅ **SimplePointNet**: Uses raw 3D coordinates directly\n", "- ✅ **PointNet++**: Full hierarchical feature learning (separate notebook)\n", "- ✅ **Fair comparison**: All use identical Buffer KML data sources\n", "\n", "**Key Changes:**\n", "- ✅ Patch radius: 3.0m (matches successful Classical ML)\n", "- ✅ Points per patch: 64 (matches successful Classical ML)\n", "- ✅ CSV export for validation\n", "\n", "**Research Question**: How do different architectures perform on same Buffer KML data?\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025  \n", "**Purpose**: Dissertation Research - SimplePointNet Buffer KML Validation\n"]}, {"cell_type": "markdown", "metadata": {"id": "setup"}, "source": ["## Setup and Mount Google Drive"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "mount_drive", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "c403829a-20d5-4570-c5fb-98ec409b9272"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n", "Project path: /content/drive/MyDrive/pointnet_pile_detection\n", "Data path: /content/drive/MyDrive/pointnet_pile_detection/data\n", "Models path: /content/drive/MyDrive/pointnet_pile_detection/models\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')\n", "\n", "# Project paths\n", "GDRIVE_BASE = \"/content/drive/MyDrive\"\n", "PROJECT_FOLDER = \"pointnet_pile_detection\"\n", "project_path = f\"{GDRIVE_BASE}/{PROJECT_FOLDER}\"\n", "data_path = f\"{project_path}/data\"\n", "models_path = f\"{project_path}/models\"\n", "\n", "print(f\"Project path: {project_path}\")\n", "print(f\"Data path: {data_path}\")\n", "print(f\"Models path: {models_path}\")"]}, {"cell_type": "markdown", "metadata": {"id": "imports"}, "source": ["## Install Dependencies and Imports"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "install_deps", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "e23a2564-cba0-48e7-f37e-85cc1df3d5e5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting laspy\n", "  Downloading laspy-2.6.1-py3-none-any.whl.metadata (3.8 kB)\n", "Requirement already satisfied: geopandas in /usr/local/lib/python3.11/dist-packages (1.1.1)\n", "Requirement already satisfied: scikit-learn in /usr/local/lib/python3.11/dist-packages (1.6.1)\n", "Collecting mlflow\n", "  Downloading mlflow-3.2.0-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from laspy) (2.0.2)\n", "Requirement already satisfied: pyogrio>=0.7.2 in /usr/local/lib/python3.11/dist-packages (from geopandas) (0.11.1)\n", "Requirement already satisfied: packaging in /usr/local/lib/python3.11/dist-packages (from geopandas) (25.0)\n", "Requirement already satisfied: pandas>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.2.2)\n", "Requirement already satisfied: pyproj>=3.5.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (3.7.1)\n", "Requirement already satisfied: shapely>=2.0.0 in /usr/local/lib/python3.11/dist-packages (from geopandas) (2.1.1)\n", "Requirement already satisfied: scipy>=1.6.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.16.1)\n", "Requirement already satisfied: joblib>=1.2.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (1.5.1)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn) (3.6.0)\n", "Collecting mlflow-skinny==3.2.0 (from mlflow)\n", "  Downloading mlflow_skinny-3.2.0-py3-none-any.whl.metadata (30 kB)\n", "Collecting mlflow-tracing==3.2.0 (from mlflow)\n", "  Downloading mlflow_tracing-3.2.0-py3-none-any.whl.metadata (19 kB)\n", "Requirement already satisfied: Flask<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.1.1)\n", "Collecting alembic!=1.10.0,<2 (from mlflow)\n", "  Downloading alembic-1.16.4-py3-none-any.whl.metadata (7.3 kB)\n", "Collecting docker<8,>=4.0.0 (from mlflow)\n", "  Downloading docker-7.1.0-py3-none-any.whl.metadata (3.8 kB)\n", "Collecting graphene<4 (from mlflow)\n", "  Downloading graphene-3.4.3-py2.py3-none-any.whl.metadata (6.9 kB)\n", "Collecting gunicorn<24 (from mlflow)\n", "  Downloading gunicorn-23.0.0-py3-none-any.whl.metadata (4.4 kB)\n", "Requirement already satisfied: matplotlib<4 in /usr/local/lib/python3.11/dist-packages (from mlflow) (3.10.0)\n", "Requirement already satisfied: pyarrow<22,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (18.1.0)\n", "Requirement already satisfied: sqlalchemy<3,>=1.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow) (2.0.43)\n", "Requirement already satisfied: cachetools<7,>=5.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.5.2)\n", "Requirement already satisfied: click<9,>=7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.2.1)\n", "Requirement already satisfied: cloudpickle<4 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.1)\n", "Collecting databricks-sdk<1,>=0.20.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading databricks_sdk-0.63.0-py3-none-any.whl.metadata (39 kB)\n", "Requirement already satisfied: fastapi<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.116.1)\n", "Requirement already satisfied: gitpython<4,>=3.1.9 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (3.1.45)\n", "Requirement already satisfied: importlib_metadata!=4.7.0,<9,>=3.7.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (8.7.0)\n", "Collecting opentelemetry-api<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_api-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting opentelemetry-sdk<3,>=1.9.0 (from mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_sdk-1.36.0-py3-none-any.whl.metadata (1.5 kB)\n", "Requirement already satisfied: protobuf<7,>=3.12.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (5.29.5)\n", "Requirement already satisfied: pydantic<3,>=1.10.8 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.11.7)\n", "Requirement already satisfied: pyyaml<7,>=5.1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (6.0.2)\n", "Requirement already satisfied: requests<3,>=2.17.3 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (2.32.3)\n", "Requirement already satisfied: sqlparse<1,>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.5.3)\n", "Requirement already satisfied: typing-extensions<5,>=4.0.0 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (4.14.1)\n", "Requirement already satisfied: uvicorn<1 in /usr/local/lib/python3.11/dist-packages (from mlflow-skinny==3.2.0->mlflow) (0.35.0)\n", "Requirement already satisfied: <PERSON><PERSON> in /usr/lib/python3/dist-packages (from alembic!=1.10.0,<2->mlflow) (1.1.3)\n", "Requirement already satisfied: urllib3>=1.26.0 in /usr/local/lib/python3.11/dist-packages (from docker<8,>=4.0.0->mlflow) (2.5.0)\n", "Requirement already satisfied: blinker>=1.9.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (1.9.0)\n", "Requirement already satisfied: itsdangerous>=2.2.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (2.2.0)\n", "Requirement already satisfied: jinja2>=3.1.2 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.6)\n", "Requirement already satisfied: markupsafe>=2.1.1 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.0.2)\n", "Requirement already satisfied: werkzeug>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from Flask<4->mlflow) (3.1.3)\n", "Collecting graphql-core<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_core-3.2.6-py3-none-any.whl.metadata (11 kB)\n", "Collecting graphql-relay<3.3,>=3.1 (from graphene<4->mlflow)\n", "  Downloading graphql_relay-3.2.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: python-dateutil<3,>=2.7.0 in /usr/local/lib/python3.11/dist-packages (from graphene<4->mlflow) (2.9.0.post0)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.3.3)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (4.59.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (1.4.9)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (11.3.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib<4->mlflow) (3.2.3)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=2.0.0->geopandas) (2025.2)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from pyogrio>=0.7.2->geopandas) (2025.8.3)\n", "Requirement already satisfied: greenlet>=1 in /usr/local/lib/python3.11/dist-packages (from sqlalchemy<3,>=1.4.0->mlflow) (3.2.4)\n", "Requirement already satisfied: google-auth~=2.0 in /usr/local/lib/python3.11/dist-packages (from databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (2.38.0)\n", "Requirement already satisfied: starlette<0.48.0,>=0.40.0 in /usr/local/lib/python3.11/dist-packages (from fastapi<1->mlflow-skinny==3.2.0->mlflow) (0.47.2)\n", "Requirement already satisfied: gitdb<5,>=4.0.1 in /usr/local/lib/python3.11/dist-packages (from gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (4.0.12)\n", "Requirement already satisfied: zipp>=3.20 in /usr/local/lib/python3.11/dist-packages (from importlib_metadata!=4.7.0,<9,>=3.7.0->mlflow-skinny==3.2.0->mlflow) (3.23.0)\n", "Collecting opentelemetry-semantic-conventions==0.57b0 (from opentelemetry-sdk<3,>=1.9.0->mlflow-skinny==3.2.0->mlflow)\n", "  Downloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl.metadata (2.4 kB)\n", "Requirement already satisfied: annotated-types>=0.6.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.2 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (2.33.2)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in /usr/local/lib/python3.11/dist-packages (from pydantic<3,>=1.10.8->mlflow-skinny==3.2.0->mlflow) (0.4.1)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil<3,>=2.7.0->graphene<4->mlflow) (1.17.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.4.3)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests<3,>=2.17.3->mlflow-skinny==3.2.0->mlflow) (3.10)\n", "Requirement already satisfied: h11>=0.8 in /usr/local/lib/python3.11/dist-packages (from uvicorn<1->mlflow-skinny==3.2.0->mlflow) (0.16.0)\n", "Requirement already satisfied: smmap<6,>=3.0.1 in /usr/local/lib/python3.11/dist-packages (from gitdb<5,>=4.0.1->gitpython<4,>=3.1.9->mlflow-skinny==3.2.0->mlflow) (5.0.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.4.2)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in /usr/local/lib/python3.11/dist-packages (from google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (4.9.1)\n", "Requirement already satisfied: anyio<5,>=3.6.2 in /usr/local/lib/python3.11/dist-packages (from starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (4.10.0)\n", "Requirement already satisfied: sniffio>=1.1 in /usr/local/lib/python3.11/dist-packages (from anyio<5,>=3.6.2->starlette<0.48.0,>=0.40.0->fastapi<1->mlflow-skinny==3.2.0->mlflow) (1.3.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.6.1 in /usr/local/lib/python3.11/dist-packages (from pyasn1-modules>=0.2.1->google-auth~=2.0->databricks-sdk<1,>=0.20.0->mlflow-skinny==3.2.0->mlflow) (0.6.1)\n", "Downloading laspy-2.6.1-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.1/86.1 kB\u001b[0m \u001b[31m4.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow-3.2.0-py3-none-any.whl (25.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m25.8/25.8 MB\u001b[0m \u001b[31m81.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_skinny-3.2.0-py3-none-any.whl (2.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m2.0/2.0 MB\u001b[0m \u001b[31m92.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading mlflow_tracing-3.2.0-py3-none-any.whl (1.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.1/1.1 MB\u001b[0m \u001b[31m69.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading alembic-1.16.4-py3-none-any.whl (247 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m247.0/247.0 kB\u001b[0m \u001b[31m20.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading docker-7.1.0-py3-none-any.whl (147 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m147.8/147.8 kB\u001b[0m \u001b[31m15.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphene-3.4.3-py2.py3-none-any.whl (114 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m114.9/114.9 kB\u001b[0m \u001b[31m11.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading gunicorn-23.0.0-py3-none-any.whl (85 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m85.0/85.0 kB\u001b[0m \u001b[31m8.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading databricks_sdk-0.63.0-py3-none-any.whl (688 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m688.0/688.0 kB\u001b[0m \u001b[31m50.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_core-3.2.6-py3-none-any.whl (203 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m203.4/203.4 kB\u001b[0m \u001b[31m17.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading graphql_relay-3.2.0-py3-none-any.whl (16 kB)\n", "Downloading opentelemetry_api-1.36.0-py3-none-any.whl (65 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m65.6/65.6 kB\u001b[0m \u001b[31m6.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_sdk-1.36.0-py3-none-any.whl (119 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m120.0/120.0 kB\u001b[0m \u001b[31m12.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opentelemetry_semantic_conventions-0.57b0-py3-none-any.whl (201 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m201.6/201.6 kB\u001b[0m \u001b[31m20.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: laspy, gunicorn, graphql-core, opentelemetry-api, graphql-relay, docker, alembic, opentelemetry-semantic-conventions, graphene, databricks-sdk, opentelemetry-sdk, mlflow-tracing, mlflow-skinny, mlflow\n", "Successfully installed alembic-1.16.4 databricks-sdk-0.63.0 docker-7.1.0 graphene-3.4.3 graphql-core-3.2.6 graphql-relay-3.2.0 gunicorn-23.0.0 laspy-2.6.1 mlflow-3.2.0 mlflow-skinny-3.2.0 mlflow-tracing-3.2.0 opentelemetry-api-1.36.0 opentelemetry-sdk-1.36.0 opentelemetry-semantic-conventions-0.57b0\n", "Using device: cuda\n"]}], "source": ["# Install required packages\n", "!pip install laspy geopandas scikit-learn mlflow\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import torch.nn.functional as F\n", "from torch.utils.data import Dataset, DataLoader, random_split\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.metrics import accuracy_score, f1_score, classification_report, confusion_matrix\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import pickle\n", "import json\n", "import time\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set random seeds\n", "np.random.seed(42)\n", "torch.manual_seed(42)\n", "if torch.cuda.is_available():\n", "    torch.cuda.manual_seed(42)\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "markdown", "metadata": {"id": "data_loading"}, "source": ["## Data Loading (EXACT SAME as Classical ML)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "load_data_functions"}, "outputs": [], "source": ["def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N (SAME as Classical ML)\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "\n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "\n", "    pile_locations = np.array(pile_coords)\n", "\n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "\n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "\n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "\n", "    return pile_locations_utm\n", "\n", "def subsample_patch(patch_points, target_size=64):\n", "    \"\"\"Subsample patch to target size - FIXED to 64 points (matches Classical ML)\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        # Upsample with noise if needed\n", "        if len(patch_points) < target_size:\n", "            extra_needed = target_size - len(patch_points)\n", "            extra_indices = np.random.choice(len(patch_points), extra_needed, replace=True)\n", "            extra_points = patch_points[extra_indices] + np.random.normal(0, 0.01, (extra_needed, 3))\n", "            return np.vstack([patch_points, extra_points])\n", "        return patch_points\n", "\n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=3.0, min_points=20, target_size=64):\n", "    \"\"\"Extract patches around locations (SAME as Classical ML but more points)\"\"\"\n", "    print(f\"Extracting patches (radius={radius}m, min_points={min_points}, target_size={target_size})\")\n", "\n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    original_sizes = []\n", "\n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "\n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            original_sizes.append(len(patch_points))\n", "\n", "            # Subsample to target size\n", "            patch_points = subsample_patch(patch_points, target_size)\n", "\n", "            # Center patch (SAME as Classical ML)\n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "\n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "\n", "    print(f\"Extracted {len(patches)} valid patches\")\n", "    if original_sizes:\n", "        print(f\"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}\")\n", "        final_sizes = [len(p) for p in patches]\n", "        print(f\"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}\")\n", "\n", "    return patches, np.array(valid_locs)\n", "\n", "def create_negative_samples(points, pile_locations, n_negative, radius=3.0, min_points=20, target_size=64):\n", "    \"\"\"Create negative samples (SAME as Classical ML)\"\"\"\n", "    print(f\"Creating {n_negative} negative samples...\")\n", "\n", "    np.random.seed(42)\n", "    x_min, x_max = points[:, 0].min(), points[:, 0].max()\n", "    y_min, y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "    # Generate random locations (avoiding pile areas)\n", "    random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative * 3)  # Generate extra\n", "    random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative * 3)\n", "    random_locations = np.column_stack([random_x, random_y])\n", "\n", "    # Filter out locations too close to piles\n", "    pile_tree = cKDTree(pile_locations)\n", "    valid_negatives = []\n", "\n", "    for loc in random_locations:\n", "        distances, _ = pile_tree.query(loc, k=1)\n", "        if distances > radius * 2:  # At least 2x radius away from any pile\n", "            valid_negatives.append(loc)\n", "            if len(valid_negatives) >= n_negative:\n", "                break\n", "\n", "    valid_negatives = np.array(valid_negatives[:n_negative])\n", "\n", "    # Extract patches\n", "    neg_patches, _ = extract_patches(points, valid_negatives, radius, min_points, target_size)\n", "\n", "    return neg_patches"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "load_data_main", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "eee7adeb-d4c7-4a5c-dfca-bc429d9ce7c5"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== LOADING RES DATA (TRAINING SITE) - SAME AS CLASSICAL ML ===\n", "Loading RES point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Block_11_2m.las\n", "Loaded 35,565,352 points\n", "Loading RES pile locations: /content/drive/MyDrive/pointnet_pile_detection/data/nortan_res/Buffer_2m.kml\n", "Loaded 368 pile locations\n", "Extracting positive patches from RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 368 valid patches\n", "Original sizes: min=86195, max=152283, mean=96645.0\n", "Final sizes: min=64, max=64, mean=64.0\n", "Extracted 368 positive patches\n", "Creating 368 negative samples...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 0 valid patches\n", "Created 0 negative patches\n", "\n", "RES training dataset: 368 samples (368 positive, 0 negative)\n", "\n", "=== LOADING RCPS DATA (TEST SITE) - SAME AS CLASSICAL ML ===\n", "Loading RCPS point cloud: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "Loading RCPS pile locations: /content/drive/MyDrive/pointnet_pile_detection/data/althea_rpcs/Buffer_2m.kml\n", "Loaded 1359 pile locations\n", "Extracting positive patches from RCPS...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 1359 valid patches\n", "Original sizes: min=35562, max=67519, mean=38898.0\n", "Final sizes: min=64, max=64, mean=64.0\n", "Extracted 1359 positive patches\n", "Creating 1359 negative samples...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 0 valid patches\n", "Created 0 negative patches\n", "\n", "RCPS test dataset: 1359 samples (1359 positive, 0 negative)\n", "\n", "=== DATA SUMMARY (FAIR COMPARISON) ===\n", "✅ SAME point clouds as Classical ML\n", "✅ SAME ground truth (Buffer_2m.kml) as Classical ML\n", "✅ SAME patch radius (3m) as Classical ML\n", "✅ SAME validation protocol (RES→RCPS) as Classical ML\n", "📊 Classical ML: 22 statistical features from 64 points\n", "📊 SimplePointNet: Raw 3D coordinates from 64 points - FIXED\n", "🎯 Research Question: Raw coordinates vs engineered features\n"]}], "source": ["# STEP 1: LOAD RES DATA (TRAINING SITE) - EXACT SAME as Classical ML\n", "print(\"=== LOADING RES DATA (TRAINING SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RES point cloud\n", "print(f\"Loading RES point cloud: {data_path}/nortan_res/Block_11_2m.las\")\n", "res_las = laspy.read(f\"{data_path}/nortan_res/Block_11_2m.las\")\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"Loaded {len(res_points):,} points\")\n", "\n", "# Load RES pile locations (SAME file as Classical ML)\n", "print(f\"Loading RES pile locations: {data_path}/nortan_res/Buffer_2m.kml\")\n", "res_pile_locations = load_and_reproject_kml(f\"{data_path}/nortan_res/Buffer_2m.kml\")\n", "print(f\"Loaded {len(res_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius)\n", "print(f\"Extracting positive patches from RES...\")\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations, radius=3.0, min_points=20, target_size=64)\n", "print(f\"Extracted {len(res_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "res_neg_patches = create_negative_samples(res_points, res_pile_locations, len(res_pos_patches), radius=3.0, min_points=20, target_size=64)\n", "print(f\"Created {len(res_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRES training dataset: {len(res_pos_patches) + len(res_neg_patches)} samples ({len(res_pos_patches)} positive, {len(res_neg_patches)} negative)\")\n", "\n", "# STEP 2: LOAD RCPS DATA (TEST SITE) - EXACT SAME as Classical ML\n", "print(\"\\n=== LOADING RCPS DATA (TEST SITE) - SAME AS CLASSICAL ML ===\")\n", "\n", "# Load RCPS point cloud\n", "print(f\"Loading RCPS point cloud: {data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_las = laspy.read(f\"{data_path}/althea_rpcs/Point_Cloud.las\")\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"Loaded {len(rcps_points):,} points\")\n", "\n", "# Load RCPS pile locations (SAME file as Classical ML)\n", "print(f\"Loading RCPS pile locations: {data_path}/althea_rpcs/Buffer_2m.kml\")\n", "rcps_pile_locations = load_and_reproject_kml(f\"{data_path}/althea_rpcs/Buffer_2m.kml\")\n", "print(f\"Loaded {len(rcps_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (SAME as Classical ML: 3m radius)\n", "print(f\"Extracting positive patches from RCPS...\")\n", "rcps_pos_patches, _ = extract_patches(rcps_points, rcps_pile_locations, radius=3.0, min_points=20, target_size=64)\n", "print(f\"Extracted {len(rcps_pos_patches)} positive patches\")\n", "\n", "# Create negative samples (SAME as Classical ML)\n", "rcps_neg_patches = create_negative_samples(rcps_points, rcps_pile_locations, len(rcps_pos_patches), radius=3.0, min_points=20, target_size=64)\n", "print(f\"Created {len(rcps_neg_patches)} negative patches\")\n", "\n", "print(f\"\\nRCPS test dataset: {len(rcps_pos_patches) + len(rcps_neg_patches)} samples ({len(rcps_pos_patches)} positive, {len(rcps_neg_patches)} negative)\")\n", "\n", "print(\"\\n=== DATA SUMMARY (FAIR COMPARISON) ===\")\n", "print(f\"✅ SAME point clouds as Classical ML\")\n", "print(f\"✅ SAME ground truth (Buffer_2m.kml) as Classical ML\")\n", "print(f\"✅ SAME patch radius (3m) as Classical ML\")\n", "print(f\"✅ SAME validation protocol (RES→RCPS) as Classical ML\")\n", "print(f\"📊 Classical ML: 22 statistical features from 64 points\")\n", "print(f\"📊 SimplePointNet: Raw 3D coordinates from 64 points - FIXED\")\n", "print(f\"🎯 Research Question: Raw coordinates vs engineered features\")"]}, {"cell_type": "markdown", "metadata": {"id": "prepare_datasets"}, "source": ["## Prepare Datasets for SimplePointNet"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "create_datasets", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "b09888ea-35dc-473e-e40e-55f5acaff036"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Final dataset shapes:\n", "Training: (368, 64, 3), labels: (368,)\n", "Testing: (1359, 64, 3), labels: (1359,)\n", "Training class distribution: [  0 368]\n", "Testing class distribution: [   0 1359]\n", "\n", "Data integrity checks:\n", "Training patches - min: -1.989, max: 1.992\n", "Test patches - min: -1.998, max: 1.993\n", "No NaN values: True\n"]}], "source": ["# Prepare training data (RES)\n", "train_patches = []\n", "train_labels = []\n", "\n", "# Add positive patches\n", "for patch in res_pos_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in res_neg_patches:\n", "    train_patches.append(patch.astype(np.float32))\n", "    train_labels.append(0)\n", "\n", "# Prepare test data (RCPS)\n", "test_patches = []\n", "test_labels = []\n", "\n", "# Add positive patches\n", "for patch in rcps_pos_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(1)\n", "\n", "# Add negative patches\n", "for patch in rcps_neg_patches:\n", "    test_patches.append(patch.astype(np.float32))\n", "    test_labels.append(0)\n", "\n", "# Convert to numpy arrays\n", "train_patches = np.array(train_patches, dtype=np.float32)  # (N, 64, 3)\n", "train_labels = np.array(train_labels, dtype=np.int64)      # (N,)\n", "test_patches = np.array(test_patches, dtype=np.float32)    # (M, 64, 3)\n", "test_labels = np.array(test_labels, dtype=np.int64)        # (M,)\n", "\n", "print(f\"\\nFinal dataset shapes:\")\n", "print(f\"Training: {train_patches.shape}, labels: {train_labels.shape}\")\n", "print(f\"Testing: {test_patches.shape}, labels: {test_labels.shape}\")\n", "print(f\"Training class distribution: {np.bincount(train_labels)}\")\n", "print(f\"Testing class distribution: {np.bincount(test_labels)}\")\n", "\n", "# Verify data integrity\n", "print(f\"\\nData integrity checks:\")\n", "print(f\"Training patches - min: {train_patches.min():.3f}, max: {train_patches.max():.3f}\")\n", "print(f\"Test patches - min: {test_patches.min():.3f}, max: {test_patches.max():.3f}\")\n", "print(f\"No NaN values: {not np.isnan(train_patches).any() and not np.isnan(test_patches).any()}\")"]}, {"cell_type": "markdown", "metadata": {"id": "pointnet_architecture"}, "source": ["## SimplePointNet Architecture"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "pointnet_model"}, "outputs": [], "source": ["class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(SimplePointNet, self).__init__()\n", "\n", "        # Point-wise MLPs\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "\n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "\n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # x shape: (batch_size, 3, num_points)\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "\n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "\n", "        # Classification\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "\n", "        return x"]}, {"cell_type": "markdown", "metadata": {"id": "dataset_class"}, "source": ["## Dataset and DataLoader"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"id": "create_dataset_class", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "cb9779ef-e9f9-47c1-be85-09437ab9e4e8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Dataset sizes:\n", "  Training: 294\n", "  Validation: 74\n", "  Test (RCPS): 1359\n", "  Batch size: 4\n", "  Input shape: (batch_size, 3, 1024) for SimplePointNet\n"]}], "source": ["class FairComparisonDataset(Dataset):\n", "    def __init__(self, patches, labels):\n", "        # SimplePointNet expects (batch_size, 3, num_points)\n", "        self.patches = torch.FloatTensor(patches).transpose(2, 1)  # (N, 3, 64)\n", "        self.labels = torch.LongTensor(labels)\n", "\n", "    def __len__(self):\n", "        return len(self.patches)\n", "\n", "    def __getitem__(self, idx):\n", "        return self.patches[idx], self.labels[idx]\n", "\n", "# Create datasets\n", "train_dataset = FairComparisonDataset(train_patches, train_labels)\n", "test_dataset = FairComparisonDataset(test_patches, test_labels)\n", "\n", "# Create train/validation split from training data\n", "train_size = int(0.8 * len(train_dataset))\n", "val_size = len(train_dataset) - train_size\n", "train_subset, val_subset = random_split(train_dataset, [train_size, val_size])\n", "\n", "# Create data loaders\n", "batch_size = 4  # Small batch size for Colab\n", "train_loader = DataLoader(train_subset, batch_size=batch_size, shuffle=True)\n", "val_loader = DataLoader(val_subset, batch_size=batch_size, shuffle=False)\n", "test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)\n", "\n", "print(f\"Dataset sizes:\")\n", "print(f\"  Training: {len(train_subset)}\")\n", "print(f\"  Validation: {len(val_subset)}\")\n", "print(f\"  Test (RCPS): {len(test_dataset)}\")\n", "print(f\"  Batch size: {batch_size}\")\n", "print(f\"  Input shape: (batch_size, 3, 1024) for SimplePointNet\")"]}, {"cell_type": "markdown", "metadata": {"id": "training_setup"}, "source": ["## Training Setup and Execution"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "setup_training", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "8ed00610-ecdc-4a46-dfdc-c15c866793f0"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model initialized with 83,778 parameters\n", "Training configuration:\n", "  Epochs: 50\n", "  Learning rate: 0.001\n", "  Weight decay: 1e-4\n", "  Device: cuda\n", "  Data source: SAME as Classical ML (Buffer_2m.kml)\n", "  Validation: SAME as Classical ML (RES→RCPS)\n"]}], "source": ["# Initialize model\n", "model = SimplePointNet(num_classes=2).to(device)\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters()):,} parameters\")\n", "\n", "# Training configuration\n", "criterion = nn.CrossEntropyLoss()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001, weight_decay=1e-4)\n", "scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=20, gamma=0.7)\n", "\n", "# Training parameters\n", "num_epochs = 50  # Reduced for Colab\n", "best_val_acc = 0.0\n", "train_losses = []\n", "val_losses = []\n", "train_accs = []\n", "val_accs = []\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"  Epochs: {num_epochs}\")\n", "print(f\"  Learning rate: 0.001\")\n", "print(f\"  Weight decay: 1e-4\")\n", "print(f\"  Device: {device}\")\n", "print(f\"  Data source: SAME as Classical ML (Buffer_2m.kml)\")\n", "print(f\"  Validation: SAME as Classical ML (RES→RCPS)\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "training_loop", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "7d126320-509b-42b2-b8d4-4f92f0b49179"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "=== STARTING FAIR COMPARISON TRAINING ===\n", "SimplePointNet vs Classical ML using IDENTICAL data sources\n", "  Epoch 1/50, <PERSON><PERSON> 0/74, Loss: 0.7183\n", "  Epoch 1/50, <PERSON><PERSON> 10/74, Loss: 0.0017\n", "  Epoch 1/50, <PERSON><PERSON> 20/74, Loss: 0.0004\n", "  Epoch 1/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 1/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "  *** New best model saved! Validation accuracy: 100.00% ***\n", "Epoch 1/50: Train Loss: 0.0205, Train Acc: 99.66%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 3.4s\n", "--------------------------------------------------------------------------------\n", "  Epoch 2/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 2/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 2/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 1.0s\n", "--------------------------------------------------------------------------------\n", "  Epoch 3/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 3/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 3/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.9s\n", "--------------------------------------------------------------------------------\n", "  Epoch 4/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 4/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 4/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.6s\n", "--------------------------------------------------------------------------------\n", "  Epoch 5/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 5/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 5/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.6s\n", "--------------------------------------------------------------------------------\n", "  Epoch 6/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 6/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 6/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.5s\n", "--------------------------------------------------------------------------------\n", "  Epoch 7/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 30/74, Loss: 0.0001\n", "  Epoch 7/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 7/50, <PERSON><PERSON> 60/74, Loss: 0.0001\n", "  Epoch 7/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 7/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.5s\n", "--------------------------------------------------------------------------------\n", "  Epoch 8/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 8/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 8/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.6s\n", "--------------------------------------------------------------------------------\n", "  Epoch 9/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 9/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 9/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.5s\n", "--------------------------------------------------------------------------------\n", "  Epoch 10/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 10/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 10/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 11/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 11/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 11/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 12/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 12/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 12/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 13/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 13/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 13/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 14/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 14/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 14/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 15/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 30/74, Loss: 0.0001\n", "  Epoch 15/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 15/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 15/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 16/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 16/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 16/50, Bat<PERSON> 50/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 16/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 16/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 17/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 17/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 17/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 18/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 18/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 18/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 19/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 19/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 19/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 20/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 20/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 20/50, Bat<PERSON> 50/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 20/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 20/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 21/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 21/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 21/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 22/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 22/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 22/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 23/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 23/50, <PERSON><PERSON> 50/74, Loss: 0.0001\n", "  Epoch 23/50, <PERSON><PERSON> 60/74, Loss: 0.0003\n", "  Epoch 23/50, <PERSON><PERSON> 70/74, Loss: 0.0001\n", "Epoch 23/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.2s\n", "--------------------------------------------------------------------------------\n", "  Epoch 24/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 24/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 24/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 25/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 25/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 25/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 26/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 26/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 26/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 27/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 27/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 27/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 28/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 30/74, Loss: 0.0001\n", "  Epoch 28/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 28/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 28/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 29/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 29/50, <PERSON><PERSON> 70/74, Loss: 0.0002\n", "Epoch 29/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 30/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 30/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 30/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 31/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 31/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 31/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 32/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 32/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 32/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 33/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 33/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 33/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 34/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 34/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 34/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 35/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 50/74, Loss: 0.0001\n", "  Epoch 35/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 35/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 35/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 36/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 40/74, Loss: 0.0001\n", "  Epoch 36/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 36/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 36/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 37/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 37/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 37/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 38/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 38/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 38/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 39/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 39/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 39/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.4s\n", "--------------------------------------------------------------------------------\n", "  Epoch 40/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 40/50, Bat<PERSON> 10/74, Loss: 0.0000\n", "  Epoch 40/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 40/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 40/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 41/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 50/74, Loss: 0.0001\n", "  Epoch 41/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 41/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 41/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 42/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 10/74, Loss: 0.0002\n", "  Epoch 42/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 42/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 42/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 43/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 43/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 43/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.4s\n", "--------------------------------------------------------------------------------\n", "  Epoch 44/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 44/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 44/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.4s\n", "--------------------------------------------------------------------------------\n", "  Epoch 45/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 10/74, Loss: 0.0002\n", "  Epoch 45/50, <PERSON><PERSON> 20/74, Loss: 0.0001\n", "  Epoch 45/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 45/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 45/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 46/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 46/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 46/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 47/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 47/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 47/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 48/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 48/50, <PERSON><PERSON> 70/74, Loss: 0.0001\n", "Epoch 48/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 49/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 10/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 20/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 49/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 49/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "  Epoch 50/50, <PERSON><PERSON> 0/74, Loss: 0.0000\n", "  Epoch 50/50, Bat<PERSON> 10/74, Loss: 0.0000\n", "  Epoch 50/50, Bat<PERSON> 20/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 30/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 40/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 50/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 60/74, Loss: 0.0000\n", "  Epoch 50/50, <PERSON><PERSON> 70/74, Loss: 0.0000\n", "Epoch 50/50: Train Loss: 0.0000, Train Acc: 100.00%, Val Loss: 0.0000, Val Acc: 100.00%, Time: 0.3s\n", "--------------------------------------------------------------------------------\n", "\n", "Training completed in 0.3 minutes\n", "Best validation accuracy: 100.00%\n"]}], "source": ["# Training loop\n", "print(\"\\n=== STARTING FAIR COMPARISON TRAINING ===\")\n", "print(\"SimplePointNet vs Classical ML using IDENTICAL data sources\")\n", "start_time = time.time()\n", "\n", "for epoch in range(num_epochs):\n", "    epoch_start = time.time()\n", "\n", "    # Training phase\n", "    model.train()\n", "    train_loss = 0.0\n", "    train_correct = 0\n", "    train_total = 0\n", "\n", "    for batch_idx, (data, target) in enumerate(train_loader):\n", "        data, target = data.to(device), target.to(device)\n", "\n", "        optimizer.zero_grad()\n", "        output = model(data)\n", "        loss = criterion(output, target)\n", "        loss.backward()\n", "        optimizer.step()\n", "\n", "        train_loss += loss.item()\n", "        pred = output.argmax(dim=1)\n", "        train_correct += pred.eq(target).sum().item()\n", "        train_total += target.size(0)\n", "\n", "        if batch_idx % 10 == 0:\n", "            print(f'  Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, Loss: {loss.item():.4f}')\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    val_correct = 0\n", "    val_total = 0\n", "\n", "    with torch.no_grad():\n", "        for data, target in val_loader:\n", "            data, target = data.to(device), target.to(device)\n", "            output = model(data)\n", "            loss = criterion(output, target)\n", "\n", "            val_loss += loss.item()\n", "            pred = output.argmax(dim=1)\n", "            val_correct += pred.eq(target).sum().item()\n", "            val_total += target.size(0)\n", "\n", "    # Calculate metrics\n", "    train_loss /= len(train_loader)\n", "    val_loss /= len(val_loader)\n", "    train_acc = 100. * train_correct / train_total\n", "    val_acc = 100. * val_correct / val_total\n", "\n", "    # Store metrics\n", "    train_losses.append(train_loss)\n", "    val_losses.append(val_loss)\n", "    train_accs.append(train_acc)\n", "    val_accs.append(val_acc)\n", "\n", "    # Update learning rate\n", "    scheduler.step()\n", "\n", "    # Save best model\n", "    if val_acc > best_val_acc:\n", "        best_val_acc = val_acc\n", "        torch.save(model.state_dict(), f'{models_path}/simplepointnet_fair_comparison_best_model.pth')\n", "        print(f'  *** New best model saved! Validation accuracy: {val_acc:.2f}% ***')\n", "\n", "    epoch_time = time.time() - epoch_start\n", "    print(f'Epoch {epoch+1}/{num_epochs}: Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.2f}%, '\n", "          f'Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.2f}%, Time: {epoch_time:.1f}s')\n", "    print('-' * 80)\n", "\n", "total_time = time.time() - start_time\n", "print(f\"\\nTraining completed in {total_time/60:.1f} minutes\")\n", "print(f\"Best validation accuracy: {best_val_acc:.2f}%\")"]}, {"cell_type": "markdown", "metadata": {"id": "evaluation"}, "source": ["## Fair Cross-Site Evaluation (RES→RCPS)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "test_evaluation", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "dbd08930-8a67-40db-9a2a-5b282b0c9eb3"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["=== FAIR CROSS-SITE EVALUATION (RES→RCPS) ===\n", "Testing SimplePointNet trained on RES data on RCPS data...\n", "SAME validation protocol as Classical ML\n", "\n", "SimplePointNet Cross-Site Test Results (RES→RCPS):\n", "  Test Accuracy: 100.00%\n", "  Test F1-Score: 1.0000\n", "  Total test samples: 1359\n", "  Correct predictions: 1359\n", "\n", "Predicted classes: [1]\n", "True classes: [1]\n", "\n", "Note: Model predicted only one class\n", "All predictions were: <PERSON><PERSON>\n", "Accuracy: 100.00%\n", "\n", "Actual distribution:\n", "  Piles: 1359\n", "  Non-Piles: 0\n", "\n", "Confusion Matrix:\n", "[[1359]]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Results saved:\n", "  Training history: simplepointnet_fair_comparison_training_history_20250814_114546.json\n", "  Predictions: simplepointnet_fair_comparison_rcps_predictions_20250814_114546.csv\n", "  Model: simplepointnet_fair_comparison_best_model.pth\n", "\n", "=== FAIR COMPARISON COMPLETE ===\n", "SimplePointNet (Raw Coordinates): 100.00% accuracy\n", "Classical ML (22 Features): TBD% accuracy (from their results)\n", "\n", "🎯 Research Question Answered:\n", "   Can deep learning match classical ML using raw coordinates vs engineered features?\n", "   Answer: SimplePointNet achieved 100.00% vs Classical ML's performance\n", "\n", "✅ FAIR COMPARISON ACHIEVED:\n", "   ✅ Same point clouds (LAS files)\n", "   ✅ Same ground truth (Buffer_2m.kml)\n", "   ✅ Same validation protocol (RES→RCPS)\n", "   ✅ Same patch extraction (3m radius)\n", "   📊 Different approaches: Raw coordinates vs Statistical features\n"]}], "source": ["# Load best model\n", "model.load_state_dict(torch.load(f'{models_path}/simplepointnet_fair_comparison_best_model.pth'))\n", "model.eval()\n", "\n", "print(\"=== FAIR CROSS-SITE EVALUATION (RES→RCPS) ===\")\n", "print(\"Testing SimplePointNet trained on RES data on RCPS data...\")\n", "print(\"SAME validation protocol as Classical ML\")\n", "\n", "# Test evaluation\n", "test_correct = 0\n", "test_total = 0\n", "all_predictions = []\n", "all_targets = []\n", "all_probabilities = []\n", "\n", "with torch.no_grad():\n", "    for data, target in test_loader:\n", "        data, target = data.to(device), target.to(device)\n", "        output = model(data)\n", "\n", "        # Get predictions and probabilities\n", "        probabilities = torch.softmax(output, dim=1)\n", "        pred = output.argmax(dim=1)\n", "\n", "        test_correct += pred.eq(target).sum().item()\n", "        test_total += target.size(0)\n", "\n", "        all_predictions.extend(pred.cpu().numpy())\n", "        all_targets.extend(target.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "\n", "# Calculate metrics\n", "test_acc = 100. * test_correct / test_total\n", "test_f1 = f1_score(all_targets, all_predictions)\n", "\n", "print(f\"\\nSimplePointNet Cross-Site Test Results (RES→RCPS):\")\n", "print(f\"  Test Accuracy: {test_acc:.2f}%\")\n", "print(f\"  Test F1-Score: {test_f1:.4f}\")\n", "print(f\"  Total test samples: {test_total}\")\n", "print(f\"  Correct predictions: {test_correct}\")\n", "\n", "# Check class distribution in predictions\n", "unique_predictions = np.unique(all_predictions)\n", "unique_targets = np.unique(all_targets)\n", "\n", "print(f\"\\nPredicted classes: {unique_predictions}\")\n", "print(f\"True classes: {unique_targets}\")\n", "\n", "# Detailed classification report with proper handling\n", "if len(unique_predictions) == 1:\n", "    print(\"\\nNote: Model predicted only one class\")\n", "    print(f\"All predictions were: {'Pile' if unique_predictions[0] == 1 else 'Non-Pile'}\")\n", "    print(f\"Accuracy: {test_acc:.2f}%\")\n", "\n", "    # Show distribution\n", "    print(f\"\\nActual distribution:\")\n", "    print(f\"  Piles: {np.sum(all_targets)}\")\n", "    print(f\"  Non-Piles: {len(all_targets) - np.sum(all_targets)}\")\n", "else:\n", "    print(\"\\nDetailed Classification Report:\")\n", "    print(classification_report(all_targets, all_predictions, target_names=['Non-<PERSON>le', '<PERSON>le']))\n", "\n", "# Confusion matrix\n", "cm = confusion_matrix(all_targets, all_predictions)\n", "print(\"\\nConfusion Matrix:\")\n", "print(cm)\n", "\n", "# Plot confusion matrix\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',\n", "            xticklabels=['Non-<PERSON><PERSON>', '<PERSON>le'],\n", "            yticklabels=['Non-<PERSON>le', '<PERSON>le'])\n", "plt.title('SimplePointNet Fair Comparison Confusion Matrix (RES→RCPS)')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Save results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Save training history\n", "training_history = {\n", "    'train_losses': train_losses,\n", "    'val_losses': val_losses,\n", "    'train_accs': train_accs,\n", "    'val_accs': val_accs,\n", "    'best_val_acc': best_val_acc,\n", "    'test_acc': test_acc,\n", "    'test_f1': test_f1,\n", "    'num_epochs': num_epochs,\n", "    'batch_size': batch_size,\n", "    'data_source': 'Buffer_2m.kml_same_as_classical_ml',\n", "    'validation_protocol': 'RES_to_RCPS_same_as_classical_ml',\n", "    'patch_radius': '3.0m_same_as_classical_ml'\n", "}\n", "\n", "with open(f'{models_path}/simplepointnet_fair_comparison_training_history_{timestamp}.json', 'w') as f:\n", "    json.dump(training_history, f, indent=2)\n", "\n", "# Save predictions\n", "results_df = pd.DataFrame({\n", "    'true_label': all_targets,\n", "    'predicted_label': all_predictions,\n", "    'pile_probability': [prob[1] for prob in all_probabilities],\n", "    'non_pile_probability': [prob[0] for prob in all_probabilities]\n", "})\n", "\n", "results_df.to_csv(f'{models_path}/simplepointnet_fair_comparison_rcps_predictions_{timestamp}.csv', index=False)\n", "\n", "print(f\"\\nResults saved:\")\n", "print(f\"  Training history: simplepointnet_fair_comparison_training_history_{timestamp}.json\")\n", "print(f\"  Predictions: simplepointnet_fair_comparison_rcps_predictions_{timestamp}.csv\")\n", "print(f\"  Model: simplepointnet_fair_comparison_best_model.pth\")\n", "\n", "print(\"\\n=== FAIR COMPARISON COMPLETE ===\")\n", "print(f\"SimplePointNet (Raw Coordinates): {test_acc:.2f}% accuracy\")\n", "print(f\"Classical ML (22 Features): TBD% accuracy (from their results)\")\n", "print(f\"\\n🎯 Research Question Answered:\")\n", "print(f\"   Can deep learning match classical ML using raw coordinates vs engineered features?\")\n", "print(f\"   Answer: SimplePointNet achieved {test_acc:.2f}% vs Classical ML's performance\")\n", "print(f\"\\n✅ FAIR COMPARISON ACHIEVED:\")\n", "print(f\"   ✅ Same point clouds (LAS files)\")\n", "print(f\"   ✅ Same ground truth (Buffer_2m.kml)\")\n", "print(f\"   ✅ Same validation protocol (RES→RCPS)\")\n", "print(f\"   ✅ Same patch extraction (3m radius)\")\n", "print(f\"   📊 Different approaches: Raw coordinates vs Statistical features\")"]}, {"cell_type": "markdown", "metadata": {"id": "csv_export_simple_pointnet"}, "source": ["## Enhanced CSV Export for Simple PointNet Validation"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "export_simple_pointnet_validation_csv"}, "outputs": [], "source": ["# Enhanced CSV export with pile coordinates for Simple PointNet validation\n", "print(\"\\n=== CREATING SIMPLE POINTNET VALIDATION CSV EXPORT ===\")\n", "\n", "# Create validation dataset with pile coordinates\n", "simple_pointnet_validation_data = []\n", "\n", "# Add RES training pile locations with predictions\n", "for i, (pile_x, pile_y) in enumerate(res_pile_locations):\n", "    simple_pointnet_validation_data.append({\n", "        'pile_id': f'RES_SIMPLE_POINTNET_TRAIN_{i+1}',\n", "        'site_name': 'nortan_res',\n", "        'utm_x': pile_x,\n", "        'utm_y': pile_y,\n", "        'data_split': 'training',\n", "        'ground_truth': 'PILE',\n", "        'source': '<PERSON><PERSON>er_KML',\n", "        'patch_radius': 3.0,\n", "        'points_per_patch': 64,\n", "        'model_architecture': 'SimplePointNet',\n", "        'training_timestamp': timestamp\n", "    })\n", "\n", "# Add RCPS test pile locations with predictions\n", "for i, (pile_x, pile_y) in enumerate(rcps_pile_locations):\n", "    simple_pointnet_validation_data.append({\n", "        'pile_id': f'RCPS_SIMPLE_POINTNET_TEST_{i+1}',\n", "        'site_name': 'althea_rcps',\n", "        'utm_x': pile_x,\n", "        'utm_y': pile_y,\n", "        'data_split': 'testing',\n", "        'ground_truth': 'PILE',\n", "        'source': '<PERSON><PERSON>er_KML',\n", "        'patch_radius': 3.0,\n", "        'points_per_patch': 64,\n", "        'model_architecture': 'SimplePointNet',\n", "        'training_timestamp': timestamp\n", "    })\n", "\n", "# Create DataFrame and save\n", "simple_pointnet_validation_df = pd.DataFrame(simple_pointnet_validation_data)\n", "simple_pointnet_validation_csv = f'{models_path}/simple_pointnet_pile_locations_{timestamp}.csv'\n", "simple_pointnet_validation_df.to_csv(simple_pointnet_validation_csv, index=False)\n", "\n", "print(f\"✅ Simple PointNet Validation CSV exported: {simple_pointnet_validation_csv}\")\n", "print(f\"   Total pile locations: {len(simple_pointnet_validation_df)}\")\n", "print(f\"   RES training piles: {len(res_pile_locations)}\")\n", "print(f\"   RCPS test piles: {len(rcps_pile_locations)}\")\n", "print(f\"   Patch parameters: 3.0m radius, 64 points (matches Classical ML)\")\n", "\n", "# Summary of all Simple PointNet outputs\n", "print(f\"\\n=== ALL SIMPLE POINTNET TRAINING OUTPUTS ===\")\n", "print(f\"📊 Training metrics: simplepointnet_fair_comparison_training_history_{timestamp}.json\")\n", "print(f\"🎯 Model weights: simplepointnet_fair_comparison_best_model.pth\")\n", "print(f\"📈 Test predictions: simplepointnet_fair_comparison_rcps_predictions_{timestamp}.csv\")\n", "print(f\"📍 Pile locations: simple_pointnet_pile_locations_{timestamp}.csv\")\n", "print(f\"\\n🎉 Simple PointNet ready for inference testing!\")"]}], "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}