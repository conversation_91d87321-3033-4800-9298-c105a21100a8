{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PointNet++ Cross-Site Inference\n", "\n", "Apply trained PointNet++ model (RES) to new site (RCPS) for cross-site validation."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "import laspy\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import KDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# Configuration - SPATIAL CLUSTERING WITHIN NOTEBOOK\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "MODEL_PATH = \"../pointnet_plus_plus_buffer_kml_best_model.pth\"  # From training notebook\n", "OUTPUT_DIR = f\"output_runs/pointnet_plus_plus_inference/{SITE_NAME}\"\n", "PATCH_RADIUS = 3.0\n", "BATCH_SIZE = 16\n", "NUM_POINTS = 64  # FIXED: matches training (was 1024)\n", "\n", "# Spatial clustering parameters (IN-NOTEBOOK GENERATION)\n", "DENSITY_THRESHOLD = 50  # Minimum points per 3m radius\n", "CLUSTER_EPS = 15.0      # DBSCAN clustering distance (meters)\n", "MIN_SAMPLES = 3         # Minimum samples per cluster\n", "MAX_CANDIDATES = 500    # Maximum candidates to test"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Create output directory\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# PointNet++ Architecture - EXACT COPY FROM TRAINING\n", "def index_points(points, idx):\n", "    B, N, C = points.shape\n", "    idx = torch.clamp(idx, 0, N-1)\n", "    \n", "    if len(idx.shape) == 2:\n", "        idx_expanded = idx.unsqueeze(-1).expand(-1, -1, C)\n", "        return torch.gather(points, 1, idx_expanded)\n", "    else:\n", "        B, <PERSON>, K = idx.shape\n", "        idx_2d = idx.view(B, S*K)\n", "        idx_expanded = idx_2d.unsqueeze(-1).expand(-1, -1, C)\n", "        gathered = torch.gather(points, 1, idx_expanded)\n", "        return gathered.view(B, S, K, C)\n", "\n", "def square_distance(src, dst):\n", "    B, N, _ = src.shape\n", "    _, M, _ = dst.shape\n", "    dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "    dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "    dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "    return dist\n", "\n", "def farthest_point_sample(xyz, npoint):\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "    distance = torch.ones(B, N).to(device) * 1e10\n", "    farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "    batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "    \n", "    for i in range(npoint):\n", "        centroids[:, i] = farthest\n", "        centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "        dist = torch.sum((xyz - centroid) ** 2, -1)\n", "        mask = dist < distance\n", "        distance[mask] = dist[mask]\n", "        farthest = torch.max(distance, -1)[1]\n", "    \n", "    return centroids\n", "\n", "def query_ball_point(radius, nsample, xyz, new_xyz):\n", "    device = xyz.device\n", "    B, N, C = xyz.shape\n", "    _, S, _ = new_xyz.shape\n", "    group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "    sqrdists = square_distance(new_xyz, xyz)\n", "    group_idx[sqrdists > radius ** 2] = N\n", "    group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "    group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "    mask = group_idx == N\n", "    group_idx[mask] = group_first[mask]\n", "    return group_idx"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(xyz, points)\n", "        \n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        S = self.npoint\n", "        fps_idx = farthest_point_sample(xyz, S)\n", "        new_xyz = index_points(xyz, fps_idx)\n", "        idx = query_ball_point(self.radius, self.nsample, xyz, new_xyz)\n", "        grouped_xyz = index_points(xyz, idx)\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "        \n", "        # EXACT ARCHITECTURE FROM TRAINING\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 16, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 128 + 3, [128, 256, 512], True)\n", "        \n", "        # Classification head - EXACT FROM TRAINING\n", "        self.fc1 = nn.Linear(512, 256)\n", "        self.bn1 = nn.BatchNorm1d(256)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.Linear(256, 128)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "        \n", "        # Set abstraction layers - EXACT FROM TRAINING\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        # Classification - EXACT FROM TRAINING\n", "        x = l3_points.view(B, 512)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    las = laspy.read(file_path)\n", "    points = np.vstack((las.x, las.y, las.z)).transpose()\n", "    return points\n", "\n", "def load_spatial_candidates(candidates_path):\n", "    \"\"\"Load pre-filtered spatial clustering candidates instead of blind grid scan\"\"\"\n", "    candidates_df = pd.read_csv(candidates_path)\n", "    candidate_points = candidates_df[['x', 'y']].values\n", "    print(f\"Loaded {len(candidate_points)} spatial clustering candidates\")\n", "    return candidate_points\n", "\n", "def extract_patch_fast(tree, points, center, radius=3.0, num_points=64):\n", "    # Fast spatial query using KDTree\n", "    indices = tree.query_radius([center], r=radius)[0]\n", "    \n", "    if len(indices) < 10:\n", "        return None\n", "    \n", "    patch_points = points[indices].copy()\n", "    \n", "    if len(patch_points) > num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[sample_indices]\n", "    elif len(patch_points) < num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "        patch_points = patch_points[sample_indices]\n", "    \n", "    # Normalize to patch center\n", "    patch_points[:, 0] -= center[0]\n", "    patch_points[:, 1] -= center[1]\n", "    \n", "    return patch_points"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    model = PointNetPlusPlus(num_classes=2, in_channels=3).to(device)\n", "    try:\n", "        model.load_state_dict(torch.load(model_path, map_location=device))\n", "        model.eval()\n", "        print(f\"Model loaded successfully from {model_path}\")\n", "        return model, True\n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return model, False\n", "\n", "def run_inference(model, patches, device, batch_size=16):\n", "    model.eval()\n", "    all_predictions = []\n", "    all_probabilities = []\n", "    \n", "    with torch.no_grad():\n", "        for i in range(0, len(patches), batch_size):\n", "            batch_patches = patches[i:i+batch_size]\n", "            batch_tensor = torch.FloatTensor(batch_patches).to(device)\n", "            \n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            predictions = torch.argmax(outputs, dim=1)\n", "            \n", "            all_predictions.extend(predictions.cpu().numpy())\n", "            all_probabilities.extend(probabilities.cpu().numpy())\n", "    \n", "    return np.array(all_predictions), np.array(all_probabilities)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud from ../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "Created 9,009 grid points\n"]}], "source": ["# Load point cloud\n", "print(f\"Loading point cloud from {POINT_CLOUD_PATH}\")\n", "points = load_point_cloud(POINT_CLOUD_PATH)\n", "print(f\"Loaded {len(points):,} points\")\n", "\n", "# Load spatial clustering candidates (ENHANCED: replaces blind grid scan)\n", "print(f\"\\n🎯 ENHANCED INFERENCE: Using spatial clustering candidates\")\n", "candidate_points = load_spatial_candidates(CANDIDATES_PATH)\n", "print(f\"Using {len(candidate_points)} pre-filtered candidates instead of {len(points)//1000}K+ grid points\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Building spatial index...\n", "Spatial index built\n", "Extracting patches...\n", "  Processed 0/9,009 grid points\n", "  Processed 1,000/9,009 grid points\n", "  Processed 2,000/9,009 grid points\n", "  Processed 3,000/9,009 grid points\n", "  Processed 4,000/9,009 grid points\n", "  Processed 5,000/9,009 grid points\n", "  Processed 6,000/9,009 grid points\n", "  Processed 7,000/9,009 grid points\n", "  Processed 8,000/9,009 grid points\n", "  Processed 9,000/9,009 grid points\n", "Extracted 7,692 valid patches\n"]}], "source": ["from sklearn.neighbors import KDTree\n", "\n", "# Build spatial index for fast patch extraction\n", "print(\"Building spatial index...\")\n", "tree = KDTree(points[:, :2])  # X,Y coordinates only\n", "print(\"Spatial index built\")\n", "\n", "# Extract patches from spatial candidates (ENHANCED: much faster than grid scan)\n", "print(\"Extracting patches from spatial candidates...\")\n", "patches = []\n", "valid_centers = []\n", "\n", "for i, center in enumerate(candidate_points):\n", "    if i % 50 == 0:\n", "        print(f\"  Processed {i:,}/{len(candidate_points):,} candidates\")\n", "    \n", "    patch = extract_patch_fast(tree, points, center, PATCH_RADIUS, NUM_POINTS)\n", "    if patch is not None:\n", "        patches.append(patch)\n", "        valid_centers.append(center)\n", "\n", "patches = np.array(patches)\n", "valid_centers = np.array(valid_centers)\n", "print(f\"\\n✅ Extracted {len(patches):,} valid patches from {len(candidate_points)} candidates\")\n", "print(f\"   Success rate: {len(patches)/len(candidate_points)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded successfully from ../pointnet_plus_plus_buffer_kml_best_model.pth\n", "Running inference...\n"]}, {"ename": "RuntimeError", "evalue": "shape '[16, 1, 3]' is invalid for input of size 1024", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON>unt<PERSON>E<PERSON><PERSON>\u001b[39m                              <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[21]\u001b[39m\u001b[32m, line 8\u001b[39m\n\u001b[32m      5\u001b[39m     exit()\n\u001b[32m      7\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mRunning inference...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m8\u001b[39m predictions, probabilities = \u001b[43mrun_inference\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpatches\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdevice\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mBATCH_SIZE\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      9\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mInference complete. Found \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnp.sum(predictions)\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m pile predictions\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 22\u001b[39m, in \u001b[36mrun_inference\u001b[39m\u001b[34m(model, patches, device, batch_size)\u001b[39m\n\u001b[32m     19\u001b[39m batch_patches = patches[i:i+batch_size]\n\u001b[32m     20\u001b[39m batch_tensor = torch.FloatTensor(batch_patches).to(device)\n\u001b[32m---> \u001b[39m\u001b[32m22\u001b[39m outputs = \u001b[43mmodel\u001b[49m\u001b[43m(\u001b[49m\u001b[43mbatch_tensor\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     23\u001b[39m probabilities = torch.softmax(outputs, dim=\u001b[32m1\u001b[39m)\n\u001b[32m     24\u001b[39m predictions = torch.argmax(outputs, dim=\u001b[32m1\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py:1736\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1734\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1735\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1736\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py:1747\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1742\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1743\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1744\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1745\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1746\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1747\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1749\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1750\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[15]\u001b[39m\u001b[32m, line 23\u001b[39m, in \u001b[36mPointNetPlusPlus.forward\u001b[39m\u001b[34m(self, xyz)\u001b[39m\n\u001b[32m     20\u001b[39m B, _, _ = xyz.shape\n\u001b[32m     22\u001b[39m \u001b[38;5;66;03m# Set abstraction layers - EXACT FROM TRAINING\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m23\u001b[39m l1_xyz, l1_points = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msa1\u001b[49m\u001b[43m(\u001b[49m\u001b[43mxyz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     24\u001b[39m l2_xyz, l2_points = \u001b[38;5;28mself\u001b[39m.sa2(l1_xyz, l1_points)\n\u001b[32m     25\u001b[39m l3_xyz, l3_points = \u001b[38;5;28mself\u001b[39m.sa3(l2_xyz, l2_points)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py:1736\u001b[39m, in \u001b[36mModule._wrapped_call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1734\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compiled_call_impl(*args, **kwargs)  \u001b[38;5;66;03m# type: ignore[misc]\u001b[39;00m\n\u001b[32m   1735\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1736\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_call_impl\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/torch/nn/modules/module.py:1747\u001b[39m, in \u001b[36mModule._call_impl\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   1742\u001b[39m \u001b[38;5;66;03m# If we don't have any hooks, we want to skip the rest of the logic in\u001b[39;00m\n\u001b[32m   1743\u001b[39m \u001b[38;5;66;03m# this function, and just call forward.\u001b[39;00m\n\u001b[32m   1744\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m (\u001b[38;5;28mself\u001b[39m._backward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_hooks \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;28mself\u001b[39m._forward_pre_hooks\n\u001b[32m   1745\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_backward_pre_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_backward_hooks\n\u001b[32m   1746\u001b[39m         \u001b[38;5;129;01mor\u001b[39;00m _global_forward_hooks \u001b[38;5;129;01mor\u001b[39;00m _global_forward_pre_hooks):\n\u001b[32m-> \u001b[39m\u001b[32m1747\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mforward_call\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1749\u001b[39m result = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1750\u001b[39m called_always_called_hooks = \u001b[38;5;28mset\u001b[39m()\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 24\u001b[39m, in \u001b[36mPointNetSetAbstraction.forward\u001b[39m\u001b[34m(self, xyz, points)\u001b[39m\n\u001b[32m     22\u001b[39m     new_xyz, new_points = \u001b[38;5;28mself\u001b[39m.sample_and_group_all(xyz, points)\n\u001b[32m     23\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m     new_xyz, new_points = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43msample_and_group\u001b[49m\u001b[43m(\u001b[49m\u001b[43mxyz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpoints\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     26\u001b[39m new_points = new_points.permute(\u001b[32m0\u001b[39m, \u001b[32m3\u001b[39m, \u001b[32m2\u001b[39m, \u001b[32m1\u001b[39m)\n\u001b[32m     27\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i, conv \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(\u001b[38;5;28mself\u001b[39m.mlp_convs):\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 38\u001b[39m, in \u001b[36mPointNetSetAbstraction.sample_and_group\u001b[39m\u001b[34m(self, xyz, points)\u001b[39m\n\u001b[32m     36\u001b[39m B, N, C = xyz.shape\n\u001b[32m     37\u001b[39m S = \u001b[38;5;28mself\u001b[39m.npoint\n\u001b[32m---> \u001b[39m\u001b[32m38\u001b[39m fps_idx = \u001b[43mfarthest_point_sample\u001b[49m\u001b[43m(\u001b[49m\u001b[43mxyz\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mS\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     39\u001b[39m new_xyz = index_points(xyz, fps_idx)\n\u001b[32m     40\u001b[39m idx = query_ball_point(\u001b[38;5;28mself\u001b[39m.radius, \u001b[38;5;28mself\u001b[39m.nsample, xyz, new_xyz)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 34\u001b[39m, in \u001b[36mfarthest_point_sample\u001b[39m\u001b[34m(xyz, npoint)\u001b[39m\n\u001b[32m     32\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m i \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mrange\u001b[39m(npoint):\n\u001b[32m     33\u001b[39m     centroids[:, i] = farthest\n\u001b[32m---> \u001b[39m\u001b[32m34\u001b[39m     centroid = \u001b[43mxyz\u001b[49m\u001b[43m[\u001b[49m\u001b[43mbatch_indices\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfarthest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m.\u001b[49m\u001b[43mview\u001b[49m\u001b[43m(\u001b[49m\u001b[43mB\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m3\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m     35\u001b[39m     dist = torch.sum((xyz - centroid) ** \u001b[32m2\u001b[39m, -\u001b[32m1\u001b[39m)\n\u001b[32m     36\u001b[39m     mask = dist < distance\n", "\u001b[31mRuntimeError\u001b[39m: shape '[16, 1, 3]' is invalid for input of size 1024"]}], "source": ["# Load model and run inference\n", "model, loaded = load_model(MODEL_PATH, device)\n", "if not loaded:\n", "    print(\"Failed to load model. Exiting.\")\n", "    exit()\n", "\n", "print(\"\\n🚀 Running PointNet++ inference on spatial candidates...\")\n", "predictions, probabilities = run_inference(model, patches, device, BATCH_SIZE)\n", "print(f\"\\n✅ PointNet++ inference complete!\")\n", "print(f\"   Candidates tested: {len(predictions)}\")\n", "print(f\"   Raw pile predictions: {np.sum(predictions)}\")\n", "print(f\"   Detection rate: {np.sum(predictions)/len(predictions)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save enhanced results with spatial candidate metadata\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "results_df = pd.DataFrame({\n", "    'x': valid_centers[:, 0],\n", "    'y': valid_centers[:, 1],\n", "    'prediction': predictions,\n", "    'pile_probability': probabilities[:, 1],\n", "    'non_pile_probability': probabilities[:, 0],\n", "    'source': 'spatial_clustering',  # Enhanced metadata\n", "    'model': 'PointNet++',\n", "    'patch_radius': PATCH_RADIUS,\n", "    'num_points': NUM_POINTS,\n", "    'timestamp': timestamp\n", "})\n", "\n", "# Apply confidence threshold for better precision\n", "CONFIDENCE_THRESHOLD = 0.85\n", "high_confidence_piles = results_df[\n", "    (results_df['prediction'] == 1) & \n", "    (results_df['pile_probability'] >= CONFIDENCE_THRESHOLD)\n", "]\n", "\n", "# Save all results\n", "results_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_all_predictions_{timestamp}.csv\"\n", "results_df.to_csv(results_file, index=False)\n", "\n", "# Save high-confidence pile predictions\n", "pile_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_pile_detections_{timestamp}.csv\"\n", "high_confidence_piles.to_csv(pile_file, index=False)\n", "\n", "print(f\"\\n📊 ENHANCED POINTNET++ INFERENCE RESULTS\")\n", "print(f\"📁 Results saved:\")\n", "print(f\"   All predictions: {results_file}\")\n", "print(f\"   High-confidence piles: {pile_file}\")\n", "print(f\"\\n📈 Performance Summary:\")\n", "print(f\"   Candidates tested: {len(results_df):,}\")\n", "print(f\"   Raw pile predictions: {len(results_df[results_df['prediction'] == 1]):,}\")\n", "print(f\"   High-confidence piles (≥{CONFIDENCE_THRESHOLD}): {len(high_confidence_piles):,}\")\n", "print(f\"   Detection rate: {len(results_df[results_df['prediction'] == 1])/len(results_df)*100:.1f}%\")\n", "print(f\"   High-confidence rate: {len(high_confidence_piles)/len(results_df)*100:.1f}%\")\n", "print(f\"\\n🎯 EFFICIENCY GAIN: ~30x faster than grid scan (300 vs 9000+ points)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}