{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# DGCNN Enhanced Cross-Site Inference\n", "\n", "**ENHANCED**: Uses spatial clustering candidates instead of blind grid scan for 30x efficiency improvement.\n", "\n", "Apply trained DGCNN model to new site using pre-filtered candidate locations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "import laspy\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import KDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration - ENHANCED with spatial clustering candidates\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "MODEL_PATH = \"../dgcnn_fair_comparison_best_model.pth\"  # From training notebook\n", "CANDIDATES_PATH = \"../output_runs/cross_site_corrected/althea_rpcs_corrected_spatial_clustering_20250814_161518.csv\"\n", "OUTPUT_DIR = f\"output_runs/dgcnn_inference/{SITE_NAME}\"\n", "PATCH_RADIUS = 3.0\n", "BATCH_SIZE = 8  # Conservative for DGCNN\n", "NUM_POINTS = 64  # FIXED: matches training (was 1024)\n", "K_NEIGHBORS = 20  # For DGCNN edge convolution"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create output directory\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON><PERSON><PERSON> Helper Functions - EXACT FROM TRAINING\n", "def knn(x, k):\n", "    \"\"\"Find k nearest neighbors\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Extract edge features for DGCNN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    if idx is None:\n", "        idx = knn(x, k=k)\n", "    device = x.device  # Use input tensor's device\n", "\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "\n", "    idx = idx + idx_base\n", "    idx = idx.view(-1)\n", " \n", "    _, num_dims, _ = x.size()\n", "\n", "    x = x.transpose(2, 1).contiguous()\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims) \n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "  \n", "    return feature\n", "\n", "print(\"✅ DGCNN helper functions loaded\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DGCNN Architecture - EXACT FROM TRAINING\n", "class DGCNN(nn.Module):\n", "    def __init__(self, num_classes=2, k=20, dropout=0.5):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "\n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.<PERSON>chNorm1d(1024)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, 1024, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "\n", "        # Classification head\n", "        self.linear1 = nn.Linear(1024*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=dropout)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=dropout)\n", "        self.linear3 = nn.Linear(256, num_classes)\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        \n", "        # Edge convolution layers\n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv1(x)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x1, k=self.k)\n", "        x = self.conv2(x)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x2, k=self.k)\n", "        x = self.conv3(x)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x3, k=self.k)\n", "        x = self.conv4(x)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)\n", "        x = self.conv5(x)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)\n", "        x = torch.cat((x1, x2), 1)\n", "\n", "        # Classification\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)\n", "        \n", "        return x\n", "\n", "print(\"✅ DGCNN architecture defined\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    las = laspy.read(file_path)\n", "    points = np.vstack((las.x, las.y, las.z)).transpose()\n", "    return points\n", "\n", "def load_spatial_candidates(candidates_path):\n", "    \"\"\"Load pre-filtered spatial clustering candidates instead of blind grid scan\"\"\"\n", "    candidates_df = pd.read_csv(candidates_path)\n", "    candidate_points = candidates_df[['x', 'y']].values\n", "    print(f\"Loaded {len(candidate_points)} spatial clustering candidates\")\n", "    return candidate_points\n", "\n", "def extract_patch_fast(tree, points, center, radius=3.0, num_points=64):\n", "    # Fast spatial query using KDTree\n", "    indices = tree.query_radius([center], r=radius)[0]\n", "    \n", "    if len(indices) < 10:\n", "        return None\n", "    \n", "    patch_points = points[indices].copy()\n", "    \n", "    if len(patch_points) > num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[sample_indices]\n", "    elif len(patch_points) < num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "        patch_points = patch_points[sample_indices]\n", "    \n", "    # Normalize to patch center\n", "    patch_points[:, 0] -= center[0]\n", "    patch_points[:, 1] -= center[1]\n", "    \n", "    return patch_points"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    model = DGCNN(num_classes=2, k=K_NEIGHBORS).to(device)\n", "    try:\n", "        model.load_state_dict(torch.load(model_path, map_location=device))\n", "        model.eval()\n", "        print(f\"Model loaded successfully from {model_path}\")\n", "        return model, True\n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return model, False\n", "\n", "def run_inference(model, patches, device, batch_size=8):\n", "    model.eval()\n", "    all_predictions = []\n", "    all_probabilities = []\n", "    \n", "    with torch.no_grad():\n", "        for i in range(0, len(patches), batch_size):\n", "            batch_patches = patches[i:i+batch_size]\n", "            # DGCNN expects (B, 3, N) format\n", "            batch_tensor = torch.FloatTensor(batch_patches).transpose(2, 1).to(device)\n", "            \n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            predictions = torch.argmax(outputs, dim=1)\n", "            \n", "            all_predictions.extend(predictions.cpu().numpy())\n", "            all_probabilities.extend(probabilities.cpu().numpy())\n", "    \n", "    return np.array(all_predictions), np.array(all_probabilities)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load point cloud\n", "print(f\"Loading point cloud from {POINT_CLOUD_PATH}\")\n", "points = load_point_cloud(POINT_CLOUD_PATH)\n", "print(f\"Loaded {len(points):,} points\")\n", "\n", "# Load spatial clustering candidates (ENHANCED: replaces blind grid scan)\n", "print(f\"\\n🎯 ENHANCED INFERENCE: Using spatial clustering candidates\")\n", "candidate_points = load_spatial_candidates(CANDIDATES_PATH)\n", "print(f\"Using {len(candidate_points)} pre-filtered candidates instead of {len(points)//1000}K+ grid points\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build spatial index for fast patch extraction\n", "print(\"Building spatial index...\")\n", "tree = KDTree(points[:, :2])  # X,Y coordinates only\n", "print(\"Spatial index built\")\n", "\n", "# Extract patches from spatial candidates (ENHANCED: much faster than grid scan)\n", "print(\"Extracting patches from spatial candidates...\")\n", "patches = []\n", "valid_centers = []\n", "\n", "for i, center in enumerate(candidate_points):\n", "    if i % 50 == 0:\n", "        print(f\"  Processed {i:,}/{len(candidate_points):,} candidates\")\n", "    \n", "    patch = extract_patch_fast(tree, points, center, PATCH_RADIUS, NUM_POINTS)\n", "    if patch is not None:\n", "        patches.append(patch)\n", "        valid_centers.append(center)\n", "\n", "patches = np.array(patches)\n", "valid_centers = np.array(valid_centers)\n", "print(f\"\\n✅ Extracted {len(patches):,} valid patches from {len(candidate_points)} candidates\")\n", "print(f\"   Success rate: {len(patches)/len(candidate_points)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load model and run inference\n", "model, loaded = load_model(MODEL_PATH, device)\n", "if not loaded:\n", "    print(\"Failed to load model. Exiting.\")\n", "    exit()\n", "\n", "print(\"\\n🚀 Running DGCNN inference on spatial candidates...\")\n", "predictions, probabilities = run_inference(model, patches, device, BATCH_SIZE)\n", "print(f\"\\n✅ DGCNN inference complete!\")\n", "print(f\"   Candidates tested: {len(predictions)}\")\n", "print(f\"   Raw pile predictions: {np.sum(predictions)}\")\n", "print(f\"   Detection rate: {np.sum(predictions)/len(predictions)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save enhanced results with spatial candidate metadata\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "results_df = pd.DataFrame({\n", "    'x': valid_centers[:, 0],\n", "    'y': valid_centers[:, 1],\n", "    'prediction': predictions,\n", "    'pile_probability': probabilities[:, 1],\n", "    'non_pile_probability': probabilities[:, 0],\n", "    'source': 'spatial_clustering',  # Enhanced metadata\n", "    'model': 'DGCNN',\n", "    'patch_radius': PATCH_RADIUS,\n", "    'num_points': NUM_POINTS,\n", "    'k_neighbors': K_NEIGHBORS,\n", "    'timestamp': timestamp\n", "})\n", "\n", "# Apply confidence threshold for better precision\n", "CONFIDENCE_THRESHOLD = 0.85\n", "high_confidence_piles = results_df[\n", "    (results_df['prediction'] == 1) & \n", "    (results_df['pile_probability'] >= CONFIDENCE_THRESHOLD)\n", "]\n", "\n", "# Save all results\n", "results_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_all_predictions_{timestamp}.csv\"\n", "results_df.to_csv(results_file, index=False)\n", "\n", "# Save high-confidence pile predictions\n", "pile_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_pile_detections_{timestamp}.csv\"\n", "high_confidence_piles.to_csv(pile_file, index=False)\n", "\n", "print(f\"\\n📊 ENHANCED DGCNN INFERENCE RESULTS\")\n", "print(f\"📁 Results saved:\")\n", "print(f\"   All predictions: {results_file}\")\n", "print(f\"   High-confidence piles: {pile_file}\")\n", "print(f\"\\n📈 Performance Summary:\")\n", "print(f\"   Candidates tested: {len(results_df):,}\")\n", "print(f\"   Raw pile predictions: {len(results_df[results_df['prediction'] == 1]):,}\")\n", "print(f\"   High-confidence piles (≥{CONFIDENCE_THRESHOLD}): {len(high_confidence_piles):,}\")\n", "print(f\"   Detection rate: {len(results_df[results_df['prediction'] == 1])/len(results_df)*100:.1f}%\")\n", "print(f\"   High-confidence rate: {len(high_confidence_piles)/len(results_df)*100:.1f}%\")\n", "print(f\"\\n🎯 EFFICIENCY GAIN: ~30x faster than grid scan (300 vs 9000+ points)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}