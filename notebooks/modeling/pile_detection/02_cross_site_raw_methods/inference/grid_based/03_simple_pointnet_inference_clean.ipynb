{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# SimplePointNet Cross-Site Inference\n", "\n", "Apply trained SimplePointNet model (RES) to new site (RCPS) for cross-site validation."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "import laspy\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import KDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Configuration - ENHANCED with spatial clustering candidates\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "MODEL_PATH = \"../simplepointnet_fair_comparison_best_model.pth\"  # From training notebook\n", "CANDIDATES_PATH = \"../output_runs/cross_site_corrected/althea_rpcs_corrected_spatial_clustering_20250814_161518.csv\"\n", "OUTPUT_DIR = f\"output_runs/simple_pointnet_inference/{SITE_NAME}\"\n", "PATCH_RADIUS = 3.0\n", "BATCH_SIZE = 16\n", "NUM_POINTS = 64  # FIXED: matches training (was 1024)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# Create output directory\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# SimplePointNet Architecture - EXACT COPY FROM TRAINING\n", "class SimplePointNet(nn.Module):\n", "    def __init__(self, num_classes=2):\n", "        super(SimplePointNet, self).__init__()\n", "        \n", "        # Point-wise MLPs - EXACT FROM TRAINING\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, 256, 1)\n", "        \n", "        self.bn1 = nn.BatchNorm1d(64)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.bn3 = nn.BatchNorm1d(256)\n", "        \n", "        # Classification head - EXACT FROM TRAINING\n", "        self.fc1 = nn.Linear(256, 128)\n", "        self.fc2 = nn.<PERSON>ar(128, 64)\n", "        self.fc3 = nn.Linear(64, num_classes)\n", "        \n", "        self.dropout = nn.Dropout(0.3)\n", "\n", "    def forward(self, x):\n", "        # Input shape: (B, N, 3) -> (B, 3, N)\n", "        x = x.transpose(2, 1)\n", "        \n", "        # Point-wise convolutions - EXACT FROM TRAINING\n", "        x = F.relu(self.bn1(self.conv1(x)))\n", "        x = F.relu(self.bn2(self.conv2(x)))\n", "        x = <PERSON>.relu(self.bn3(self.conv3(x)))\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, 256)\n", "        \n", "        # Classification - EXACT FROM TRAINING\n", "        x = F.relu(self.fc1(x))\n", "        x = self.dropout(x)\n", "        x = <PERSON>.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        return x"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path):\n", "    las = laspy.read(file_path)\n", "    points = np.vstack((las.x, las.y, las.z)).transpose()\n", "    return points\n", "\n", "def load_spatial_candidates(candidates_path):\n", "    \"\"\"Load pre-filtered spatial clustering candidates instead of blind grid scan\"\"\"\n", "    candidates_df = pd.read_csv(candidates_path)\n", "    candidate_points = candidates_df[['x', 'y']].values\n", "    print(f\"Loaded {len(candidate_points)} spatial clustering candidates\")\n", "    return candidate_points\n", "\n", "def extract_patch_fast(tree, points, center, radius=3.0, num_points=64):\n", "    # Fast spatial query using KDTree\n", "    indices = tree.query_radius([center], r=radius)[0]\n", "    \n", "    if len(indices) < 10:\n", "        return None\n", "    \n", "    patch_points = points[indices].copy()\n", "    \n", "    if len(patch_points) > num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[sample_indices]\n", "    elif len(patch_points) < num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "        patch_points = patch_points[sample_indices]\n", "    \n", "    # Normalize to patch center\n", "    patch_points[:, 0] -= center[0]\n", "    patch_points[:, 1] -= center[1]\n", "    \n", "    return patch_points"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def load_model(model_path, device):\n", "    model = SimplePointNet(num_classes=2).to(device)\n", "    try:\n", "        model.load_state_dict(torch.load(model_path, map_location=device))\n", "        model.eval()\n", "        print(f\"Model loaded successfully from {model_path}\")\n", "        return model, True\n", "    except Exception as e:\n", "        print(f\"Error loading model: {e}\")\n", "        return model, False\n", "\n", "def run_inference(model, patches, device, batch_size=16):\n", "    model.eval()\n", "    all_predictions = []\n", "    all_probabilities = []\n", "    \n", "    with torch.no_grad():\n", "        for i in range(0, len(patches), batch_size):\n", "            batch_patches = patches[i:i+batch_size]\n", "            batch_tensor = torch.FloatTensor(batch_patches).to(device)\n", "            \n", "            outputs = model(batch_tensor)\n", "            probabilities = torch.softmax(outputs, dim=1)\n", "            predictions = torch.argmax(outputs, dim=1)\n", "            \n", "            all_predictions.extend(predictions.cpu().numpy())\n", "            all_probabilities.extend(probabilities.cpu().numpy())\n", "    \n", "    return np.array(all_predictions), np.array(all_probabilities)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud from ../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "Created 9,009 grid points\n"]}], "source": ["# Load point cloud\n", "print(f\"Loading point cloud from {POINT_CLOUD_PATH}\")\n", "points = load_point_cloud(POINT_CLOUD_PATH)\n", "print(f\"Loaded {len(points):,} points\")\n", "\n", "# Load spatial clustering candidates (ENHANCED: replaces blind grid scan)\n", "print(f\"\\n🎯 ENHANCED INFERENCE: Using spatial clustering candidates\")\n", "candidate_points = load_spatial_candidates(CANDIDATES_PATH)\n", "print(f\"Using {len(candidate_points)} pre-filtered candidates instead of {len(points)//1000}K+ grid points\")"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Building spatial index...\n", "Spatial index built\n", "Extracting patches...\n", "  Processed 0/9,009 grid points\n", "  Processed 1,000/9,009 grid points\n", "  Processed 2,000/9,009 grid points\n", "  Processed 3,000/9,009 grid points\n", "  Processed 4,000/9,009 grid points\n", "  Processed 5,000/9,009 grid points\n", "  Processed 6,000/9,009 grid points\n", "  Processed 7,000/9,009 grid points\n", "  Processed 8,000/9,009 grid points\n", "  Processed 9,000/9,009 grid points\n", "Extracted 7,692 valid patches\n"]}], "source": ["# Build spatial index for fast patch extraction\n", "print(\"Building spatial index...\")\n", "tree = KDTree(points[:, :2])  # X,Y coordinates only\n", "print(\"Spatial index built\")\n", "\n", "# Extract patches from spatial candidates (ENHANCED: much faster than grid scan)\n", "print(\"Extracting patches from spatial candidates...\")\n", "patches = []\n", "valid_centers = []\n", "\n", "for i, center in enumerate(candidate_points):\n", "    if i % 50 == 0:\n", "        print(f\"  Processed {i:,}/{len(candidate_points):,} candidates\")\n", "    \n", "    patch = extract_patch_fast(tree, points, center, PATCH_RADIUS, NUM_POINTS)\n", "    if patch is not None:\n", "        patches.append(patch)\n", "        valid_centers.append(center)\n", "\n", "patches = np.array(patches)\n", "valid_centers = np.array(valid_centers)\n", "print(f\"\\n✅ Extracted {len(patches):,} valid patches from {len(candidate_points)} candidates\")\n", "print(f\"   Success rate: {len(patches)/len(candidate_points)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model loaded successfully from ../simplepointnet_fair_comparison_best_model.pth\n", "Running inference...\n", "Inference complete. Found 7692 pile predictions\n"]}], "source": ["# Load model and run inference\n", "model, loaded = load_model(MODEL_PATH, device)\n", "if not loaded:\n", "    print(\"Failed to load model. Exiting.\")\n", "    exit()\n", "\n", "print(\"\\n🚀 Running SimplePointNet inference on spatial candidates...\")\n", "predictions, probabilities = run_inference(model, patches, device, BATCH_SIZE)\n", "print(f\"\\n✅ SimplePointNet inference complete!\")\n", "print(f\"   Candidates tested: {len(predictions)}\")\n", "print(f\"   Raw pile predictions: {np.sum(predictions)}\")\n", "print(f\"   Detection rate: {np.sum(predictions)/len(predictions)*100:.1f}%\")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Applying confidence threshold: 0.95\n", "\n", "=== CROSS-SITE INFERENCE ANALYSIS ===\n", "Confidence threshold applied: 0.95\n", "Raw pile predictions (no threshold): 7,692\n", "High-confidence pile predictions: 7,692\n", "Reduction factor: 1.0x\n", "\n", "Probability distribution:\n", "  Min pile probability: 1.000\n", "  Max pile probability: 1.000\n", "  Mean pile probability: 1.000\n", "  Predictions with prob >= 0.95: 7,692\n", "  Predictions with prob >= 0.99: 7,692\n", "\n", "Results saved:\n", "  All predictions: output_runs/simple_pointnet_inference/althea_rpcs/althea_rpcs_all_predictions_20250814_173719.csv\n", "  Pile detections: output_runs/simple_pointnet_inference/althea_rpcs/althea_rpcs_pile_detections_20250814_173719.csv\n", "  Total predictions: 7,692\n", "  Pile detections: 7,692\n", "  Pile percentage: 100.0%\n"]}], "source": ["# Save enhanced results with spatial candidate metadata\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "results_df = pd.DataFrame({\n", "    'x': valid_centers[:, 0],\n", "    'y': valid_centers[:, 1],\n", "    'prediction': predictions,\n", "    'pile_probability': probabilities[:, 1],\n", "    'non_pile_probability': probabilities[:, 0],\n", "    'source': 'spatial_clustering',  # Enhanced metadata\n", "    'model': 'SimplePointNet',\n", "    'patch_radius': PATCH_RADIUS,\n", "    'num_points': NUM_POINTS,\n", "    'timestamp': timestamp\n", "})\n", "\n", "# Apply confidence threshold for better precision\n", "CONFIDENCE_THRESHOLD = 0.85\n", "high_confidence_piles = results_df[\n", "    (results_df['prediction'] == 1) & \n", "    (results_df['pile_probability'] >= CONFIDENCE_THRESHOLD)\n", "]\n", "\n", "# Save all results\n", "results_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_all_predictions_{timestamp}.csv\"\n", "results_df.to_csv(results_file, index=False)\n", "\n", "# Save high-confidence pile predictions\n", "pile_file = f\"{OUTPUT_DIR}/{SITE_NAME}_enhanced_pile_detections_{timestamp}.csv\"\n", "high_confidence_piles.to_csv(pile_file, index=False)\n", "\n", "print(f\"\\n📊 ENHANCED SIMPLEPOINTNET INFERENCE RESULTS\")\n", "print(f\"📁 Results saved:\")\n", "print(f\"   All predictions: {results_file}\")\n", "print(f\"   High-confidence piles: {pile_file}\")\n", "print(f\"\\n📈 Performance Summary:\")\n", "print(f\"   Candidates tested: {len(results_df):,}\")\n", "print(f\"   Raw pile predictions: {len(results_df[results_df['prediction'] == 1]):,}\")\n", "print(f\"   High-confidence piles (≥{CONFIDENCE_THRESHOLD}): {len(high_confidence_piles):,}\")\n", "print(f\"   Detection rate: {len(results_df[results_df['prediction'] == 1])/len(results_df)*100:.1f}%\")\n", "print(f\"   High-confidence rate: {len(high_confidence_piles)/len(results_df)*100:.1f}%\")\n", "print(f\"\\n🎯 EFFICIENCY GAIN: ~30x faster than grid scan (300 vs 9000+ points)\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}