{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# PointNet++ Clean Inference\n", "\n", "Clean inference using exact architecture from training notebook.\n", "Uses Buffer KML approach for pile location candidates."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using device: cpu\n"]}], "source": ["import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import geopandas as gpd\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import KDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "print(f\"Using device: {device}\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration set for althea_rpcs\n"]}], "source": ["# Configuration\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "BUFFER_KML_PATH = \"../../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "MODEL_PATH = \"../pointnet_plus_plus_buffer_kml_best_model.pth\"\n", "OUTPUT_DIR = f\"output_runs/buffer_kml_inference/{SITE_NAME}\"\n", "PATCH_RADIUS = 3.0\n", "BATCH_SIZE = 8\n", "NUM_POINTS = 64\n", "CONFIDENCE_THRESHOLD = 0.85\n", "MIN_POINTS_PER_PATCH = 30\n", "\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)\n", "print(f\"Configuration set for {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PointNet++ Set Abstraction defined\n"]}], "source": ["# PointNet++ Set Abstraction - EXACT FROM TRAINING\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(xyz, points)\n", "\n", "        new_points = new_points.permute(0, 3, 2, 1)\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, xyz, points):\n", "        B, N, C = xyz.shape\n", "        S = self.npoint\n", "\n", "        # Use random sampling instead of farthest point sampling\n", "        fps_idx = torch.randint(0, N, (B, S), device=xyz.device, dtype=torch.long)\n", "        new_xyz = self.index_points(xyz, fps_idx)\n", "\n", "        # Simplified ball query - use k-nearest neighbors\n", "        idx = self.knn_query(xyz, new_xyz, self.nsample)\n", "        grouped_xyz = self.index_points(xyz, idx)\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = self.index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points\n", "\n", "    def knn_query(self, xyz, new_xyz, k):\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "\n", "        # Compute pairwise distances\n", "        xyz_expanded = xyz.unsqueeze(2)  # (B, N, 1, C)\n", "        new_xyz_expanded = new_xyz.unsqueeze(1)  # (B, 1, S, C)\n", "\n", "        # Calculate squared distances\n", "        dists = torch.sum((xyz_expanded - new_xyz_expanded) ** 2, dim=-1)  # (B, N, S)\n", "\n", "        # Get k nearest neighbors for each query point\n", "        _, idx = torch.topk(dists, k, dim=1, largest=False)  # (B, k, S)\n", "        idx = idx.permute(0, 2, 1)  # (B, S, k)\n", "\n", "        return idx\n", "\n", "    def index_points(self, points, idx):\n", "        B, N, C = points.shape\n", "        device = points.device\n", "\n", "        # Handle out of bounds indices\n", "        idx = torch.clamp(idx, 0, N-1)\n", "\n", "        # Use torch.gather - most reliable approach\n", "        if len(idx.shape) == 2:  # [B, S]\n", "            idx_expanded = idx.unsqueeze(-1).expand(-1, -1, C)\n", "            return torch.gather(points, 1, idx_expanded)\n", "        else:  # [B, S, K]\n", "            B, <PERSON>, K = idx.shape\n", "            idx_2d = idx.reshape(B, S*K)\n", "            idx_expanded = idx_2d.unsqueeze(-1).expand(-1, -1, C)\n", "            gathered = torch.gather(points, 1, idx_expanded)\n", "            return gathered.reshape(B, S, K, C)\n", "\n", "print(\"PointNet++ Set Abstraction defined\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PointNet++ Model defined\n"]}], "source": ["# PointNet++ Main Model - EXACT FROM TRAINING\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_classes=2, in_channels=3):\n", "        super(PointNetPlusPlus, self).__init__()\n", "\n", "        # Simplified set abstraction layers\n", "        self.sa1 = PointNetSetAbstraction(256, 0.2, 16, in_channels, [32, 32, 64], False)\n", "        self.sa2 = PointNetSetAbstraction(64, 0.4, 16, 64 + 3, [64, 64, 128], False)\n", "        self.sa3 = PointNetSetAbstraction(None, None, None, 128 + 3, [128, 256, 512], True)\n", "\n", "        # Classification head\n", "        self.fc1 = nn.Linear(512, 256)\n", "        self.bn1 = nn.BatchNorm1d(256)\n", "        self.drop1 = nn.Dropout(0.3)\n", "        self.fc2 = nn.Linear(256, 128)\n", "        self.bn2 = nn.BatchNorm1d(128)\n", "        self.drop2 = nn.Dropout(0.3)\n", "        self.fc3 = nn.Linear(128, num_classes)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "\n", "        # Set abstraction layers\n", "        l1_xyz, l1_points = self.sa1(xyz, None)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "\n", "        # Classification\n", "        x = l3_points.view(B, 512)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "\n", "        return x\n", "\n", "print(\"PointNet++ Model defined\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading PointNet++ model...\n", "Model loaded successfully from ../pointnet_plus_plus_buffer_kml_best_model.pth\n", "Model test passed! Output shape: torch.Size([2, 2])\n", "Sample probabilities: [3.532902e-05 9.999647e-01]\n"]}], "source": ["# Load and test model\n", "print(\"Loading PointNet++ model...\")\n", "model = PointNetPlusPlus(num_classes=2, in_channels=3).to(device)\n", "try:\n", "    model.load_state_dict(torch.load(MODEL_PATH, map_location=device))\n", "    model.eval()\n", "    print(f\"Model loaded successfully from {MODEL_PATH}\")\n", "    \n", "    # Test with dummy data\n", "    dummy_input = torch.randn(2, 3, NUM_POINTS).to(device)\n", "    with torch.no_grad():\n", "        output = model(dummy_input)\n", "        probabilities = torch.softmax(output, dim=1)\n", "        print(f\"Model test passed! Output shape: {output.shape}\")\n", "        print(f\"Sample probabilities: {probabilities[0].cpu().numpy()}\")\n", "except Exception as e:\n", "    print(f\"Error loading model: {e}\")\n", "    raise"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data loading functions defined\n"]}], "source": ["# Data loading functions\n", "def load_point_cloud(las_path):\n", "    \"\"\"Load point cloud from LAS file\"\"\"\n", "    print(f\"Loading point cloud: {las_path}\")\n", "    las_file = laspy.read(las_path)\n", "    points = np.vstack([las_file.x, las_file.y, las_file.z]).T\n", "    print(f\"Loaded {len(points):,} points\")\n", "    print(f\"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}], Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}], Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]\")\n", "    return points\n", "\n", "def load_buffer_kml(kml_path):\n", "    \"\"\"Load pile locations from Buffer KML\"\"\"\n", "    print(f\"Loading pile locations from Buffer KML: {kml_path}\")\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    # Create GeoDataFrame and reproject to UTM\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "    \n", "    # Reproject to UTM Zone 15N for RCPS\n", "    gdf_utm = gdf_geo.to_crs('EPSG:32615')\n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    \n", "    print(f\"Loaded {len(pile_locations_utm)} pile locations\")\n", "    print(f\"Bounds: X[{pile_locations_utm[:, 0].min():.1f}, {pile_locations_utm[:, 0].max():.1f}], Y[{pile_locations_utm[:, 1].min():.1f}, {pile_locations_utm[:, 1].max():.1f}]\")\n", "    \n", "    return pile_locations_utm\n", "\n", "print(\"Data loading functions defined\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Patch extraction function defined\n"]}], "source": ["# Patch extraction function\n", "def extract_patches(points, pile_locations, radius=3.0, min_points=30, target_size=64):\n", "    \"\"\"Extract patches around pile locations\"\"\"\n", "    patches = []\n", "    valid_locations = []\n", "    \n", "    # Build KDTree for efficient spatial queries\n", "    tree = KDTree(points[:, :2])  # Use only X, Y for spatial queries\n", "    \n", "    for i, pile_loc in enumerate(pile_locations):\n", "        # Find points within radius\n", "        indices = tree.query_radius([pile_loc], r=radius)[0]\n", "        \n", "        if len(indices) < min_points:\n", "            continue\n", "            \n", "        patch_points = points[indices]\n", "        \n", "        # Center the patch\n", "        centroid = patch_points.mean(axis=0)\n", "        patch_points_centered = patch_points - centroid\n", "        \n", "        # Normalize coordinates\n", "        max_dist = np.max(np.linalg.norm(patch_points_centered[:, :2], axis=1))\n", "        if max_dist > 0:\n", "            patch_points_centered[:, :2] /= max_dist\n", "        \n", "        # Sample or pad to target size\n", "        if len(patch_points_centered) >= target_size:\n", "            # Random sampling\n", "            indices = np.random.choice(len(patch_points_centered), target_size, replace=False)\n", "            patch_points_final = patch_points_centered[indices]\n", "        else:\n", "            # Pad with repeated points\n", "            padding_needed = target_size - len(patch_points_centered)\n", "            padding_indices = np.random.choice(len(patch_points_centered), padding_needed, replace=True)\n", "            padding_points = patch_points_centered[padding_indices]\n", "            patch_points_final = np.vstack([patch_points_centered, padding_points])\n", "        \n", "        patches.append(patch_points_final)\n", "        valid_locations.append(pile_loc)\n", "    \n", "    return np.array(patches), np.array(valid_locations)\n", "\n", "print(\"Patch extraction function defined\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING DATA ===\n", "Loading point cloud: ../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "Bounds: X[599595.2, 599866.2], Y[4334366.6, 4334660.8], Z[238.6, 259.2]\n", "Loading pile locations from Buffer KML: ../../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\n", "Loaded 1359 pile locations\n", "Bounds: X[599597.2, 599864.2], Y[4334368.6, 4334658.8]\n", "\n", "=== EXTRACTING PATCHES ===\n", "Extracted 1359 valid patches from 1359 candidate locations\n", "Patch shape: (1359, 64, 3)\n"]}], "source": ["# Load data\n", "print(\"=== LOADING DATA ===\")\n", "points = load_point_cloud(POINT_CLOUD_PATH)\n", "pile_locations = load_buffer_kml(BUFFER_KML_PATH)\n", "\n", "print(f\"\\n=== EXTRACTING PATCHES ===\")\n", "patches, valid_locations = extract_patches(\n", "    points, pile_locations, \n", "    radius=PATCH_RADIUS, \n", "    min_points=MIN_POINTS_PER_PATCH, \n", "    target_size=NUM_POINTS\n", ")\n", "\n", "print(f\"Extracted {len(patches)} valid patches from {len(pile_locations)} candidate locations\")\n", "print(f\"Patch shape: {patches.shape}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== RUNNING INFERENCE ===\n", "Processed 80/1359 patches\n", "Processed 160/1359 patches\n", "Processed 240/1359 patches\n", "Processed 320/1359 patches\n", "Processed 400/1359 patches\n", "Processed 480/1359 patches\n", "Processed 560/1359 patches\n", "Processed 640/1359 patches\n", "Processed 720/1359 patches\n", "Processed 800/1359 patches\n", "Processed 880/1359 patches\n", "Processed 960/1359 patches\n", "Processed 1040/1359 patches\n", "Processed 1120/1359 patches\n", "Processed 1200/1359 patches\n", "Processed 1280/1359 patches\n", "Processed 1359/1359 patches\n", "Inference completed on 1359 patches\n"]}], "source": ["# Run inference\n", "print(\"\\n=== RUNNING INFERENCE ===\")\n", "all_predictions = []\n", "all_probabilities = []\n", "\n", "model.eval()\n", "with torch.no_grad():\n", "    for i in range(0, len(patches), BATCH_SIZE):\n", "        batch_patches = patches[i:i+BATCH_SIZE]\n", "        \n", "        # Convert to tensor and transpose for PointNet++ (B, 3, N)\n", "        batch_tensor = torch.FloatTensor(batch_patches).transpose(2, 1).to(device)\n", "        \n", "        # Forward pass\n", "        outputs = model(batch_tensor)\n", "        probabilities = torch.softmax(outputs, dim=1)\n", "        predictions = torch.argmax(probabilities, dim=1)\n", "        \n", "        all_predictions.extend(predictions.cpu().numpy())\n", "        all_probabilities.extend(probabilities.cpu().numpy())\n", "        \n", "        if (i // BATCH_SIZE + 1) % 10 == 0:\n", "            print(f\"Processed {i + len(batch_patches)}/{len(patches)} patches\")\n", "\n", "all_predictions = np.array(all_predictions)\n", "all_probabilities = np.array(all_probabilities)\n", "\n", "print(f\"Inference completed on {len(patches)} patches\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== RESULTS ANALYSIS ===\n", "Total candidate locations: 1359\n", "Valid patches extracted: 1359\n", "Pile predictions (any confidence): 1359\n", "High-confidence pile detections (>0.85): 1359\n", "Detection rate: 100.0%\n", "Average confidence: 1.000\n", "Confidence range: [1.000, 1.000]\n"]}], "source": ["# Analyze results\n", "print(\"\\n=== RESULTS ANALYSIS ===\")\n", "\n", "# Filter high-confidence pile predictions\n", "pile_predictions = all_predictions == 1\n", "pile_confidences = all_probabilities[:, 1]\n", "high_confidence_piles = (pile_predictions) & (pile_confidences >= CONFIDENCE_THRESHOLD)\n", "\n", "detected_piles = valid_locations[high_confidence_piles]\n", "detected_confidences = pile_confidences[high_confidence_piles]\n", "\n", "print(f\"Total candidate locations: {len(pile_locations)}\")\n", "print(f\"Valid patches extracted: {len(patches)}\")\n", "print(f\"Pile predictions (any confidence): {pile_predictions.sum()}\")\n", "print(f\"High-confidence pile detections (>{CONFIDENCE_THRESHOLD}): {len(detected_piles)}\")\n", "print(f\"Detection rate: {len(detected_piles)/len(patches)*100:.1f}%\")\n", "\n", "if len(detected_piles) > 0:\n", "    print(f\"Average confidence: {detected_confidences.mean():.3f}\")\n", "    print(f\"Confidence range: [{detected_confidences.min():.3f}, {detected_confidences.max():.3f}]\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "All results saved to: output_runs/buffer_kml_inference/althea_rpcs/pointnet_plus_plus_results_20250814_192430.csv\n", "High-confidence detections saved to: output_runs/buffer_kml_inference/althea_rpcs/high_confidence_detections_20250814_192430.csv\n", "\n", "Sample high-confidence detections:\n", "               x             y  confidence\n", "0  599729.925060  4.334562e+06    0.999960\n", "1  599793.483444  4.334476e+06    0.999960\n", "2  599785.540273  4.334645e+06    0.999961\n", "3  599709.557077  4.334444e+06    0.999961\n", "4  599849.728171  4.334445e+06    0.999960\n", "5  599667.753267  4.334399e+06    0.999960\n", "6  599688.904662  4.334376e+06    0.999961\n", "7  599653.356437  4.334467e+06    0.999961\n", "8  599653.274840  4.334482e+06    0.999960\n", "9  599688.443296  4.334460e+06    0.999961\n", "\n", "=== INFERENCE COMPLETE ===\n", "Results saved to: output_runs/buffer_kml_inference/althea_rpcs\n"]}], "source": ["# Save results\n", "print(\"\\n=== SAVING RESULTS ===\")\n", "\n", "# Create results DataFrame\n", "results_df = pd.DataFrame({\n", "    'x': valid_locations[:, 0],\n", "    'y': valid_locations[:, 1],\n", "    'prediction': all_predictions,\n", "    'pile_confidence': all_probabilities[:, 1],\n", "    'non_pile_confidence': all_probabilities[:, 0],\n", "    'high_confidence_pile': high_confidence_piles\n", "})\n", "\n", "# Save all results\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "results_path = f\"{OUTPUT_DIR}/pointnet_plus_plus_results_{timestamp}.csv\"\n", "results_df.to_csv(results_path, index=False)\n", "print(f\"All results saved to: {results_path}\")\n", "\n", "# Save high-confidence detections\n", "if len(detected_piles) > 0:\n", "    detections_df = pd.DataFrame({\n", "        'x': detected_piles[:, 0],\n", "        'y': detected_piles[:, 1],\n", "        'confidence': detected_confidences\n", "    })\n", "    \n", "    detections_path = f\"{OUTPUT_DIR}/high_confidence_detections_{timestamp}.csv\"\n", "    detections_df.to_csv(detections_path, index=False)\n", "    print(f\"High-confidence detections saved to: {detections_path}\")\n", "    \n", "    # Display sample detections\n", "    print(\"\\nSample high-confidence detections:\")\n", "    print(detections_df.head(10))\n", "else:\n", "    print(\"No high-confidence detections found\")\n", "\n", "print(f\"\\n=== INFERENCE COMPLETE ===\")\n", "print(f\"Results saved to: {OUTPUT_DIR}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}