{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ONNX Buffer KML Inference - Fixed\n", "\n", "Simple inference using ONNX models - no architecture mismatch issues!\n", "\n", "**Benefits:**\n", "- No need for complex PyTorch helper functions\n", "- No tensor shape issues\n", "- Faster inference\n", "- Version independent"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ONNX Runtime version: 1.22.0\n", "Available providers: ['CoreMLExecutionProvider', 'AzureExecutionProvider', 'CPUExecutionProvider']\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import laspy\n", "import geopandas as gpd\n", "import onnxruntime as ort\n", "from pathlib import Path\n", "from datetime import datetime\n", "from sklearn.neighbors import KDTree\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(f\"ONNX Runtime version: {ort.__version__}\")\n", "print(f\"Available providers: {ort.get_available_providers()}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Configuration set for althea_rpcs\n"]}], "source": ["# Configuration\n", "SITE_NAME = \"althea_rpcs\"\n", "POINT_CLOUD_PATH = \"../../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "BUFFER_KML_PATH = \"../../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "OUTPUT_DIR = f\"output_runs/onnx_inference/{SITE_NAME}\"\n", "PATCH_RADIUS = 3.0\n", "NUM_POINTS = 64\n", "CONFIDENCE_THRESHOLD = 0.85\n", "MIN_POINTS_PER_PATCH = 30\n", "\n", "# ONNX model paths\n", "MODELS = {\n", "    'Simple PointNet': '../../training/simplepointnet_fair_comparison_best_model.onnx',\n", "    'DGCNN': '../../training/dgcnn_best_model.onnx'\n", "}\n", "\n", "Path(OUTPUT_DIR).mkdir(parents=True, exist_ok=True)\n", "print(f\"Configuration set for {SITE_NAME}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data loading functions defined\n"]}], "source": ["# Data loading functions\n", "def load_point_cloud(file_path):\n", "    las = laspy.read(file_path)\n", "    points = np.vstack((las.x, las.y, las.z)).transpose()\n", "    return points\n", "\n", "def load_buffer_kml_candidates(kml_path):\n", "    print(f\"Loading Buffer KML from {kml_path}...\")\n", "    \n", "    gdf = gpd.read_file(kml_path)\n", "    print(f\"Loaded {len(gdf)} pile locations from KML\")\n", "    \n", "    # Reproject from WGS84 to UTM Zone 15N\n", "    gdf_utm = gdf.to_crs('EPSG:32615')\n", "    \n", "    candidates = []\n", "    for idx, row in gdf_utm.iterrows():\n", "        geom = row.geometry\n", "        if geom.geom_type == 'Point':\n", "            candidates.append({\n", "                'pile_id': f'buffer_pile_{idx}',\n", "                'x': geom.x,\n", "                'y': geom.y,\n", "                'source': 'buffer_kml'\n", "            })\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            candidates.append({\n", "                'pile_id': f'buffer_pile_{idx}',\n", "                'x': centroid.x,\n", "                'y': centroid.y,\n", "                'source': 'buffer_kml'\n", "            })\n", "    \n", "    print(f\"Reprojected {len(candidates)} candidates to UTM Zone 15N\")\n", "    return candidates\n", "\n", "def extract_patch_at_location(tree, points, candidate, radius=3.0, num_points=64):\n", "    center = [candidate['x'], candidate['y']]\n", "    indices = tree.query_radius([center], r=radius)[0]\n", "    \n", "    if len(indices) < MIN_POINTS_PER_PATCH:\n", "        return None\n", "    \n", "    patch_points = points[indices].copy()\n", "    \n", "    # Sample or pad to exact number of points\n", "    if len(patch_points) > num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[sample_indices]\n", "    elif len(patch_points) < num_points:\n", "        sample_indices = np.random.choice(len(patch_points), num_points, replace=True)\n", "        patch_points = patch_points[sample_indices]\n", "    \n", "    # Normalize to patch center\n", "    patch_points[:, 0] -= center[0]\n", "    patch_points[:, 1] -= center[1]\n", "    \n", "    return patch_points\n", "\n", "def softmax(x):\n", "    exp_x = np.exp(x - np.max(x, axis=1, keepdims=True))\n", "    return exp_x / np.sum(exp_x, axis=1, keepdims=True)\n", "\n", "print(\"Data loading functions defined\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data...\n", "Point cloud loaded: 52,862,386 points\n", "Loading Buffer KML from ../../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml...\n", "Loaded 1359 pile locations from KML\n", "Reprojected 1359 candidates to UTM Zone 15N\n", "Loaded 1359 Buffer KML candidates\n"]}], "source": ["# Load data\n", "print(\"Loading data...\")\n", "points = load_point_cloud(POINT_CLOUD_PATH)\n", "tree = KDTree(points[:, :2])\n", "print(f\"Point cloud loaded: {len(points):,} points\")\n", "\n", "pile_candidates = load_buffer_kml_candidates(BUFFER_KML_PATH)\n", "print(f\"Loaded {len(pile_candidates)} Buffer KML candidates\")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Loading ONNX models...\n", "Loaded Simple PointNet from ../../training/simplepointnet_fair_comparison_best_model.onnx\n", "Loaded DGCNN from ../../training/dgcnn_best_model.onnx\n", "\n", "Loaded 2 ONNX models successfully\n"]}], "source": ["# Load ONNX models\n", "print(\"\\nLoading ONNX models...\")\n", "onnx_sessions = {}\n", "\n", "for model_name, model_path in MODELS.items():\n", "    if Path(model_path).exists():\n", "        try:\n", "            session = ort.InferenceSession(model_path)\n", "            onnx_sessions[model_name] = session\n", "            print(f\"Loaded {model_name} from {model_path}\")\n", "        except Exception as e:\n", "            print(f\"Failed to load {model_name}: {e}\")\n", "    else:\n", "        print(f\"Model file not found: {model_path}\")\n", "\n", "print(f\"\\nLoaded {len(onnx_sessions)} ONNX models successfully\")\n", "if len(onnx_sessions) == 0:\n", "    print(\"No ONNX models found. Please run the export_models_to_onnx.ipynb notebook first.\")\n", "    raise RuntimeError(\"No ONNX models available\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Starting ONNX inference...\n", "\n", "=== Running Simple PointNet inference ===\n", "  Processed 50/1359 candidates...\n", "  Processed 100/1359 candidates...\n", "  Processed 150/1359 candidates...\n", "  Processed 200/1359 candidates...\n", "  Processed 250/1359 candidates...\n", "  Processed 300/1359 candidates...\n", "  Processed 350/1359 candidates...\n", "  Processed 400/1359 candidates...\n", "  Processed 450/1359 candidates...\n", "  Processed 500/1359 candidates...\n", "  Processed 550/1359 candidates...\n", "  Processed 600/1359 candidates...\n", "  Processed 650/1359 candidates...\n", "  Processed 700/1359 candidates...\n", "  Processed 750/1359 candidates...\n", "  Processed 800/1359 candidates...\n", "  Processed 850/1359 candidates...\n", "  Processed 900/1359 candidates...\n", "  Processed 950/1359 candidates...\n", "  Processed 1000/1359 candidates...\n", "  Processed 1050/1359 candidates...\n", "  Processed 1100/1359 candidates...\n", "  Processed 1150/1359 candidates...\n", "  Processed 1200/1359 candidates...\n", "  Processed 1250/1359 candidates...\n", "  Processed 1300/1359 candidates...\n", "  Processed 1350/1359 candidates...\n", "  Total candidates: 1359\n", "  Valid patches: 1359\n", "  High-confidence pile detections: 1359\n", "  Detection rate: 100.0%\n", "\n", "=== Running DGCNN inference ===\n", "  Processed 50/1359 candidates...\n", "  Processed 100/1359 candidates...\n", "  Processed 150/1359 candidates...\n", "  Processed 200/1359 candidates...\n", "  Processed 250/1359 candidates...\n", "  Processed 300/1359 candidates...\n", "  Processed 350/1359 candidates...\n", "  Processed 400/1359 candidates...\n", "  Processed 450/1359 candidates...\n", "  Processed 500/1359 candidates...\n", "  Processed 550/1359 candidates...\n", "  Processed 600/1359 candidates...\n", "  Processed 650/1359 candidates...\n", "  Processed 700/1359 candidates...\n", "  Processed 750/1359 candidates...\n", "  Processed 800/1359 candidates...\n", "  Processed 850/1359 candidates...\n", "  Processed 900/1359 candidates...\n", "  Processed 950/1359 candidates...\n", "  Processed 1000/1359 candidates...\n", "  Processed 1050/1359 candidates...\n", "  Processed 1100/1359 candidates...\n", "  Processed 1150/1359 candidates...\n", "  Processed 1200/1359 candidates...\n", "  Processed 1250/1359 candidates...\n", "  Processed 1300/1359 candidates...\n", "  Processed 1350/1359 candidates...\n", "  Total candidates: 1359\n", "  Valid patches: 1359\n", "  High-confidence pile detections: 1359\n", "  Detection rate: 100.0%\n", "\n", "ONNX inference complete for all models!\n"]}], "source": ["# Run ONNX inference for all models\n", "print(\"\\nStarting ONNX inference...\")\n", "timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "\n", "all_results = {}\n", "\n", "for model_name, session in onnx_sessions.items():\n", "    print(f\"\\n=== Running {model_name} inference ===\")\n", "    \n", "    results = []\n", "    processed = 0\n", "    pile_detections = 0\n", "    valid_patches = 0\n", "    \n", "    # Process all candidates\n", "    for candidate in pile_candidates:\n", "        # Extract patch\n", "        if model_name == 'DGCNN':\n", "            # DGCNN needs 256 points\n", "            patch = extract_patch_at_location(tree, points, candidate, PATCH_RADIUS, 256)\n", "        else:\n", "            # Simple PointNet uses 64 points\n", "            patch = extract_patch_at_location(tree, points, candidate, PATCH_RADIUS, NUM_POINTS)\n", "            \n", "        if patch is None:\n", "            continue\n", "            \n", "        valid_patches += 1\n", "        \n", "        # Prepare input for ONNX - All models expect (batch, 3, num_points)\n", "        input_data = patch.T.astype(np.float32)  # (3, num_points)\n", "        input_data = np.expand_dims(input_data, 0)  # (1, 3, num_points)\n", "        \n", "        # Run ONNX inference\n", "        try:\n", "            outputs = session.run(None, {'input': input_data})\n", "            logits = outputs[0][0]  # Remove batch dimension\n", "            \n", "            # Apply softmax to get probabilities\n", "            probabilities = softmax(logits.reshape(1, -1))[0]\n", "            pile_prob = probabilities[1]\n", "            is_pile = pile_prob >= 0.5\n", "            \n", "            if is_pile and pile_prob >= CONFIDENCE_THRESHOLD:\n", "                pile_detections += 1\n", "            \n", "            results.append({\n", "                'pile_id': candidate['pile_id'],\n", "                'x': candidate['x'],\n", "                'y': candidate['y'],\n", "                'predicted_class': 'PILE' if is_pile else 'NON_PILE',\n", "                'pile_probability': float(pile_prob),\n", "                'non_pile_probability': float(probabilities[0]),\n", "                'confidence_level': 'HIGH' if pile_prob >= CONFIDENCE_THRESHOLD else 'LOW',\n", "                'source': candidate['source'],\n", "                'model': model_name,\n", "                'timestamp': timestamp\n", "            })\n", "            \n", "        except Exception as e:\n", "            print(f\"Error processing candidate {candidate['pile_id']}: {e}\")\n", "            continue\n", "        \n", "        processed += 1\n", "        if processed % 50 == 0:\n", "            print(f\"  Processed {processed}/{len(pile_candidates)} candidates...\")\n", "    \n", "    # Store results for this model\n", "    all_results[model_name] = results\n", "    \n", "    print(f\"  Total candidates: {len(pile_candidates)}\")\n", "    print(f\"  Valid patches: {valid_patches}\")\n", "    print(f\"  High-confidence pile detections: {pile_detections}\")\n", "    print(f\"  Detection rate: {pile_detections/valid_patches*100:.1f}%\")\n", "\n", "print(f\"\\nONNX inference complete for all models!\")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS AND GENERATING COMPARISON ===\n", "Saved Simple PointNet results: output_runs/onnx_inference/althea_rpcs/simple_pointnet_onnx_inference_20250814_191036.csv\n", "Saved DGCNN results: output_runs/onnx_inference/althea_rpcs/dgcnn_onnx_inference_20250814_191036.csv\n", "\n", "=== MODEL COMPARISON RESULTS ===\n", "          model  total_candidates  pile_predictions  high_confidence_piles  detection_rate  high_conf_rate  avg_pile_probability inference_type       timestamp\n", "Simple PointNet              1359              1359                   1359           100.0           100.0                   1.0           ONNX 20250814_191036\n", "          DGCNN              1359              1359                   1359           100.0           100.0                   1.0           ONNX 20250814_191036\n", "\n", "=== FILES CREATED ===\n", "  Simple PointNet: simple_pointnet_onnx_inference_20250814_191036.csv\n", "  DGCNN: dgcnn_onnx_inference_20250814_191036.csv\n", "  Comparison: model_comparison_onnx_20250814_191036.csv\n", "\n", "✅ ONNX inference complete!\n", "   No architecture mismatch issues\n", "   Fast and reliable inference\n", "   Ready for validation against ground truth\n"]}], "source": ["# Save results and generate comparison\n", "print(\"\\n=== SAVING RESULTS AND GENERATING COMPARISON ===\")\n", "\n", "# Save individual model results\n", "for model_name, results in all_results.items():\n", "    results_df = pd.DataFrame(results)\n", "    output_file = f\"{OUTPUT_DIR}/{model_name.lower().replace(' ', '_')}_onnx_inference_{timestamp}.csv\"\n", "    results_df.to_csv(output_file, index=False)\n", "    print(f\"Saved {model_name} results: {output_file}\")\n", "\n", "# Generate comparison summary\n", "comparison_data = []\n", "for model_name, results in all_results.items():\n", "    results_df = pd.DataFrame(results)\n", "    \n", "    total_candidates = len(results_df)\n", "    pile_predictions = len(results_df[results_df['predicted_class'] == 'PILE'])\n", "    high_conf_piles = len(results_df[(results_df['predicted_class'] == 'PILE') & (results_df['confidence_level'] == 'HIGH')])\n", "    \n", "    if pile_predictions > 0:\n", "        avg_pile_prob = results_df[results_df['predicted_class'] == 'PILE']['pile_probability'].mean()\n", "    else:\n", "        avg_pile_prob = 0.0\n", "    \n", "    comparison_data.append({\n", "        'model': model_name,\n", "        'total_candidates': total_candidates,\n", "        'pile_predictions': pile_predictions,\n", "        'high_confidence_piles': high_conf_piles,\n", "        'detection_rate': (pile_predictions / total_candidates * 100) if total_candidates > 0 else 0,\n", "        'high_conf_rate': (high_conf_piles / total_candidates * 100) if total_candidates > 0 else 0,\n", "        'avg_pile_probability': avg_pile_prob,\n", "        'inference_type': 'ONNX',\n", "        'timestamp': timestamp\n", "    })\n", "\n", "# Save comparison\n", "comparison_df = pd.DataFrame(comparison_data)\n", "comparison_file = f\"{OUTPUT_DIR}/model_comparison_onnx_{timestamp}.csv\"\n", "comparison_df.to_csv(comparison_file, index=False)\n", "\n", "print(f\"\\n=== MODEL COMPARISON RESULTS ===\")\n", "print(comparison_df.to_string(index=False))\n", "\n", "print(f\"\\n=== FILES CREATED ===\")\n", "for model_name in all_results.keys():\n", "    print(f\"  {model_name}: {model_name.lower().replace(' ', '_')}_onnx_inference_{timestamp}.csv\")\n", "print(f\"  Comparison: model_comparison_onnx_{timestamp}.csv\")\n", "\n", "print(f\"\\n✅ ONNX inference complete!\")\n", "print(f\"   No architecture mismatch issues\")\n", "print(f\"   Fast and reliable inference\")\n", "print(f\"   Ready for validation against ground truth\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}