{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# True Model Generalization Test\n", "\n", "Test **true model generalization**: Train model on RES data, test on RCPS data without retraining.\n", "\n", "**Research Question**: Can a model trained on one construction site work on another site without retraining?\n", "\n", "**Approach**: \n", "1. Train model on RES data (with negative samples)\n", "2. Save trained model\n", "3. Load model and test on RCPS data (no retraining)\n", "4. Measure true generalization performance\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": 5, "id": "params", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TRUE MODEL GENERALIZATION TEST\n", "Training site: nortan_res\n", "Testing site: althea_rcps\n", "No retraining - pure model transfer\n"]}], "source": ["# Parameters\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# RES data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH = \"../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "# RCPS data paths\n", "RCPS_POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH = \"../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# Patch parameters (consistent across both sites)\n", "PATCH_RADIUS = 3.0  # meters\n", "MIN_POINTS = 20\n", "TARGET_PATCH_SIZE = 64  # points per patch\n", "\n", "print(f\"TRUE MODEL GENERALIZATION TEST\")\n", "print(f\"Training site: {RES_SITE_NAME}\")\n", "print(f\"Testing site: {RCPS_SITE_NAME}\")\n", "print(f\"No retraining - pure model transfer\")"]}, {"cell_type": "code", "execution_count": 6, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import json\n", "import joblib\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Point cloud and spatial\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "\n", "# ML\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "\n", "# Viz\n", "import matplotlib.pyplot as plt\n", "\n", "print(\"Libraries imported\")\n", "\n", "# Create output dir\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 7, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Utility functions defined\n"]}], "source": ["def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):\n", "    \"\"\"Extract patches around locations\"\"\"\n", "    print(f\"Extracting patches (radius={radius}m, min_points={min_points}, target_size={TARGET_PATCH_SIZE})\")\n", "    \n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    original_sizes = []\n", "    \n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            original_sizes.append(len(patch_points))\n", "            \n", "            # Subsample to target size\n", "            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "            \n", "            # Center patch\n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "            \n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "    \n", "    print(f\"Extracted {len(patches)} valid patches\")\n", "    if original_sizes:\n", "        print(f\"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}\")\n", "        final_sizes = [len(p) for p in patches]\n", "        print(f\"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}\")\n", "    \n", "    return patches, np.array(valid_locs)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Extract 22 features from patches\"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        \n", "        # Compute derived features\n", "        radial_dist = np.sqrt(x**2 + y**2)\n", "        height_above_min = z - np.min(z)\n", "        \n", "        # Statistical features (22 features total)\n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch),  # num_points\n", "            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio\n", "            np.percentile(radial_dist, 90),  # 90th percentile radial distance\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height\n", "        ]\n", "        features.append(feature_vector)\n", "    \n", "    return np.array(features)\n", "\n", "def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "    \n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "    \n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    \n", "    return pile_locations_utm\n", "\n", "print(\"Utility functions defined\")"]}, {"cell_type": "code", "execution_count": 8, "id": "train_on_res", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "STEP 1: TRAINING MODEL ON RES DATA\n", "============================================================\n", "\n", "Loading RES point cloud: ../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Loaded 35,565,352 points\n", "\n", "Loading RES pile locations: ../../../../data/raw/nortan_res/kml/Buffer_2m.kml\n", "Loaded 368 pile locations\n", "\n", "Extracting positive patches from RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 368 valid patches\n", "Original sizes: min=86195, max=152283, mean=96645.0\n", "Final sizes: min=64, max=64, mean=64.0\n", "Extracted 368 positive patches\n", "\n", "Creating negative samples for RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 357 valid patches\n", "Original sizes: min=65, max=105814, mean=43152.4\n", "Final sizes: min=64, max=64, mean=64.0\n", "Created 357 negative patches\n", "\n", "Extracting features from RES patches...\n", "RES training dataset: 725 samples (368.0 positive, 357.0 negative)\n", "Features per sample: 22\n", "\n", "Training Gradient Boosting model on RES data...\n", "\n", "RES model performance (sanity check):\n", "  Detection rate on RES piles: 100.0%\n", "  Average confidence: 1.000\n", "\n", "Model saved: output_runs/true_generalization/res_trained_model_20250815_102825.pkl\n", "RES training complete!\n"]}], "source": ["# STEP 1: TRAIN MODEL ON RES DATA\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"STEP 1: TRAINING MODEL ON RES DATA\")\n", "print(\"=\" * 60)\n", "\n", "# Load RES point cloud\n", "print(f\"\\nLoading RES point cloud: {RES_POINT_CLOUD_PATH}\")\n", "res_las = laspy.read(RES_POINT_CLOUD_PATH)\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"Loaded {len(res_points):,} points\")\n", "\n", "# Load RES pile locations\n", "print(f\"\\nLoading RES pile locations: {RES_BUFFER_KML_PATH}\")\n", "res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)\n", "print(f\"Loaded {len(res_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (pile locations)\n", "print(f\"\\nExtracting positive patches from RES...\")\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations)\n", "print(f\"Extracted {len(res_pos_patches)} positive patches\")\n", "\n", "# Create negative samples for RES\n", "print(f\"\\nCreating negative samples for RES...\")\n", "np.random.seed(42)\n", "x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()\n", "y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()\n", "\n", "n_negative = len(res_pos_patches)\n", "random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)\n", "random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)\n", "random_locations = np.column_stack([random_x, random_y])\n", "\n", "res_neg_patches, _ = extract_patches(res_points, random_locations)\n", "print(f\"Created {len(res_neg_patches)} negative patches\")\n", "\n", "# Extract features\n", "print(f\"\\nExtracting features from RES patches...\")\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = extract_features(res_neg_patches)\n", "\n", "# Combine training data\n", "X_train_res = np.vstack([X_pos_res, X_neg_res])\n", "y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])\n", "\n", "print(f\"RES training dataset: {len(X_train_res)} samples ({np.sum(y_train_res)} positive, {len(y_train_res) - np.sum(y_train_res)} negative)\")\n", "print(f\"Features per sample: {X_train_res.shape[1]}\")\n", "\n", "# Train model on RES data\n", "print(f\"\\nTraining Gradient Boosting model on RES data...\")\n", "res_model = GradientBoostingClassifier(\n", "    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42\n", ")\n", "res_model.fit(X_train_res, y_train_res)\n", "\n", "# Test model on RES data (sanity check)\n", "y_pred_res = res_model.predict(X_pos_res)\n", "y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]\n", "res_detection_rate = np.mean(y_pred_res)\n", "res_avg_confidence = np.mean(y_prob_res)\n", "\n", "print(f\"\\nRES model performance (sanity check):\")\n", "print(f\"  Detection rate on RES piles: {res_detection_rate*100:.1f}%\")\n", "print(f\"  Average confidence: {res_avg_confidence:.3f}\")\n", "\n", "# Save trained model\n", "model_file = output_dir / f\"res_trained_model_{timestamp}.pkl\"\n", "joblib.dump(res_model, model_file)\n", "print(f\"\\nModel saved: {model_file}\")\n", "print(f\"RES training complete!\")"]}, {"cell_type": "code", "execution_count": 9, "id": "test_on_rcps", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "STEP 2: TESTING RES MODEL ON RCPS DATA\n", "============================================================\n", "NO RETRAINING - <PERSON>URE MODEL TRANSFER\n", "\n", "Loading RCPS point cloud: ../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n"]}, {"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: '../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 9\u001b[39m\n\u001b[32m      7\u001b[39m \u001b[38;5;66;03m# Load RCPS point cloud\u001b[39;00m\n\u001b[32m      8\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[33mLoading RCPS point cloud: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mRCPS_POINT_CLOUD_PATH\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m9\u001b[39m rcps_las = \u001b[43mlaspy\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[43mRCPS_POINT_CLOUD_PATH\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     10\u001b[39m rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n\u001b[32m     11\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mLoaded \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(rcps_points)\u001b[38;5;132;01m:\u001b[39;00m\u001b[33m,\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m points\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/laspy/lib.py:249\u001b[39m, in \u001b[36mread_las\u001b[39m\u001b[34m(source, closefd, laz_backend, decompression_selection)\u001b[39m\n\u001b[32m    209\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mread_las\u001b[39m(\n\u001b[32m    210\u001b[39m     source,\n\u001b[32m    211\u001b[39m     closefd=\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[32m    212\u001b[39m     laz_backend=LazBackend.detect_available(),\n\u001b[32m    213\u001b[39m     decompression_selection: DecompressionSelection = DecompressionSelection.all(),\n\u001b[32m    214\u001b[39m ):\n\u001b[32m    215\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Entry point for reading las data in laspy\u001b[39;00m\n\u001b[32m    216\u001b[39m \n\u001b[32m    217\u001b[39m \u001b[33;03m    Reads the whole file into memory.\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    247\u001b[39m \u001b[33;03m        The ``decompression_selection`` parameter.\u001b[39;00m\n\u001b[32m    248\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m249\u001b[39m     \u001b[38;5;28;01mwith\u001b[39;00m \u001b[43mopen_las\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    250\u001b[39m \u001b[43m        \u001b[49m\u001b[43msource\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    251\u001b[39m \u001b[43m        \u001b[49m\u001b[43mclosefd\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclosefd\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    252\u001b[39m \u001b[43m        \u001b[49m\u001b[43mlaz_backend\u001b[49m\u001b[43m=\u001b[49m\u001b[43mlaz_backend\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    253\u001b[39m \u001b[43m        \u001b[49m\u001b[43mdecompression_selection\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdecompression_selection\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    254\u001b[39m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m \u001b[38;5;28;01mas\u001b[39;00m reader:\n\u001b[32m    255\u001b[39m         \u001b[38;5;28;01mreturn\u001b[39;00m reader.read()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/laspy/lib.py:142\u001b[39m, in \u001b[36mopen_las\u001b[39m\u001b[34m(source, mode, closefd, laz_backend, header, do_compress, encoding_errors, read_evlrs, decompression_selection)\u001b[39m\n\u001b[32m    137\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m LaspyException(\n\u001b[32m    138\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mdo_compress argument is not used when opening in read mode, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    139\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mdid you meant to open in write mode ?\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    140\u001b[39m     )\n\u001b[32m    141\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(source, (\u001b[38;5;28mstr\u001b[39m, Path)):\n\u001b[32m--> \u001b[39m\u001b[32m142\u001b[39m     stream = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msource\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mclosefd\u001b[49m\u001b[43m=\u001b[49m\u001b[43mclosefd\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    143\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(source, \u001b[38;5;28mbytes\u001b[39m):\n\u001b[32m    144\u001b[39m     stream = io.BytesIO(source)\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: '../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las'"]}], "source": ["# STEP 2: TEST MODEL ON RCPS DATA (NO RETRAINING)\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"STEP 2: TESTING RES MODEL ON RCPS DATA\")\n", "print(\"=\" * 60)\n", "print(\"NO RETRAINING - PURE MODEL TRANSFER\")\n", "\n", "# Load RCPS point cloud\n", "print(f\"\\nLoading RCPS point cloud: {RCPS_POINT_CLOUD_PATH}\")\n", "rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"Loaded {len(rcps_points):,} points\")\n", "\n", "# Load RCPS pile locations\n", "print(f\"\\nLoading RCPS pile locations: {RCPS_BUFFER_KML_PATH}\")\n", "rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)\n", "print(f\"Loaded {len(rcps_pile_locations)} pile locations\")\n", "\n", "# Extract patches from RCPS pile locations\n", "print(f\"\\nExtracting patches from RCPS pile locations...\")\n", "rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)\n", "print(f\"Extracted {len(rcps_patches)} patches from RCPS\")\n", "\n", "# Extract features from RCPS patches\n", "print(f\"\\nExtracting features from RCPS patches...\")\n", "X_rcps = extract_features(rcps_patches)\n", "print(f\"RCPS features: {X_rcps.shape}\")\n", "\n", "# Load trained RES model\n", "print(f\"\\nLoading trained RES model...\")\n", "loaded_model = joblib.load(model_file)\n", "print(f\"Model loaded successfully\")\n", "\n", "# Apply RES model to RCPS data (NO RETRAINING!)\n", "print(f\"\\nApplying RES-trained model to RCPS data...\")\n", "y_pred_rcps = loaded_model.predict(X_rcps)\n", "y_prob_rcps = loaded_model.predict_proba(X_rcps)[:, 1]\n", "\n", "rcps_detection_rate = np.mean(y_pred_rcps)\n", "rcps_avg_confidence = np.mean(y_prob_rcps)\n", "\n", "# Results\n", "print(f\"\\nTRUE MODEL GENERALIZATION RESULTS:\")\n", "print(f\"  Training site: {RES_SITE_NAME}\")\n", "print(f\"  Testing site: {RCPS_SITE_NAME}\")\n", "print(f\"  Known pile locations tested: {len(rcps_pile_locations)}\")\n", "print(f\"  Valid patches extracted: {len(rcps_patches)}\")\n", "print(f\"  Detected as piles: {np.sum(y_pred_rcps)} ({rcps_detection_rate*100:.1f}%)\")\n", "print(f\"  Average confidence: {rcps_avg_confidence:.3f}\")\n", "\n", "# Compare with RES performance\n", "performance_diff = rcps_detection_rate - res_detection_rate\n", "\n", "print(f\"\\nGENERALIZATION ANALYSIS:\")\n", "print(f\"  RES performance (training site): {res_detection_rate*100:.1f}%\")\n", "print(f\"  RCPS performance (test site): {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "# Interpretation\n", "if rcps_detection_rate >= 0.9:\n", "    if abs(performance_diff) <= 0.1:\n", "        status = \"EXCELLENT GENERALIZATION\"\n", "        interpretation = \"Model transfers perfectly across construction sites\"\n", "    else:\n", "        status = \"GOOD GENERALIZATION\"\n", "        interpretation = \"Model works well but with some site-specific differences\"\n", "elif rcps_detection_rate >= 0.7:\n", "    status = \"MODERATE GENERALIZATION\"\n", "    interpretation = \"Model partially generalizes - may need fine-tuning\"\n", "else:\n", "    status = \"POOR GENERALIZATION\"\n", "    interpretation = \"Model fails to generalize - site-specific training needed\"\n", "\n", "print(f\"\\nCONCLUSION:\")\n", "print(f\"  {status}\")\n", "print(f\"  {interpretation}\")\n", "\n", "# Save results\n", "results = {\n", "    'experiment_info': {\n", "        'type': 'true_model_generalization',\n", "        'training_site': RES_SITE_NAME,\n", "        'testing_site': RCPS_SITE_NAME,\n", "        'timestamp': timestamp,\n", "        'model_file': str(model_file)\n", "    },\n", "    'performance_metrics': {\n", "        'res_detection_rate': float(res_detection_rate),\n", "        'res_avg_confidence': float(res_avg_confidence),\n", "        'rcps_detection_rate': float(rcps_detection_rate),\n", "        'rcps_avg_confidence': float(rcps_avg_confidence),\n", "        'performance_difference': float(performance_diff),\n", "        'generalization_status': status\n", "    },\n", "    'data_info': {\n", "        'res_pile_locations': len(res_pile_locations),\n", "        'res_patches': len(res_pos_patches),\n", "        'rcps_pile_locations': len(rcps_pile_locations),\n", "        'rcps_patches': len(rcps_patches),\n", "        'patch_radius': PATCH_RADIUS,\n", "        'target_patch_size': TARGET_PATCH_SIZE\n", "    }\n", "}\n", "\n", "results_file = output_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nResults saved: {results_file}\")\n", "\n", "# Export RCPS results for QGIS visualization\n", "print(f\"\\nExporting RCPS generalization results for QGIS...\")\n", "\n", "# Create comprehensive results DataFrame for QGIS\n", "rcps_results_df = pd.DataFrame({\n", "    'pile_id': range(len(rcps_pile_locations)),\n", "    'utm_x': rcps_pile_locations[:, 0],\n", "    'utm_y': rcps_pile_locations[:, 1], \n", "    'predicted_pile': y_pred_rcps,\n", "    'confidence': y_prob_rcps,\n", "    'training_site': RES_SITE_NAME,\n", "    'test_site': RCPS_SITE_NAME,\n", "    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],\n", "    'generalization_type': 'true_model_transfer'\n", "})\n", "\n", "# Convert UTM coordinates to geographic (WGS84) for QGIS\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "\n", "# Create GeoDataFrame with UTM coordinates (Zone 15N for RCPS)\n", "geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]\n", "gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')  # UTM Zone 15N\n", "\n", "# Convert to WGS84 for QGIS\n", "gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "\n", "# Extract lat/lon from geometry\n", "rcps_results_df['longitude'] = gdf_wgs84.geometry.x\n", "rcps_results_df['latitude'] = gdf_wgs84.geometry.y\n", "\n", "# Save CSV for QGIS\n", "csv_file = output_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "rcps_results_df.to_csv(csv_file, index=False)\n", "\n", "print(f\"QGIS CSV exported: {csv_file}\")\n", "print(f\"Columns: pile_id, utm_x, utm_y, longitude, latitude, predicted_pile, confidence, detection_status, training_site, test_site\")\n", "print(f\"Coordinate System: WGS84 (EPSG:4326) + UTM Zone 15N\")\n", "print(f\"Total RCPS points: {len(rcps_results_df)}\")\n", "\n", "# Generalization summary for QGIS\n", "detected_count = np.sum(y_pred_rcps)\n", "missed_count = len(y_pred_rcps) - detected_count\n", "\n", "print(f\"\\nQGIS Generalization Visualization:\")\n", "print(f\"  Training site: {RES_SITE_NAME} (368 piles)\")\n", "print(f\"  Test site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)\")\n", "print(f\"  Detected: {detected_count} piles\")\n", "print(f\"  Missed: {missed_count} piles\")\n", "print(f\"  Generalization rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  Average confidence: {rcps_avg_confidence:.3f}\")\n", "\n", "print(f\"\\nTrue model generalization test complete!\")\n", "print(f\"\\nSUMMARY:\")\n", "print(f\"  Trained once on {RES_SITE_NAME}\")\n", "print(f\"  Tested on {RCPS_SITE_NAME} without retraining\")\n", "print(f\"  Detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  This is TRUE model generalization!\")\n", "print(f\"  QGIS visualization ready: {csv_file}\")"]}, {"cell_type": "code", "execution_count": null, "id": "false_positive_test", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "FALSE POSITIVE RATE TEST ON RCPS\n", "============================================================\n", "Testing RES-trained model on RCPS random non-pile locations\n", "This tests cross-site discrimination ability\n", "Generated 300 RCPS test locations (>2.0m from known piles)\n", "Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives\n", "Extracting patches (radius=1.5m, min_points=3, target_size=64)\n", "Extracted 175 valid patches\n", "Original sizes: min=3, max=15811, mean=3212.3\n", "Final sizes: min=3, max=64, mean=62.8\n", "\n", "📊 RCPS FALSE POSITIVE ANALYSIS:\n", "  RCPS test negative locations: 300\n", "  Valid negative patches: 175\n", "  False positives: 175.0 / 175\n", "  False positive rate: 100.0%\n", "  Avg confidence on negatives: 1.000\n", "  ❌ POOR - Too many false positives across sites\n", "\n", "🎯 COMPLETE GENERALIZATION VALIDATION:\n", "  Training site: nortan_res\n", "  Test site: althea_rcps\n", "  Pile detection rate: 100.0%\n", "  False positive rate: 100.0%\n", "  Cross-site performance: ❌ POOR - Too many false positives across sites\n", "\n", "🏆 FINAL ASSESSMENT: ⚠️ GENERALIZATION ISSUES - Needs investigation\n", "\n", "📍 QGIS Negative Test CSV: output_runs/true_generalization/rcps_negative_test_results_20250807_214117.csv\n", "   Use this to visualize false positive locations\n", "   False positives: 175.0 red points\n", "   True negatives: 0.0 green points\n"]}], "source": ["# FALSE POSITIVE TEST ON RCPS - Critical for generalization validation\n", "if len(rcps_patches) > 0 and 'loaded_model' in locals():\n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\"FALSE POSITIVE RATE TEST ON RCPS\")\n", "    print(\"=\" * 60)\n", "    print(\"Testing RES-trained model on RCPS random non-pile locations\")\n", "    print(\"This tests cross-site discrimination ability\")\n", "    \n", "    # Generate random test locations in RCPS (away from known piles)\n", "    np.random.seed(456)  # Different seed for RCPS test locations\n", "    n_test_negatives = min(300, len(rcps_patches))  # Test more for larger dataset\n", "    \n", "    # Sample random locations with buffer from known piles\n", "    buffer_distance = 2.0  # 2m buffer from known piles (very reduced for dense RCPS)\n", "    max_attempts = 3000  # Increased attempts for larger site\n", "    test_negative_locations = []\n", "    \n", "    rcps_x_min, rcps_x_max = rcps_points[:, 0].min(), rcps_points[:, 0].max()\n", "    rcps_y_min, rcps_y_max = rcps_points[:, 1].min(), rcps_points[:, 1].max()\n", "    \n", "    for attempt in range(max_attempts):\n", "        if len(test_negative_locations) >= n_test_negatives:\n", "            break\n", "            \n", "        # Random location within RCPS point cloud bounds (reduced margins)\n", "        test_x = np.random.uniform(rcps_x_min + 50, rcps_x_max - 50)\n", "        test_y = np.random.uniform(rcps_y_min + 50, rcps_y_max - 50)\n", "        test_loc = np.array([test_x, test_y])\n", "        \n", "        # Check distance from all known RCPS piles\n", "        distances = np.sqrt(np.sum((rcps_pile_locations[:, :2] - test_loc)**2, axis=1))\n", "        min_distance = np.min(distances)\n", "        \n", "        # Only use if far enough from known piles\n", "        if min_distance > buffer_distance:\n", "            test_negative_locations.append(test_loc)\n", "    \n", "    test_negative_locations = np.array(test_negative_locations)\n", "    print(f\"Generated {len(test_negative_locations)} RCPS test locations (>{buffer_distance}m from known piles)\")\n", "    print(f\"Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives\")\n", "    \n", "    if len(test_negative_locations) > 0:\n", "        # Extract patches from RCPS test negative locations (very relaxed parameters)\n", "        test_neg_patches, _ = extract_patches(rcps_points, test_negative_locations, 1.5, 3)  # Very small radius, minimal points\n", "        \n", "        if len(test_neg_patches) > 0:\n", "            # Subsample test negative patches\n", "            test_neg_patches = [subsample_patch(patch, TARGET_PATCH_SIZE) for patch in test_neg_patches]\n", "            \n", "            # Extract features and predict using RES-trained model\n", "            X_test_neg_rcps = extract_features(test_neg_patches)\n", "            y_pred_test_neg = loaded_model.predict(X_test_neg_rcps)\n", "            y_prob_test_neg = loaded_model.predict_proba(X_test_neg_rcps)[:, 1]\n", "            \n", "            false_positive_rate_rcps = np.mean(y_pred_test_neg)\n", "            avg_confidence_fp_rcps = np.mean(y_prob_test_neg)\n", "            \n", "            print(f\"\\n📊 RCPS FALSE POSITIVE ANALYSIS:\")\n", "            print(f\"  RCPS test negative locations: {len(test_negative_locations)}\")\n", "            print(f\"  Valid negative patches: {len(test_neg_patches)}\")\n", "            print(f\"  False positives: {np.sum(y_pred_test_neg)} / {len(y_pred_test_neg)}\")\n", "            print(f\"  False positive rate: {false_positive_rate_rcps*100:.1f}%\")\n", "            print(f\"  Avg confidence on negatives: {avg_confidence_fp_rcps:.3f}\")\n", "            \n", "            # Interpretation\n", "            if false_positive_rate_rcps <= 0.1:  # ≤10% false positives\n", "                fp_status_rcps = \"✅ EXCELLENT - Low false positives across sites\"\n", "            elif false_positive_rate_rcps <= 0.2:  # ≤20% false positives\n", "                fp_status_rcps = \"✅ GOOD - Acceptable false positives across sites\"\n", "            elif false_positive_rate_rcps <= 0.4:  # ≤40% false positives\n", "                fp_status_rcps = \"⚠️ MODERATE - Some false positives across sites\"\n", "            else:\n", "                fp_status_rcps = \"❌ POOR - Too many false positives across sites\"\n", "            \n", "            print(f\"  {fp_status_rcps}\")\n", "            \n", "            print(f\"\\n🎯 COMPLETE GENERALIZATION VALIDATION:\")\n", "            print(f\"  Training site: {RES_SITE_NAME}\")\n", "            print(f\"  Test site: {RCPS_SITE_NAME}\")\n", "            print(f\"  Pile detection rate: {rcps_detection_rate*100:.1f}%\")\n", "            print(f\"  False positive rate: {false_positive_rate_rcps*100:.1f}%\")\n", "            print(f\"  Cross-site performance: {fp_status_rcps}\")\n", "            \n", "            # Overall assessment\n", "            if rcps_detection_rate >= 0.9 and false_positive_rate_rcps <= 0.2:\n", "                overall_status = \"🎉 EXCELLENT GENERALIZATION - Ready for deployment\"\n", "            elif rcps_detection_rate >= 0.8 and false_positive_rate_rcps <= 0.3:\n", "                overall_status = \"✅ GOOD GENERALIZATION - Minor tuning may help\"\n", "            else:\n", "                overall_status = \"⚠️ GENERALIZATION ISSUES - Needs investigation\"\n", "            \n", "            print(f\"\\n🏆 FINAL ASSESSMENT: {overall_status}\")\n", "            \n", "            # Add to CSV for QGIS (negative test points)\n", "            if len(test_negative_locations) > 0:\n", "                # Create negative test results DataFrame\n", "                # Fix array length mismatch: use only valid patches\n", "                neg_test_df = pd.DataFrame({\n", "                    'pile_id': [f'neg_test_{i}' for i in range(len(test_neg_patches))],\n", "                    'utm_x': test_negative_locations[:len(test_neg_patches), 0],\n", "                    'utm_y': test_negative_locations[:len(test_neg_patches), 1],\n", "                    'predicted_pile': y_pred_test_neg,\n", "                    'confidence': y_prob_test_neg,\n", "                    'training_site': RES_SITE_NAME,\n", "                    'test_site': RCPS_SITE_NAME,\n", "                    'detection_status': ['False_Positive' if pred == 1 else 'True_Negative' for pred in y_pred_test_neg],\n", "                    'generalization_type': 'negative_test'\n", "                })\n", "                \n", "                # Convert to geographic coordinates (only valid patches)\n", "                geometry_neg = [Point(xy) for xy in zip(test_negative_locations[:len(test_neg_patches), 0], test_negative_locations[:len(test_neg_patches), 1])]\n", "                gdf_neg = gpd.GeoDataFrame(neg_test_df, geometry=geometry_neg, crs='EPSG:32615')  # UTM Zone 15N\n", "                gdf_neg_wgs84 = gdf_neg.to_crs('EPSG:4326')\n", "                \n", "                neg_test_df['longitude'] = gdf_neg_wgs84.geometry.x\n", "                neg_test_df['latitude'] = gdf_neg_wgs84.geometry.y\n", "                \n", "                # Save negative test results CSV\n", "                neg_csv_file = output_dir / f\"rcps_negative_test_results_{timestamp}.csv\"\n", "                neg_test_df.to_csv(neg_csv_file, index=False)\n", "                \n", "                print(f\"\\n📍 QGIS Negative Test CSV: {neg_csv_file}\")\n", "                print(f\"   Use this to visualize false positive locations\")\n", "                print(f\"   False positives: {np.sum(y_pred_test_neg)} red points\")\n", "                print(f\"   True negatives: {len(y_pred_test_neg) - np.sum(y_pred_test_neg)} green points\")\n", "            \n", "        else:\n", "            print(f\"⚠️ Could not extract valid patches from RCPS test negative locations\")\n", "    else:\n", "        print(f\"⚠️ Could not generate sufficient RCPS test negative locations\")\n", "        \n", "else:\n", "    print(\"No model available for RCPS false positive testing\")"]}, {"cell_type": "code", "execution_count": null, "id": "f18deb8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RES points: 35,565,352\n", "RES pile locations: 368\n", "RES positive patches: 368\n", "Creating negative samples within point cloud coverage...\n", "RES negative patches: 357\n", "Note: Negatives may include pile-adjacent areas due to data filtering\n", "Training data: 725 samples\n", "Positive: 368.0, Negative: 357.0\n", "RES detection rate: 100.0%\n", "Model saved: output_runs/true_generalization/res_trained_model_20250807_221320.pkl\n", "RCPS points: 52,862,386\n", "RCPS pile locations: 1359\n", "RCPS patches: 1359\n", "RCPS detection rate: 100.0%\n", "Performance difference: +0.0 percentage points\n", "CROSS-SITE GENERALIZATION RESULTS:\n", "Training site: nortan_res (368 piles)\n", "Testing site: althea_rcps (1359 piles)\n", "Cross-site detection rate: 100.0%\n", "Performance difference: +0.0 percentage points\n", "Overall status: EXCELLENT CROSS-SITE GENERALIZATION\n", "Note: Point cloud data pre-filtered to pile buffer regions\n", "Results saved: output_runs/true_generalization/true_generalization_results_20250807_221320.json\n", "QGIS CSV exported: output_runs/true_generalization/rcps_generalization_results_20250807_221320.csv\n", "Cross-site generalization test complete\n"]}], "source": ["# %% [markdown]\n", "# # True Model Generalization Test\n", "# \n", "# Train model on RES data, test on RCPS data without retraining.\n", "# \n", "# **Author**: <PERSON><PERSON><PERSON>  \n", "# **Date**: August 2025\n", "\n", "# %% [markdown]\n", "# ## Configuration\n", "\n", "# %%\n", "# Parameters\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# Data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH = \"../../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "RCPS_POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH = \"../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# Patch parameters\n", "PATCH_RADIUS = 3.0\n", "MIN_POINTS = 20\n", "TARGET_PATCH_SIZE = 64\n", "\n", "# %% [markdown]\n", "# ## Imports\n", "\n", "# %%\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import joblib\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# %% [markdown]\n", "# ## Utility Functions\n", "\n", "# %%\n", "def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):\n", "    \"\"\"Extract patches around locations\"\"\"\n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    \n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "            \n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "            \n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "    \n", "    return patches, np.array(valid_locs)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Extract 22 features from patches\"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        radial_dist = np.sqrt(x**2 + y**2)\n", "        height_above_min = z - np.min(z)\n", "        \n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch),\n", "            np.std(x) / (np.std(y) + 1e-6),\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),\n", "            np.percentile(radial_dist, 90),\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),\n", "        ]\n", "        features.append(feature_vector)\n", "    \n", "    return np.array(features)\n", "\n", "def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'\n", "    )\n", "    \n", "    # Reproject to appropriate UTM zone\n", "    if 'nortan' in str(kml_path):\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "    \n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    return pile_locations_utm\n", "\n", "# %% [markdown]\n", "# ## Step 1: Train Model on RES Data\n", "\n", "# %%\n", "# Load RES point cloud\n", "res_las = laspy.read(RES_POINT_CLOUD_PATH)\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"RES points: {len(res_points):,}\")\n", "\n", "# Load RES pile locations\n", "res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)\n", "print(f\"RES pile locations: {len(res_pile_locations)}\")\n", "\n", "# Extract positive patches\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations)\n", "print(f\"RES positive patches: {len(res_pos_patches)}\")\n", "\n", "# %%\n", "# Create negative samples using random sampling within point cloud bounds\n", "print(\"Creating negative samples within point cloud coverage...\")\n", "\n", "np.random.seed(42)\n", "x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()\n", "y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()\n", "\n", "n_negative = len(res_pos_patches)\n", "random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)\n", "random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)\n", "random_locations = np.column_stack([random_x, random_y])\n", "\n", "res_neg_patches, _ = extract_patches(res_points, random_locations)\n", "print(f\"RES negative patches: {len(res_neg_patches)}\")\n", "print(\"Note: Negatives may include pile-adjacent areas due to data filtering\")\n", "\n", "# %%\n", "# Prepare training data\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = extract_features(res_neg_patches)\n", "\n", "X_train_res = np.vstack([X_pos_res, X_neg_res])\n", "y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])\n", "\n", "print(f\"Training data: {len(X_train_res)} samples\")\n", "print(f\"Positive: {np.sum(y_train_res)}, Negative: {len(y_train_res) - np.sum(y_train_res)}\")\n", "\n", "# %%\n", "# Train model\n", "res_model = GradientBoostingClassifier(\n", "    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42\n", ")\n", "res_model.fit(X_train_res, y_train_res)\n", "\n", "# Sanity check on RES\n", "y_pred_res = res_model.predict(X_pos_res)\n", "y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]\n", "res_detection_rate = np.mean(y_pred_res)\n", "\n", "print(f\"RES detection rate: {res_detection_rate*100:.1f}%\")\n", "\n", "# Save model\n", "model_file = output_dir / f\"res_trained_model_{timestamp}.pkl\"\n", "joblib.dump(res_model, model_file)\n", "print(f\"Model saved: {model_file}\")\n", "\n", "# %% [markdown]\n", "# ## Step 2: Test on RCPS Data (No Retraining)\n", "\n", "# %%\n", "# Load RCPS data\n", "rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"RCPS points: {len(rcps_points):,}\")\n", "\n", "rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)\n", "print(f\"RCPS pile locations: {len(rcps_pile_locations)}\")\n", "\n", "# %%\n", "# Extract RCPS patches\n", "rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)\n", "print(f\"RCPS patches: {len(rcps_patches)}\")\n", "\n", "# Extract features\n", "X_rcps = extract_features(rcps_patches)\n", "\n", "# Apply trained model (no retraining)\n", "y_pred_rcps = res_model.predict(X_rcps)\n", "y_prob_rcps = res_model.predict_proba(X_rcps)[:, 1]\n", "\n", "rcps_detection_rate = np.mean(y_pred_rcps)\n", "performance_diff = rcps_detection_rate - res_detection_rate\n", "\n", "print(f\"RCPS detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "\n", "\n", "# %% [markdown]\n", "# ## Results and Export\n", "\n", "# %%\n", "# Results summary\n", "print(\"CROSS-SITE GENERALIZATION RESULTS:\")\n", "print(f\"Training site: {RES_SITE_NAME} ({len(res_pile_locations)} piles)\")\n", "print(f\"Testing site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)\")\n", "print(f\"Cross-site detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "# Overall generalization assessment\n", "if rcps_detection_rate >= 0.9:\n", "    if abs(performance_diff) <= 0.1:\n", "        status = \"EXCELLENT CROSS-SITE GENERALIZATION\"\n", "    else:\n", "        status = \"GOOD CROSS-SITE GENERALIZATION\"\n", "elif rcps_detection_rate >= 0.7:\n", "    status = \"MODERATE CROSS-SITE GENERALIZATION\"\n", "else:\n", "    status = \"POOR CROSS-SITE GENERALIZATION\"\n", "\n", "print(f\"Overall status: {status}\")\n", "print(\"Note: Point cloud data pre-filtered to pile buffer regions\")\n", "\n", "# %%\n", "# Save results\n", "results = {\n", "    'experiment_info': {\n", "        'type': 'true_model_generalization',\n", "        'training_site': RES_SITE_NAME,\n", "        'testing_site': RCPS_SITE_NAME,\n", "        'timestamp': timestamp,\n", "        'model_file': str(model_file)\n", "    },\n", "    'performance_metrics': {\n", "        'res_detection_rate': float(res_detection_rate),\n", "        'rcps_detection_rate': float(rcps_detection_rate),\n", "        'performance_difference': float(performance_diff),\n", "        'generalization_status': status\n", "    },\n", "    'data_info': {\n", "        'res_pile_locations': len(res_pile_locations),\n", "        'rcps_pile_locations': len(rcps_pile_locations),\n", "        'patch_radius': PATCH_RADIUS,\n", "        'target_patch_size': TARGET_PATCH_SIZE,\n", "        'data_scope': 'pile_buffer_regions_only'\n", "    }\n", "}\n", "\n", "results_file = output_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"Results saved: {results_file}\")\n", "\n", "# %%\n", "# Export for QGIS visualization\n", "rcps_results_df = pd.DataFrame({\n", "    'pile_id': range(len(rcps_pile_locations)),\n", "    'utm_x': rcps_pile_locations[:, 0],\n", "    'utm_y': rcps_pile_locations[:, 1], \n", "    'predicted_pile': y_pred_rcps,\n", "    'confidence': y_prob_rcps,\n", "    'training_site': RES_SITE_NAME,\n", "    'test_site': RCPS_SITE_NAME,\n", "    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],\n", "    'generalization_type': 'cross_site_pile_detection'\n", "})\n", "\n", "# Convert to geographic coordinates\n", "from shapely.geometry import Point\n", "geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]\n", "gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')\n", "gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "\n", "rcps_results_df['longitude'] = gdf_wgs84.geometry.x\n", "rcps_results_df['latitude'] = gdf_wgs84.geometry.y\n", "\n", "csv_file = output_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "rcps_results_df.to_csv(csv_file, index=False)\n", "\n", "print(f\"QGIS CSV exported: {csv_file}\")\n", "print(\"Cross-site generalization test complete\")"]}, {"cell_type": "code", "execution_count": 7, "id": "b0490bd5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 8\n", "drwxr-xr-x@ 4 <USER>  <GROUP>   128B Jul 28 15:56 \u001b[34mcad\u001b[m\u001b[m\n", "drwxr-xr-x@ 5 <USER>  <GROUP>   160B Jul 29 15:20 \u001b[34mconverted_dxf\u001b[m\u001b[m\n", "drwxr-xr-x@ 4 <USER>  <GROUP>   128B Jul 29 14:24 \u001b[34mgeo<PERSON>son_outputs\u001b[m\u001b[m\n", "drwxr-xr-x  4 <USER>  <GROUP>   128B Aug  7 22:04 \u001b[34mkml\u001b[m\u001b[m\n", "drwxr-xr-x@ 2 <USER>  <GROUP>    64B Jun 26 14:05 \u001b[34mortho\u001b[m\u001b[m\n", "drwxr-xr-x@ 3 <USER>  <GROUP>    96B Jul 25 14:13 \u001b[34mpointcloud\u001b[m\u001b[m\n", "-rw-r--r--@ 1 <USER>  <GROUP>   343B Jul 29 15:25 site_coordinate_config.json\n"]}], "source": ["!ls -lh ../../../../data/raw/nortan_res"]}, {"cell_type": "code", "execution_count": 12, "id": "6000c9e8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[RES] points: 35,565,352\n", "[RES] pile locations: 368  CRS=EPSG:32614\n", "[RES] positive patches: 368\n", "[RES] strategic negatives loaded (locations): 183\n", "[RES] negatives from strategic_csv: +100 patches\n", "[RES] WARNING: only 100 negative patches available; trimming positives to match.\n", "[RES] using balanced patches → pos=100, neg=100\n", "[RES] train samples: 200 | features: 22\n", "[RES] tuned threshold = 0.20 | P=1.00 R=1.00 F1=1.00 Acc=1.00\n", "[RES] model saved → output_runs/true_generalization/res_trained_gb_20250815_121806.pkl\n", "[RCPS] points: 52,862,386\n", "[RCPS] pile locations: 1359  CRS=EPSG:32615\n", "[RCPS] patches extracted: 1359\n", "[RCPS] prob stats → min=1.000, p50=1.000, max=1.000, thr=0.20\n", "[RCPS] detection rate: 100.0% | Δ vs RES: +0.0 pp\n", "[RCPS] precision probe with off-pile negatives (dense sampler)...\n", "[RCPS] precision probe skipped (dense sampler produced no valid negatives). Consider lowering MIN_POINTS or raising PATCH_RADIUS slightly.\n", "[OK] results saved → output_runs/true_generalization/true_generalization_results_20250815_121806.json\n", "[OK] QGIS CSV (with lon/lat) → output_runs/true_generalization/rcps_generalization_results_20250815_121806.csv\n", "[OK] GeoJSON → output_runs/true_generalization/rcps_generalization_results_20250815_121806.geojson\n", "Cross-site generalization test complete.\n"]}], "source": ["# %% [markdown]\n", "# # True Model Generalization Test (RES → RCPS, no retraining)\n", "# Train on RES, select decision threshold on RES, and test as-is on RCPS.\n", "#\n", "# Author: <PERSON><PERSON><PERSON>\n", "# Date: August 2025\n", "\n", "# %% [markdown]\n", "# ## Configuration\n", "\n", "# %%\n", "from __future__ import annotations\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Site names\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# Data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH = \"../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "RCPS_POINT_CLOUD_PATH = \"../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH = \"../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "# Optional: strategic negatives for RES (UTM X/Y columns: utm_x, utm_y)\n", "RES_STRATEGIC_NEG_CSV = \"../../../../data/ground_truth/nortan_res_negative_samples.csv\"\n", "\n", "# Output\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# Patch & sampling\n", "PATCH_RADIUS = 3.0      # meters\n", "MIN_POINTS   = 20\n", "TARGET_PATCH_SIZE = 64\n", "NEGATIVE_EXCLUSION_RADIUS = 6.0  # don't draw negatives within this 2D distance of any pile\n", "\n", "# Model & eval\n", "GB_PARAMS = dict(n_estimators=150, max_depth=6, learning_rate=0.08, random_state=42)\n", "THRESH_GRID = [round(x, 2) for x in list(__import__(\"numpy\").linspace(0.2, 0.9, 36))]  # for max-F1\n", "\n", "# Logging\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "out_dir = Path(OUTPUT_DIR); out_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# %% [markdown]\n", "# ## Imports\n", "\n", "# %%\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import joblib\n", "import laspy\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.metrics import precision_recall_fscore_support, accuracy_score\n", "\n", "# %% [markdown]\n", "# ## Utilities\n", "\n", "# %%\n", "def rng_for(i: int, base_seed: int = 42):\n", "    \"\"\"Deterministic RNG per patch index.\"\"\"\n", "    return np.random.default_rng(base_seed + int(i))\n", "\n", "def utm_epsg_from_lonlat(lon: float, lat: float) -> str:\n", "    \"\"\"Compute UTM EPSG code from lon/lat (WGS84).\"\"\"\n", "    zone = int(np.floor((lon + 180) / 6) + 1)\n", "    north = lat >= 0\n", "    return f\"EPSG:{326 if north else 327}{zone:02d}\"\n", "\n", "def load_and_reproject_kml_to_utm_points(kml_path: str | Path) -> tuple[np.ndarray, str]:\n", "    \"\"\"Load KML geometries, take centroids for polygons, and reproject to UTM.\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    pts_ll = []\n", "    for geom in gdf.geometry:\n", "        if geom is None:\n", "            continue\n", "        if geom.geom_type == \"Point\":\n", "            pts_ll.append([geom.x, geom.y])\n", "        elif geom.geom_type in (\"Polygon\", \"MultiPolygon\", \"LineString\", \"MultiLineString\"):\n", "            centroid = geom.centroid\n", "            pts_ll.append([centroid.x, centroid.y])\n", "    if not pts_ll:\n", "        return np.empty((0, 2), dtype=float), \"EPSG:4326\"\n", "\n", "    pts_ll = np.asarray(pts_ll, dtype=float)\n", "    mean_lon, mean_lat = float(np.mean(pts_ll[:,0])), float(np.mean(pts_ll[:,1]))\n", "    utm_epsg = utm_epsg_from_lonlat(mean_lon, mean_lat)\n", "\n", "    gdf_ll = gpd.GeoDataFrame(geometry=gpd.points_from_xy(pts_ll[:,0], pts_ll[:,1]), crs=\"EPSG:4326\")\n", "    gdf_utm = gdf_ll.to_crs(utm_epsg)\n", "    pts_utm = np.array([[p.x, p.y] for p in gdf_utm.geometry], dtype=float)\n", "    return pts_utm, utm_epsg\n", "\n", "def read_las_points_xyz(las_path: str | Path) -> np.ndarray:\n", "    \"\"\"Read LAS/LAZ and return Nx3 float32 array [x,y,z].\"\"\"\n", "    las = laspy.read(las_path)\n", "    pts = np.vstack([las.x, las.y, las.z]).T.astype(np.float32, copy=False)\n", "    return pts\n", "\n", "def subsample_patch(patch_points: np.ndarray, target_size: int, idx_seed: int) -> np.ndarray:\n", "    \"\"\"Deterministic subsample to target_size using per-index RNG.\"\"\"\n", "    n = len(patch_points)\n", "    if n <= target_size:\n", "        return patch_points\n", "    rs = rng_for(idx_seed)\n", "    idx = rs.choice(n, size=target_size, replace=False)\n", "    return patch_points[idx]\n", "\n", "def extract_patches(points_xyz: np.ndarray,\n", "                    locations_xy: np.n<PERSON><PERSON>,\n", "                    radius: float,\n", "                    min_points: int,\n", "                    target_size: int) -> tuple[list[np.ndarray], np.ndarray]:\n", "    \"\"\"Extract centered patches around each location.\"\"\"\n", "    if len(points_xyz) == 0 or len(locations_xy) == 0:\n", "        return [], np.empty((0,2), dtype=float)\n", "    tree = cKDTree(points_xyz[:, :2])\n", "    patches, kept_locs = [], []\n", "    for i, loc in enumerate(locations_xy):\n", "        idxs = tree.query_ball_point(loc, radius)\n", "        if len(idxs) >= min_points:\n", "            patch = points_xyz[np.asarray(idxs)]\n", "            center = np.array([loc[0], loc[1], float(np.mean(patch[:,2]))], dtype=np.float32)\n", "            patch_c = patch - center\n", "            patch_c = subsample_patch(patch_c, target_size, idx_seed=i)\n", "            patches.append(patch_c.astype(np.float32, copy=False))\n", "            kept_locs.append(loc)\n", "    return patches, np.asarray(kept_locs, dtype=float)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Return EXACTLY 22 features per patch (thesis baseline).\"\"\"\n", "    feats, eps = [], 1e-6\n", "    for patch in patches:\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        r = np.sqrt(x**2 + y**2)\n", "        zmin = np.min(z)\n", "        h = z - zmin  # height above local min\n", "        feats.append([\n", "            # Basic spatial (9)\n", "            float(np.mean(x)), float(np.std(x)), float(np.ptp(x)),\n", "            float(np.mean(y)), float(np.std(y)), float(np.ptp(y)),\n", "            float(np.mean(z)), float(np.std(z)), float(np.ptp(z)),\n", "            # Height-based (4)\n", "            float(np.mean(h)), float(np.std(h)),\n", "            float(np.percentile(h, 75)), float(np.percentile(h, 25)),\n", "            # Radial distance (4)\n", "            float(np.mean(r)), float(np.std(r)), float(np.min(r)), float(np.max(r)),\n", "            # Shape/density (5)\n", "            float(len(patch)),\n", "            float(np.std(x) / (np.std(y) + eps)),\n", "            float(np.std(z) / (np.std(x) + np.std(y) + eps)),\n", "            float(np.percentile(r, 90)),\n", "            float(np.mean(h > np.mean(h))),\n", "        ])\n", "    X = np.asarray(feats, dtype=np.float32)\n", "    if X.size and X.shape[1] != 22:\n", "        raise RuntimeError(f\"Feature drift: expected 22, got {X.shape[1]}\")\n", "    return X\n", "\n", "# --- NEW: density-aware negative sampler (jitter real points, enforce min neighbor count) ---\n", "def sample_negatives_from_points(points_xy: np.n<PERSON><PERSON>,\n", "                                 n_needed: int,\n", "                                 exclude_xy: np.n<PERSON><PERSON>,\n", "                                 exclusion_radius: float,\n", "                                 radius: float,\n", "                                 min_points: int,\n", "                                 seed: int = 42,\n", "                                 jitter_sigma: float = 0.40,\n", "                                 max_tries: int = 150000) -> np.ndarray:\n", "    \"\"\"\n", "    Sample negatives from high-density areas by picking existing point locations and jittering.\n", "    Ensures:\n", "      - far from exclude_xy by exclusion_radius\n", "      - at least min_points neighbors within radius (so patches won't be empty)\n", "    \"\"\"\n", "    rs = np.random.default_rng(seed)\n", "    tree_pts = cKDTree(points_xy)\n", "    tree_ex = cKDTree(exclude_xy) if len(exclude_xy) else None\n", "\n", "    accepted = []\n", "    tries = 0\n", "    N = len(points_xy)\n", "    if N == 0:\n", "        return np.empty((0,2), dtype=float)\n", "\n", "    # Adaptive jitter growth if environment is tight\n", "    sigma = float(jitter_sigma)\n", "\n", "    while len(accepted) < n_needed and tries < max_tries:\n", "        base = points_xy[rs.integers(0, N)]\n", "        cand = base + rs.normal(0.0, sigma, size=2)\n", "\n", "        # exclude near piles\n", "        if tree_ex is not None:\n", "            d, _ = tree_ex.query(cand, k=1)\n", "            if d <= exclusion_radius:\n", "                tries += 1\n", "                if tries % 20000 == 0:\n", "                    sigma *= 1.4\n", "                continue\n", "\n", "        # ensure density\n", "        cnt = len(tree_pts.query_ball_point(cand, r=radius))\n", "        if cnt >= min_points:\n", "            accepted.append(cand)\n", "\n", "        tries += 1\n", "        if tries % 20000 == 0:\n", "            sigma *= 1.4  # gradually widen search\n", "\n", "    return np.asarray(accepted[:n_needed], dtype=float)\n", "\n", "# %% [markdown]\n", "# ## Step 1: Train on RES (+ threshold selection)\n", "\n", "# %%\n", "# Load points & piles (RES)\n", "res_pts = read_las_points_xyz(RES_POINT_CLOUD_PATH)\n", "print(f\"[RES] points: {len(res_pts):,}\")\n", "\n", "res_piles_xy, res_epsg = load_and_reproject_kml_to_utm_points(RES_BUFFER_KML_PATH)\n", "print(f\"[RES] pile locations: {len(res_piles_xy)}  CRS={res_epsg}\")\n", "\n", "# Positive patches\n", "res_pos_patches, res_pos_locs = extract_patches(res_pts, res_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)\n", "print(f\"[RES] positive patches: {len(res_pos_patches)}\")\n", "if len(res_pos_patches) == 0:\n", "    raise RuntimeError(\"No positive patches on RES; check KML / radius / min_points.\")\n", "n_pos_full = len(res_pos_patches)\n", "\n", "# Strategic negatives (preferred)\n", "neg_from_csv = None\n", "try:\n", "    df_neg = pd.read_csv(RES_STRATEGIC_NEG_CSV)\n", "    if {\"utm_x\",\"utm_y\"}.issubset(df_neg.columns):\n", "        neg_from_csv = df_neg[[\"utm_x\",\"utm_y\"]].to_numpy(dtype=float)\n", "        print(f\"[RES] strategic negatives loaded (locations): {len(neg_from_csv)}\")\n", "    else:\n", "        print(\"[RES] strategic negatives CSV missing columns 'utm_x','utm_y' → ignored\")\n", "except FileNotFoundError:\n", "    print(\"[RES] strategic negatives CSV not found → skipping\")\n", "\n", "# Build negative patches to MATCH all positives (top-up with density-aware sampling)\n", "res_neg_patches, res_neg_locs = [], []\n", "\n", "def _append_neg_from_xy(xy: np.n<PERSON><PERSON>, label: str):\n", "    if xy is None or len(xy) == 0:\n", "        return 0\n", "    patches, locs = extract_patches(res_pts, xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)\n", "    if patches:\n", "        res_neg_patches.extend(patches); res_neg_locs.extend(locs)\n", "    print(f\"[RES] negatives from {label}: +{len(patches)} patches\")\n", "    return len(patches)\n", "\n", "# 1) strategic negatives first\n", "_ = _append_neg_from_xy(neg_from_csv, \"strategic_csv\")\n", "\n", "# 2) density-aware top-up\n", "need = n_pos_full - len(res_neg_patches)\n", "if need > 0:\n", "    res_neg_xy_dense = sample_negatives_from_points(\n", "        points_xy=res_pts[:, :2],\n", "        n_needed=need,\n", "        exclude_xy=res_piles_xy,\n", "        exclusion_radius=NEGATIVE_EXCLUSION_RADIUS,\n", "        radius=PATCH_RADIUS,\n", "        min_points=MIN_POINTS,\n", "        seed=42,\n", "        jitter_sigma=0.40,\n", "    )\n", "    _ = _append_neg_from_xy(res_neg_xy_dense, \"dense_sampler\")\n", "\n", "# 3) finalize balance\n", "if len(res_neg_patches) < n_pos_full:\n", "    print(f\"[RES] WARNING: only {len(res_neg_patches)} negative patches available; trimming positives to match.\")\n", "n_use = min(n_pos_full, len(res_neg_patches))\n", "res_pos_patches = res_pos_patches[:n_use]\n", "res_neg_patches = res_neg_patches[:n_use]\n", "print(f\"[RES] using balanced patches → pos={len(res_pos_patches)}, neg={len(res_neg_patches)}\")\n", "\n", "# Features & labels (22 features)\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = extract_features(res_neg_patches)\n", "assert X_pos_res.shape[1] == 22 and X_neg_res.shape[1] == 22, \"Feature drift: expected 22\"\n", "X_train_res = np.vstack([X_pos_res, X_neg_res]).astype(np.float32)\n", "y_train_res = np.hstack([np.ones(len(X_pos_res), dtype=int), np.zeros(len(X_neg_res), dtype=int)])\n", "print(f\"[RES] train samples: {len(X_train_res)} | features: {X_train_res.shape[1]}\")\n", "\n", "# Train model\n", "model = GradientBoostingClassifier(**GB_PARAMS)\n", "model.fit(X_train_res, y_train_res)\n", "\n", "# Threshold tuning on RES\n", "y_prob_res = model.predict_proba(X_train_res)[:,1]\n", "best = tune_threshold(y_train_res, y_prob_res, THRESH_GRID)\n", "print(f\"[RES] tuned threshold = {best['threshold']:.2f} | \"\n", "      f\"P={best['precision']:.2f} R={best['recall']:.2f} F1={best['f1']:.2f} Acc={best['accuracy']:.2f}\")\n", "\n", "# Save model & importances\n", "model_path = out_dir / f\"res_trained_gb_{timestamp}.pkl\"\n", "joblib.dump(model, model_path)\n", "fi = getattr(model, \"feature_importances_\", None)\n", "if fi is not None:\n", "    pd.DataFrame({\"feature_index\": np.arange(len(fi)), \"importance\": fi}) \\\n", "      .sort_values(\"importance\", ascending=False) \\\n", "      .to_csv(out_dir / f\"feature_importances_{timestamp}.csv\", index=False)\n", "print(f\"[RES] model saved → {model_path}\")\n", "\n", "# %% [markdown]\n", "# ## Step 2: Test on RCPS (no retraining)\n", "\n", "# %%\n", "rcps_pts = read_las_points_xyz(RCPS_POINT_CLOUD_PATH)\n", "print(f\"[RCPS] points: {len(rcps_pts):,}\")\n", "\n", "rcps_piles_xy, rcps_epsg = load_and_reproject_kml_to_utm_points(RCPS_BUFFER_KML_PATH)\n", "print(f\"[RCPS] pile locations: {len(rcps_piles_xy)}  CRS={rcps_epsg}\")\n", "\n", "rcps_patches, rcps_kept_xy = extract_patches(rcps_pts, rcps_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)\n", "print(f\"[RCPS] patches extracted: {len(rcps_patches)}\")\n", "\n", "if len(rcps_patches) == 0:\n", "    print(\"[RCPS] No patches extracted — check CRS/radius/min_points.\")\n", "    X_rcps = np.empty((0, X_train_res.shape[1]), dtype=np.float32)\n", "else:\n", "    X_rcps = extract_features(rcps_patches)\n", "    assert X_rcps.shape[1] == 22, \"Feature drift: expected 22\"\n", "\n", "y_prob_rcps = model.predict_proba(X_rcps)[:,1] if len(X_rcps) else np.array([])\n", "y_pred_rcps = (y_prob_rcps >= best[\"threshold\"]).astype(int) if len(y_prob_rcps) else np.array([])\n", "\n", "# Recall on RCPS (since inputs are piles)\n", "rcps_detection_rate = float(np.mean(y_pred_rcps)) if len(y_pred_rcps) else 0.0\n", "\n", "# RES positive recall at tuned threshold:\n", "res_pos_prob = model.predict_proba(X_pos_res)[:,1]\n", "res_detection_rate = float(np.mean(res_pos_prob >= best[\"threshold\"]))\n", "perf_diff = rcps_detection_rate - res_detection_rate\n", "\n", "# Probability stats (sanity)\n", "if len(y_prob_rcps):\n", "    print(f\"[RCPS] prob stats → min={y_prob_rcps.min():.3f}, \"\n", "          f\"p50={np.median(y_prob_rcps):.3f}, max={y_prob_rcps.max():.3f}, \"\n", "          f\"thr={best['threshold']:.2f}\")\n", "\n", "print(f\"[RCPS] detection rate: {rcps_detection_rate*100:.1f}% | Δ vs RES: {perf_diff*100:+.1f} pp\")\n", "\n", "# --- RCPS precision probe: negatives from dense sampler ---\n", "precision = None; fpr = None; rcps_negatives_count = 0\n", "print(\"[RCPS] precision probe with off-pile negatives (dense sampler)...\")\n", "\n", "rcps_neg_xy = sample_negatives_from_points(\n", "    points_xy=rcps_pts[:, :2],\n", "    n_needed=len(rcps_kept_xy),\n", "    exclude_xy=rcps_piles_xy,\n", "    exclusion_radius=NEGATIVE_EXCLUSION_RADIUS,\n", "    radius=PATCH_RADIUS,\n", "    min_points=MIN_POINTS,\n", "    seed=123,\n", "    jitter_sigma=0.40,\n", ")\n", "rcps_neg_patches, _ = extract_patches(rcps_pts, rcps_neg_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)\n", "X_rcps_neg = extract_features(rcps_neg_patches) if len(rcps_neg_patches) else np.empty((0,22), dtype=np.float32)\n", "\n", "if len(X_rcps_neg):\n", "    y_prob_neg = model.predict_proba(X_rcps_neg)[:,1]\n", "    y_pred_neg = (y_prob_neg >= best[\"threshold\"]).astype(int)\n", "    fp = int(np.sum(y_pred_neg == 1))\n", "    tn = int(np.sum(y_pred_neg == 0))\n", "    rcps_negatives_count = int(len(X_rcps_neg))\n", "    precision = len(y_pred_rcps) / (len(y_pred_rcps) + fp) if (len[y_pred_rcps] if hasattr(y_pred_rcps, '__len__') else 0) + fp > 0 else 0.0\n", "    # safer precision calc without fancy checks:\n", "    precision = float(len(y_pred_rcps) / (len(y_pred_rcps) + fp)) if (len(y_pred_rcps)+fp) > 0 else 0.0\n", "    fpr = fp / (fp + tn) if (fp+tn) > 0 else 0.0\n", "    print(f\"[RCPS] negatives probed: {rcps_negatives_count} | FP={fp}, TN={tn} | \"\n", "          f\"Precision≈{precision:.3f} | FPR={fpr:.3f}\")\n", "\n", "    pd.DataFrame({\n", "        \"utm_x\": rcps_neg_xy[:len(y_pred_neg),0],\n", "        \"utm_y\": rcps_neg_xy[:len(y_pred_neg),1],\n", "        \"pred\": y_pred_neg,\n", "        \"prob\": y_prob_neg\n", "    }).to_csv(out_dir / f\"rcps_precision_probe_{timestamp}.csv\", index=False)\n", "    print(f\"[OK] RCPS precision probe CSV → {out_dir / f'rcps_precision_probe_{timestamp}.csv'}\")\n", "else:\n", "    print(\"[RCPS] precision probe skipped (dense sampler produced no valid negatives). Consider lowering MIN_POINTS or raising PATCH_RADIUS slightly.\")\n", "\n", "# %% [markdown]\n", "# ## Results + Exports\n", "\n", "# %%\n", "# Status gate: require both high recall (on piles) and high precision (off-pile probe)\n", "precision_ok = (precision is not None) and (precision >= 0.90)\n", "recall_ok    = (rcps_detection_rate >= 0.90)\n", "\n", "if precision_ok and recall_ok:\n", "    status = \"EXCELLENT CROSS-SITE GENERALIZATION\"\n", "elif (rcps_detection_rate >= 0.70) and (precision is not None) and (precision >= 0.70):\n", "    status = \"MODERATE CROSS-SITE GENERALIZATION\"\n", "else:\n", "    status = \"POOR CROSS-SITE GENERALIZATION\"\n", "\n", "summary = {\n", "    \"experiment_info\": {\n", "        \"type\": \"true_model_generalization\",\n", "        \"training_site\": RES_SITE_NAME,\n", "        \"testing_site\": RCPS_SITE_NAME,\n", "        \"timestamp\": timestamp,\n", "        \"model_file\": str(model_path),\n", "        \"features\": int(X_train_res.shape[1]),\n", "        \"threshold\": float(best[\"threshold\"]),\n", "        \"epsg_res\": res_epsg,\n", "        \"epsg_rcps\": rcps_epsg\n", "    },\n", "    \"performance_metrics\": {\n", "        \"res_precision\": float(best[\"precision\"]),\n", "        \"res_recall\": float(best[\"recall\"]),\n", "        \"res_f1\": float(best[\"f1\"]),\n", "        \"res_accuracy\": float(best[\"accuracy\"]),\n", "        \"res_detection_rate_on_positives\": float(res_detection_rate),\n", "        \"rcps_detection_rate\": float(rcps_detection_rate),\n", "        \"rcps_precision_probe_precision\": (float(precision) if precision is not None else None),\n", "        \"rcps_precision_probe_fpr\": (float(fpr) if fpr is not None else None),\n", "        \"rcps_prob_min\": (float(y_prob_rcps.min()) if len(y_prob_rcps) else None),\n", "        \"rcps_prob_p50\": (float(np.median(y_prob_rcps)) if len(y_prob_rcps) else None),\n", "        \"rcps_prob_max\": (float(y_prob_rcps.max()) if len(y_prob_rcps) else None),\n", "        \"performance_difference\": float(perf_diff),\n", "        \"generalization_status\": status\n", "    },\n", "    \"data_info\": {\n", "        \"res_pile_locations\": int(len(res_piles_xy)),\n", "        \"rcps_pile_locations\": int(len(rcps_piles_xy)),\n", "        \"train_pos_used\": int(len(res_pos_patches)),\n", "        \"train_neg_used\": int(len(res_neg_patches)),\n", "        \"patch_radius\": float(PATCH_RADIUS),\n", "        \"target_patch_size\": int(TARGET_PATCH_SIZE),\n", "        \"min_points\": int(MIN_POINTS),\n", "        \"negatives_source\": \"strategic+dense\" if (neg_from_csv is not None) else \"dense_only\",\n", "        \"negative_exclusion_radius\": float(NEGATIVE_EXCLUSION_RADIUS),\n", "        \"rcps_precision_probe_negatives\": int(rcps_negatives_count)\n", "    }\n", "}\n", "results_file = out_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, \"w\") as f:\n", "    json.dump(summary, f, indent=2)\n", "print(f\"[OK] results saved → {results_file}\")\n", "\n", "# Export RCPS predictions for QGIS\n", "if len(rcps_kept_xy):\n", "    gdf_rcps = gpd.GeoDataFrame(\n", "        {\n", "            \"pile_id\": np.arange(len(rcps_kept_xy)),\n", "            \"utm_x\": rcps_kept_xy[:,0],\n", "            \"utm_y\": rcps_kept_xy[:,1],\n", "            \"predicted_pile\": y_pred_rcps.astype(int),\n", "            \"confidence\": y_prob_rcps.astype(float),\n", "            \"training_site\": RES_SITE_NAME,\n", "            \"test_site\": RCPS_SITE_NAME,\n", "            \"detection_status\": np.where(y_pred_rcps==1, \"Detected\",\"Missed\"),\n", "            \"generalization_type\": \"cross_site_pile_detection\",\n", "        },\n", "        geometry=[Point(xy) for xy in rcps_kept_xy],\n", "        crs=rcps_epsg\n", "    )\n", "    gdf_wgs84 = gdf_rcps.to_crs(\"EPSG:4326\")\n", "    rcps_csv = out_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "    df_out = pd.DataFrame(gdf_rcps.drop(columns=\"geometry\"))\n", "    df_out[\"longitude\"] = gdf_wgs84.geometry.x\n", "    df_out[\"latitude\"]  = gdf_wgs84.geometry.y\n", "    df_out.to_csv(rcps_csv, index=False)\n", "    print(f\"[OK] QGIS CSV (with lon/lat) → {rcps_csv}\")\n", "\n", "    # Optional GeoJSON for direct mapping\n", "    geojson_path = out_dir / f\"rcps_generalization_results_{timestamp}.geojson\"\n", "    gdf_wgs84.to_file(geojson_path, driver=\"GeoJSON\")\n", "    print(f\"[OK] GeoJSON → {geojson_path}\")\n", "\n", "print(\"Cross-site generalization test complete.\")\n"]}, {"cell_type": "code", "execution_count": 15, "id": "61ab6ec5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[RES] points: 35,565,352\n", "[RES] pile locations: 368  CRS=EPSG:32614\n", "[RES] positive patches: 368\n", "[RES] strategic negatives loaded (locations): 183\n", "[RES] negatives from strategic_csv (usable after mask): 100\n", "[RES] negatives from within-buffer sampler: 268\n", "[RES] using balanced features → pos=368, neg=368\n", "[RES] train samples: 736 | features: 22\n", "[RES] tuned threshold = 0.20 | P=1.00 R=1.00 F1=1.00 Acc=1.00\n", "[PLOT] saved → output_runs/true_generalization/res_pr_curve_20250815_123336.png\n", "[PLOT] saved → output_runs/true_generalization/res_confusion_matrix_20250815_123336.png\n", "[PLOT] saved → output_runs/true_generalization/res_prob_hist_20250815_123336.png\n", "[RES] model saved → output_runs/true_generalization/res_trained_gb_20250815_123336.pkl\n", "[RCPS] points: 52,862,386\n", "[RCPS] pile locations: 1359  CRS=EPSG:32615\n", "[RCPS] patches extracted: 1359\n", "[RCPS] prob stats → min=0.382, p50=1.000, max=1.000, thr=0.20\n", "[RCPS] detection rate: 100.0% | Δ vs RES: +0.0 pp\n", "[PLOT] saved → output_runs/true_generalization/rcps_prob_hist_pos_20250815_123336.png\n", "[RCPS] precision probe with core-masked within-buffer negatives...\n", "[RCPS] negatives probed (capped): 400 | FP=0, TN=400 | Precision≈1.000 | FPR=0.000\n", "[PLOT] saved → output_runs/true_generalization/rcps_prob_hist_pos_vs_neg_20250815_123336.png\n", "[OK] RCPS precision probe CSV → output_runs/true_generalization/rcps_precision_probe_20250815_123336.csv\n", "[OK] results saved → output_runs/true_generalization/true_generalization_results_20250815_123336.json\n", "[RES] model saved → output_runs/true_generalization/res_trained_gb_20250815_123336.pkl\n", "[OK] QGIS CSV (with lon/lat) → output_runs/true_generalization/rcps_generalization_results_20250815_123336.csv\n", "Cross-site generalization test complete.\n"]}], "source": ["# %% [markdown]\n", "# # True Model Generalization Test (RES → RCPS, no retraining)\n", "# Train on RES, select decision threshold on RES, and test as-is on RCPS.\n", "#\n", "# Author: <PERSON><PERSON><PERSON>\n", "# Date: August 2025\n", "\n", "# %% [markdown]\n", "# ## Configuration\n", "\n", "# %%\n", "from __future__ import annotations\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "# Site names\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# Data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH  = \"../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "RCPS_POINT_CLOUD_PATH = \"../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH  = \"../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "# Optional: strategic negatives for RES (UTM X/Y columns: utm_x, utm_y)\n", "RES_STRATEGIC_NEG_CSV = \"../../../../data/ground_truth/nortan_res_negative_samples.csv\"\n", "\n", "# Output\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# === Speed & run flags ===\n", "SPEED_MODE = \"fast\"          # \"balanced\" or \"fast\"\n", "RUN_PRECISION_PROBE = True   # set False for routine runs\n", "PROBE_NEG_MAX = 400          # cap negatives for RCPS precision probe (lower to run faster)\n", "\n", "# Patch & sampling (defaults; overridden by preset below)\n", "PATCH_RADIUS = 3.0                # meters (same for pos/neg)\n", "MIN_POINTS   = 20                 # before masking\n", "MIN_POINTS_AFTER_MASK = 15        # after masking for negatives\n", "TARGET_PATCH_SIZE = 64\n", "CORE_EXCLUDE_RADIUS = 0.40        # meters: remove pile core points for negative features\n", "RING_INNER_MARGIN  = 0.60         # min offset from core when placing neg centers\n", "RING_OUTER_MARGIN  = 0.20         # keep center inside coverage radius by this margin\n", "MAX_PER_PILE = 2\n", "MAX_ANGLE_TRIALS = 30\n", "\n", "if SPEED_MODE == \"fast\":\n", "    MAX_PER_PILE = 1\n", "    MAX_ANGLE_TRIALS = 12\n", "    MIN_POINTS_AFTER_MASK = 12\n", "    TARGET_PATCH_SIZE = 48\n", "    CORE_EXCLUDE_RADIUS = 0.35\n", "    RING_INNER_MARGIN = 0.80\n", "    PROBE_NEG_MAX = min(PROBE_NEG_MAX, 400)\n", "\n", "# Model & eval\n", "GB_PARAMS = dict(n_estimators=150, max_depth=6, learning_rate=0.08, random_state=42)\n", "THRESH_GRID = [round(x, 2) for x in list(__import__(\"numpy\").linspace(0.2, 0.9, 28))]  # fewer steps for speed\n", "\n", "# Logging\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "out_dir = Path(OUTPUT_DIR); out_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Plots\n", "SAVE_PLOTS = True\n", "SHOW_PLOTS = False  # set True to pop up windows\n", "if SAVE_PLOTS and not SHOW_PLOTS:\n", "    import matplotlib\n", "    matplotlib.use(\"Agg\")  # safe headless backend\n", "\n", "# %% [markdown]\n", "# ## Imports\n", "\n", "# %%\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import joblib\n", "import laspy\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.metrics import (\n", "    precision_recall_fscore_support,\n", "    accuracy_score,\n", "    precision_recall_curve,\n", "    confusion_matrix,\n", ")\n", "import matplotlib.pyplot as plt\n", "\n", "# %% [markdown]\n", "# ## Utilities\n", "\n", "# %%\n", "def rng_for(i: int, base_seed: int = 42):\n", "    return np.random.default_rng(base_seed + int(i))\n", "\n", "def utm_epsg_from_lonlat(lon: float, lat: float) -> str:\n", "    zone = int(np.floor((lon + 180) / 6) + 1)\n", "    north = lat >= 0\n", "    return f\"EPSG:{326 if north else 327}{zone:02d}\"\n", "\n", "def load_and_reproject_kml_to_utm_points(kml_path: str | Path) -> tuple[np.ndarray, str]:\n", "    gdf = gpd.read_file(kml_path)\n", "    pts_ll = []\n", "    for geom in gdf.geometry:\n", "        if geom is None: continue\n", "        if geom.geom_type == \"Point\":\n", "            pts_ll.append([geom.x, geom.y])\n", "        elif geom.geom_type in (\"Polygon\",\"MultiPolygon\",\"LineString\",\"MultiLineString\"):\n", "            c = geom.centroid; pts_ll.append([c.x, c.y])\n", "    if not pts_ll:\n", "        return np.empty((0,2), float), \"EPSG:4326\"\n", "    pts_ll = np.asarray(pts_ll, float)\n", "    mean_lon, mean_lat = float(np.mean(pts_ll[:,0])), float(np.mean(pts_ll[:,1]))\n", "    utm = utm_epsg_from_lonlat(mean_lon, mean_lat)\n", "    gdf_ll = gpd.GeoDataFrame(geometry=gpd.points_from_xy(pts_ll[:,0], pts_ll[:,1]), crs=\"EPSG:4326\")\n", "    gdf_utm = gdf_ll.to_crs(utm)\n", "    pts_utm = np.array([[p.x, p.y] for p in gdf_utm.geometry], float)\n", "    return pts_utm, utm\n", "\n", "def read_las_points_xyz(las_path: str | Path) -> np.ndarray:\n", "    las = laspy.read(las_path)\n", "    return np.vstack([las.x, las.y, las.z]).T.astype(np.float32, copy=False)\n", "\n", "def subsample_patch(patch_points: np.ndarray, target_size: int, idx_seed: int) -> np.ndarray:\n", "    n = len(patch_points)\n", "    if n <= target_size: return patch_points\n", "    rs = rng_for(idx_seed)\n", "    idx = rs.choice(n, size=target_size, replace=False)\n", "    return patch_points[idx]\n", "\n", "def extract_patches(points_xyz: np.ndarray,\n", "                    locations_xy: np.n<PERSON><PERSON>,\n", "                    radius: float,\n", "                    min_points: int,\n", "                    target_size: int,\n", "                    tree_points_xy: cKDTree | None = None) -> tuple[list[np.ndarray], np.ndarray]:\n", "    if len(points_xyz) == 0 or len(locations_xy) == 0:\n", "        return [], np.empty((0,2), float)\n", "    tree = tree_points_xy or cKDTree(points_xyz[:, :2])\n", "    patches, kept_locs = [], []\n", "    for i, loc in enumerate(locations_xy):\n", "        idxs = tree.query_ball_point(loc, radius)\n", "        if len(idxs) >= min_points:\n", "            patch = points_xyz[np.asarray(idxs)]\n", "            center = np.array([loc[0], loc[1], float(np.mean(patch[:,2]))], np.float32)\n", "            patch_c = patch - center\n", "            patch_c = subsample_patch(patch_c, target_size, idx_seed=i)\n", "            patches.append(patch_c.astype(np.float32, copy=False))\n", "            kept_locs.append(loc)\n", "    return patches, np.asarray(kept_locs, float)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Exactly 22 features (thesis baseline).\"\"\"\n", "    feats, eps = [], 1e-6\n", "    for patch in patches:\n", "        x, y, z = patch[:,0], patch[:,1], patch[:,2]\n", "        r = np.sqrt(x**2 + y**2)\n", "        zmin = np.min(z); h = z - zmin\n", "        feats.append([\n", "            # Basic spatial (9)\n", "            float(np.mean(x)), float(np.std(x)), float(np.ptp(x)),\n", "            float(np.mean(y)), float(np.std(y)), float(np.ptp(y)),\n", "            float(np.mean(z)), float(np.std(z)), float(np.ptp(z)),\n", "            # Height (4)\n", "            float(np.mean(h)), float(np.std(h)),\n", "            float(np.percentile(h, 75)), float(np.percentile(h, 25)),\n", "            # Radial (4)\n", "            float(np.mean(r)), float(np.std(r)), float(np.min(r)), float(np.max(r)),\n", "            # Shape/density (5)\n", "            float(len(patch)),\n", "            float(np.std(x) / (np.std(y) + eps)),\n", "            float(np.std(z) / (np.std(x) + np.std(y) + eps)),\n", "            float(np.percentile(r, 90)),\n", "            float(np.mean(h > np.mean(h))),\n", "        ])\n", "    X = np.asarray(feats, np.float32)\n", "    if X.size and X.shape[1] != 22:\n", "        raise RuntimeError(f\"Feature drift: expected 22, got {X.shape[1]}\")\n", "    return X\n", "\n", "# ---- Core-masked negative patch utilities (runs inside buffers) ----\n", "\n", "def mask_core_points(patch_points_xyz: np.ndarray,\n", "                     tree_piles: cKDTree,\n", "                     core_radius: float) -> np.ndarray:\n", "    \"\"\"Remove points within core_radius (in XY) of any pile center.\"\"\"\n", "    if len(patch_points_xyz) == 0:\n", "        return patch_points_xyz\n", "    d, _ = tree_piles.query(patch_points_xyz[:, :2], k=1)\n", "    return patch_points_xyz[d > core_radius]\n", "\n", "def estimate_coverage_radius(tree_points: cKDTree, points_xy: np.n<PERSON><PERSON>,\n", "                             center_xy: np.n<PERSON><PERSON>, search_radius: float) -> float:\n", "    \"\"\"Rough local coverage radius based on 95th percentile distance of points near the center.\"\"\"\n", "    idxs = tree_points.query_ball_point(center_xy, r=search_radius)\n", "    if not idxs: return search_radius\n", "    dists = np.linalg.norm(points_xy[idxs] - center_xy, axis=1)\n", "    if len(dists) < 8: return search_radius\n", "    return float(np.percentile(dists, 95))\n", "\n", "def sample_negatives_with_core_mask(points_xyz: np.ndarray,\n", "                                    tree_points: cKDTree,\n", "                                    tree_piles: cKDTree,\n", "                                    pile_centers_xy: np.n<PERSON><PERSON>,\n", "                                    n_needed: int,\n", "                                    radius: float,\n", "                                    min_points_before: int,\n", "                                    min_points_after: int,\n", "                                    core_radius: float,\n", "                                    ring_inner_margin: float,\n", "                                    ring_outer_margin: float,\n", "                                    max_per_pile: int,\n", "                                    max_angle_trials: int,\n", "                                    seed: int = 99) -> tuple[list[np.ndarray], np.ndarray]:\n", "    \"\"\"\n", "    For each pile center, place candidate centers within the buffer ring, extract patches,\n", "    mask out core points, and keep patches with enough remaining points.\n", "    Returns (patches, centers_xy_kept).\n", "    \"\"\"\n", "    rs = np.random.default_rng(seed)\n", "    pts_xy = points_xyz[:, :2]\n", "\n", "    patches, centers = [], []\n", "    per_pile_counts = np.zeros(len(pile_centers_xy), dtype=int)\n", "\n", "    order = np.arange(len(pile_centers_xy))\n", "    rs.shuffle(order)\n", "\n", "    for idx in order:\n", "        if len(patches) >= n_needed: break\n", "        c = pile_centers_xy[idx]\n", "        if per_pile_counts[idx] >= max_per_pile: continue\n", "\n", "        cov_r = estimate_coverage_radius(tree_points, pts_xy, c, search_radius=radius*2.0)\n", "        r_min = core_radius + ring_inner_margin\n", "        r_max = max(r_min + 0.15, cov_r - ring_outer_margin)\n", "        got_for_this_pile = 0\n", "\n", "        for _ in range(max_angle_trials):\n", "            if len(patches) >= n_needed or got_for_this_pile >= max_per_pile: break\n", "            theta = rs.uniform(0, 2*np.pi)\n", "            rr = rs.uniform(r_min, r_max)\n", "            center_xy = c + np.array([rr*np.cos(theta), rr*np.sin(theta)])\n", "\n", "            idxs = tree_points.query_ball_point(center_xy, r=radius)\n", "            if len(idxs) < min_points_before: continue\n", "            patch = points_xyz[np.asarray(idxs)]\n", "\n", "            patch_masked = mask_core_points(patch, tree_piles, core_radius)\n", "            if len(patch_masked) < min_points_after: continue\n", "\n", "            center_z = float(np.mean(patch_masked[:,2]))\n", "            centered = patch_masked - np.array([center_xy[0], center_xy[1], center_z], np.float32)\n", "            centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=len(patches))\n", "            patches.append(centered.astype(np.float32, copy=False))\n", "            centers.append(center_xy)\n", "            got_for_this_pile += 1\n", "            per_pile_counts[idx] += 1\n", "\n", "    return patches, (np.asarray(centers, float) if centers else np.empty((0,2), float))\n", "\n", "def features_from_core_masked_xy(points_xyz: np.ndarray,\n", "                                 tree_points: cKDTree,\n", "                                 tree_piles: cKDTree,\n", "                                 centers_xy: np.n<PERSON><PERSON>,\n", "                                 radius: float,\n", "                                 min_points_before: int,\n", "                                 min_points_after: int,\n", "                                 core_radius: float) -> tuple[np.ndarray, list[np.ndarray]]:\n", "    \"\"\"Extract 22-feature vectors from given centers with core masking (tree reuse).\"\"\"\n", "    if len(centers_xy) == 0:\n", "        return np.empty((0,22), np.float32), []\n", "    X_list, patches_list = [], []\n", "    for i, center_xy in enumerate(centers_xy):\n", "        idxs = tree_points.query_ball_point(center_xy, r=radius)\n", "        if len(idxs) < min_points_before: continue\n", "        patch = points_xyz[np.asarray(idxs)]\n", "        patch_m = mask_core_points(patch, tree_piles, core_radius)\n", "        if len(patch_m) < min_points_after: continue\n", "\n", "        center_z = float(np.mean(patch_m[:,2]))\n", "        centered = patch_m - np.array([center_xy[0], center_xy[1], center_z], np.float32)\n", "        centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=i+1234)\n", "        X_list.append(extract_features([centered])[0])\n", "        patches_list.append(centered)\n", "    X = np.asarray(X_list, np.float32) if X_list else np.empty((0,22), np.float32)\n", "    return X, patches_list\n", "\n", "def tune_threshold(y_true: np.ndarray, y_prob: np.ndarray, grid: list[float]):\n", "    best = dict(threshold=0.5, f1=-1.0, precision=0.0, recall=0.0, accuracy=0.0)\n", "    y_true = np.asarray(y_true).astype(int)\n", "    for t in grid:\n", "        y_pred = (y_prob >= t).astype(int)\n", "        p, r, f1, _ = precision_recall_fscore_support(y_true, y_pred, average=\"binary\", zero_division=0)\n", "        acc = accuracy_score(y_true, y_pred)\n", "        if f1 > best[\"f1\"]:\n", "            best.update(dict(threshold=float(t), f1=float(f1), precision=float(p), recall=float(r), accuracy=float(acc)))\n", "    return best\n", "\n", "def save_fig(fig, name: str):\n", "    if not SAVE_PLOTS:\n", "        if SHOW_PLOTS: plt.show()\n", "        else: plt.close(fig)\n", "        return\n", "    path = out_dir / f\"{name}_{timestamp}.png\"\n", "    fig.tight_layout()\n", "    fig.savefig(path, dpi=150)\n", "    if SHOW_PLOTS:\n", "        plt.show()\n", "    else:\n", "        plt.close(fig)\n", "    print(f\"[PLOT] saved → {path}\")\n", "\n", "# %% [markdown]\n", "# ## Step 1: Train on RES (+ threshold selection)\n", "\n", "# %%\n", "# Load points & piles (RES)\n", "res_pts = read_las_points_xyz(RES_POINT_CLOUD_PATH)\n", "print(f\"[RES] points: {len(res_pts):,}\")\n", "\n", "res_piles_xy, res_epsg = load_and_reproject_kml_to_utm_points(RES_BUFFER_KML_PATH)\n", "print(f\"[RES] pile locations: {len(res_piles_xy)}  CRS={res_epsg}\")\n", "\n", "# Build KD-trees once (speed)\n", "res_tree_pts = cKDTree(res_pts[:, :2])\n", "res_tree_piles = cKDTree(res_piles_xy)\n", "\n", "# Positive patches\n", "res_pos_patches, res_pos_locs = extract_patches(\n", "    res_pts, res_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=res_tree_pts\n", ")\n", "print(f\"[RES] positive patches: {len(res_pos_patches)}\")\n", "if len(res_pos_patches) == 0:\n", "    raise RuntimeError(\"No positive patches on RES; check KML / radius / min_points.\")\n", "n_pos_full = len(res_pos_patches)\n", "\n", "# Strategic negatives (locations) → core-masked features\n", "neg_from_csv = None\n", "try:\n", "    df_neg = pd.read_csv(RES_STRATEGIC_NEG_CSV)\n", "    if {\"utm_x\",\"utm_y\"}.issubset(df_neg.columns):\n", "        neg_from_csv = df_neg[[\"utm_x\",\"utm_y\"]].to_numpy(float)\n", "        print(f\"[RES] strategic negatives loaded (locations): {len(neg_from_csv)}\")\n", "    else:\n", "        print(\"[RES] strategic negatives CSV missing 'utm_x','utm_y' → ignored\")\n", "except FileNotFoundError:\n", "    print(\"[RES] strategic negatives CSV not found → skipping\")\n", "\n", "X_neg_csv, neg_csv_patches = (np.empty((0,22), np.float32), [])\n", "if neg_from_csv is not None and len(neg_from_csv):\n", "    X_neg_csv, neg_csv_patches = features_from_core_masked_xy(\n", "        res_pts, res_tree_pts, res_tree_piles, neg_from_csv,\n", "        radius=PATCH_RADIUS,\n", "        min_points_before=MIN_POINTS,\n", "        min_points_after=MIN_POINTS_AFTER_MASK,\n", "        core_radius=CORE_EXCLUDE_RADIUS,\n", "    )\n", "    print(f\"[RES] negatives from strategic_csv (usable after mask): {len(X_neg_csv)}\")\n", "\n", "# Within-buffer core-masked negatives (top-up to match positives)\n", "need = max(0, n_pos_full - len(X_neg_csv))\n", "neg_patches_extra, _ = sample_negatives_with_core_mask(\n", "    points_xyz=res_pts,\n", "    tree_points=res_tree_pts,\n", "    tree_piles=res_tree_piles,\n", "    pile_centers_xy=res_piles_xy,\n", "    n_needed=need,\n", "    radius=PATCH_RADIUS,\n", "    min_points_before=MIN_POINTS,\n", "    min_points_after=MIN_POINTS_AFTER_MASK,\n", "    core_radius=CORE_EXCLUDE_RADIUS,\n", "    ring_inner_margin=RING_INNER_MARGIN,\n", "    ring_outer_margin=RING_OUTER_MARGIN,\n", "    max_per_pile=MAX_PER_PILE,\n", "    max_angle_trials=MAX_ANGLE_TRIALS,\n", "    seed=77,\n", ")\n", "X_neg_extra = extract_features(neg_patches_extra) if len(neg_patches_extra) else np.empty((0,22), np.float32)\n", "print(f\"[RES] negatives from within-buffer sampler: {len(X_neg_extra)}\")\n", "\n", "# Final negatives + balance\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = np.vstack([arr for arr in (X_neg_csv, X_neg_extra) if len(arr)]) if (len(X_neg_csv) or len(X_neg_extra)) else np.empty((0,22), np.float32)\n", "if len(X_neg_res) == 0:\n", "    raise RuntimeError(\"No usable negatives; tune CORE_EXCLUDE_RADIUS / RING margins / MIN_POINTS_AFTER_MASK.\")\n", "\n", "n_use = min(len(X_pos_res), len(X_neg_res))\n", "X_pos_res, X_neg_res = X_pos_res[:n_use], X_neg_res[:n_use]\n", "print(f\"[RES] using balanced features → pos={len(X_pos_res)}, neg={len(X_neg_res)}\")\n", "\n", "# Train\n", "X_train_res = np.vstack([X_pos_res, X_neg_res]).astype(np.float32)\n", "y_train_res = np.hstack([np.ones(len(X_pos_res), int), np.zeros(len(X_neg_res), int)])\n", "print(f\"[RES] train samples: {len(X_train_res)} | features: {X_train_res.shape[1]}\")\n", "\n", "model = GradientBoostingClassifier(**GB_PARAMS).fit(X_train_res, y_train_res)\n", "\n", "# Threshold tuning on RES\n", "y_prob_res = model.predict_proba(X_train_res)[:,1]\n", "best = tune_threshold(y_train_res, y_prob_res, THRESH_GRID)\n", "print(f\"[RES] tuned threshold = {best['threshold']:.2f} | P={best['precision']:.2f} R={best['recall']:.2f} F1={best['f1']:.2f} Acc={best['accuracy']:.2f}\")\n", "\n", "# ---- PLOTS: RES PR curve, confusion matrix, and prob histograms ----\n", "# PR curve (RES)\n", "prec, rec, thr = precision_recall_curve(y_train_res, y_prob_res)\n", "fig = plt.figure(figsize=(5.5, 4.0))\n", "plt.plot(rec, prec, label=\"PR curve\")\n", "# Mark chosen threshold (approximate match by nearest point)\n", "idx_thr = np.argmin(np.abs(thr - best[\"threshold\"])) if len(thr) else None\n", "if idx_thr is not None and idx_thr < len(rec):\n", "    plt.scatter(rec[idx_thr], prec[idx_thr], marker=\"o\", label=f\"thr={best['threshold']:.2f}\")\n", "plt.xlabel(\"Recall\"); plt.ylabel(\"Precision\"); plt.title(\"RES Precision-Recall\")\n", "plt.legend()\n", "save_fig(fig, \"res_pr_curve\")\n", "\n", "# Confusion matrix (RES, at tuned threshold)\n", "y_pred_res_train = (y_prob_res >= best[\"threshold\"]).astype(int)\n", "cm = confusion_matrix(y_train_res, y_pred_res_train)\n", "fig = plt.figure(figsize=(4.5, 4.0))\n", "plt.imshow(cm, interpolation=\"nearest\")\n", "plt.xticks([0,1], [\"Neg\",\"Pos\"]); plt.yticks([0,1], [\"Neg\",\"Pos\"])\n", "plt.xlabel(\"Predicted\"); plt.ylabel(\"True\"); plt.title(\"RES Confusion Matrix\")\n", "for (i,j), v in np.ndenumerate(cm):\n", "    plt.text(j, i, int(v), ha=\"center\", va=\"center\")\n", "save_fig(fig, \"res_confusion_matrix\")\n", "\n", "# Probability histograms (RES)\n", "fig = plt.figure(figsize=(5.5, 4.0))\n", "plt.hist(y_prob_res[y_train_res==1], bins=30, alpha=0.6, label=\"Positives\")\n", "plt.hist(y_prob_res[y_train_res==0], bins=30, alpha=0.6, label=\"Negatives\")\n", "plt.axvline(best[\"threshold\"], linestyle=\"--\", label=f\"thr={best['threshold']:.2f}\")\n", "plt.xlabel(\"Predicted probability\"); plt.ylabel(\"Count\"); plt.title(\"RES probability distributions\")\n", "plt.legend()\n", "save_fig(fig, \"res_prob_hist\")\n", "\n", "# Save model\n", "model_path = out_dir / f\"res_trained_gb_{timestamp}.pkl\"\n", "joblib.dump(model, model_path)\n", "print(f\"[RES] model saved → {model_path}\")\n", "\n", "# %% [markdown]\n", "# ## Step 2: Test on RCPS (no retraining)\n", "\n", "# %%\n", "rcps_pts = read_las_points_xyz(RCPS_POINT_CLOUD_PATH)\n", "print(f\"[RCPS] points: {len(rcps_pts):,}\")\n", "\n", "rcps_piles_xy, rcps_epsg = load_and_reproject_kml_to_utm_points(RCPS_BUFFER_KML_PATH)\n", "print(f\"[RCPS] pile locations: {len(rcps_piles_xy)}  CRS={rcps_epsg}\")\n", "\n", "# Build KD-trees once\n", "rcps_tree_pts = cKDTree(rcps_pts[:, :2])\n", "rcps_tree_piles = cKDTree(rcps_piles_xy)\n", "\n", "rcps_patches, rcps_kept_xy = extract_patches(\n", "    rcps_pts, rcps_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=rcps_tree_pts\n", ")\n", "print(f\"[RCPS] patches extracted: {len(rcps_patches)}\")\n", "X_rcps = extract_features(rcps_patches) if len(rcps_patches) else np.empty((0,22), np.float32)\n", "\n", "y_prob_rcps = model.predict_proba(X_rcps)[:,1] if len(X_rcps) else np.array([])\n", "y_pred_rcps = (y_prob_rcps >= best[\"threshold\"]).astype(int) if len(y_prob_rcps) else np.array([])\n", "rcps_detection_rate = float(np.mean(y_pred_rcps)) if len(y_pred_rcps) else 0.0\n", "\n", "# RES positive recall at tuned threshold:\n", "res_pos_prob = model.predict_proba(X_pos_res)[:,1]\n", "res_detection_rate = float(np.mean(res_pos_prob >= best[\"threshold\"]))\n", "perf_diff = rcps_detection_rate - res_detection_rate\n", "\n", "if len(y_prob_rcps):\n", "    print(f\"[RCPS] prob stats → min={y_prob_rcps.min():.3f}, p50={np.median(y_prob_rcps):.3f}, max={y_prob_rcps.max():.3f}, thr={best['threshold']:.2f}\")\n", "print(f\"[RCPS] detection rate: {rcps_detection_rate*100:.1f}% | Δ vs RES: {perf_diff*100:+.1f} pp\")\n", "\n", "# ---- PLOTS: RCPS probability histogram (positives only; add negatives if probe runs) ----\n", "if len(y_prob_rcps):\n", "    fig = plt.figure(figsize=(5.5, 4.0))\n", "    plt.hist(y_prob_rcps, bins=30, alpha=0.7, label=\"RCPS piles\")\n", "    plt.axvline(best[\"threshold\"], linestyle=\"--\", label=f\"thr={best['threshold']:.2f}\")\n", "    plt.xlabel(\"Predicted probability\"); plt.ylabel(\"Count\"); plt.title(\"RCPS pile probabilities\")\n", "    plt.legend()\n", "    save_fig(fig, \"rcps_prob_hist_pos\")\n", "\n", "# --- RCPS precision probe: within-buffer core-masked negatives (CAPPED) ---\n", "precision = None; fpr = None; rcps_negatives_count = 0\n", "if RUN_PRECISION_PROBE:\n", "    print(\"[RCPS] precision probe with core-masked within-buffer negatives...\")\n", "    probe_n = min(len(rcps_kept_xy), PROBE_NEG_MAX)\n", "    neg_patches_probe, neg_centers_xy = sample_negatives_with_core_mask(\n", "        points_xyz=rcps_pts,\n", "        tree_points=rcps_tree_pts,\n", "        tree_piles=rcps_tree_piles,\n", "        pile_centers_xy=rcps_piles_xy,\n", "        n_needed=probe_n,\n", "        radius=PATCH_RADIUS,\n", "        min_points_before=MIN_POINTS,\n", "        min_points_after=MIN_POINTS_AFTER_MASK,\n", "        core_radius=CORE_EXCLUDE_RADIUS,\n", "        ring_inner_margin=RING_INNER_MARGIN,\n", "        ring_outer_margin=RING_OUTER_MARGIN,\n", "        max_per_pile=MAX_PER_PILE,\n", "        max_angle_trials=MAX_ANGLE_TRIALS,\n", "        seed=131,\n", "    )\n", "    X_rcps_neg = extract_features(neg_patches_probe) if len(neg_patches_probe) else np.empty((0,22), np.float32)\n", "\n", "    if len(X_rcps_neg):\n", "        y_prob_neg = model.predict_proba(X_rcps_neg)[:,1]\n", "        y_pred_neg = (y_prob_neg >= best[\"threshold\"]).astype(int)\n", "        fp = int(np.sum(y_pred_neg == 1))\n", "        tn = int(np.sum(y_pred_neg == 0))\n", "        rcps_negatives_count = int(len(X_rcps_neg))\n", "        precision = float(len(y_pred_rcps) / (len(y_pred_rcps) + fp)) if (len(y_pred_rcps)+fp) > 0 else 0.0\n", "        fpr = fp / (fp + tn) if (fp+tn) > 0 else 0.0\n", "        print(f\"[RCPS] negatives probed (capped): {rcps_negatives_count} | FP={fp}, TN={tn} | Precision≈{precision:.3f} | FPR={fpr:.3f}\")\n", "\n", "        # Plot RCPS pos vs neg probability hist\n", "        if len(y_prob_rcps):\n", "            fig = plt.figure(figsize=(5.5, 4.0))\n", "            plt.hist(y_prob_rcps, bins=30, alpha=0.6, label=\"RCPS piles (+)\")\n", "            plt.hist(y_prob_neg, bins=30, alpha=0.6, label=\"RCPS off-pile (−)\")\n", "            plt.axvline(best[\"threshold\"], linestyle=\"--\", label=f\"thr={best['threshold']:.2f}\")\n", "            plt.xlabel(\"Predicted probability\"); plt.ylabel(\"Count\")\n", "            plt.title(\"RCPS pos vs off-pile prob distributions\")\n", "            plt.legend()\n", "            save_fig(fig, \"rcps_prob_hist_pos_vs_neg\")\n", "\n", "        # Optional CSV for audit\n", "        probe_csv = out_dir / f\"rcps_precision_probe_{timestamp}.csv\"\n", "        pd.DataFrame({\n", "            \"utm_x\": neg_centers_xy[:len(y_pred_neg),0],\n", "            \"utm_y\": neg_centers_xy[:len(y_pred_neg),1],\n", "            \"pred\": y_pred_neg,\n", "            \"prob\": y_prob_neg\n", "        }).to_csv(probe_csv, index=False)\n", "        print(f\"[OK] RCPS precision probe CSV → {probe_csv}\")\n", "    else:\n", "        print(\"[RCPS] precision probe skipped (no usable core-masked negatives).\")\n", "else:\n", "    print(\"[RCPS] precision probe skipped by flag (RUN_PRECISION_PROBE=False).\")\n", "\n", "# %% [markdown]\n", "# ## Results + Exports (CSV only; GeoJSON removed)\n", "\n", "# %%\n", "# Status gate: require both high recall (on piles) and high precision (off-pile probe)\n", "precision_ok = (precision is not None) and (precision >= 0.90)\n", "recall_ok    = (rcps_detection_rate >= 0.90)\n", "\n", "if precision_ok and recall_ok:\n", "    status = \"EXCELLENT CROSS-SITE GENERALIZATION\"\n", "elif (rcps_detection_rate >= 0.70) and (precision is not None) and (precision >= 0.70):\n", "    status = \"MODERATE CROSS-SITE GENERALIZATION\"\n", "else:\n", "    status = \"POOR CROSS-SITE GENERALIZATION\"\n", "\n", "summary = {\n", "    \"experiment_info\": {\n", "        \"type\": \"true_model_generalization\",\n", "        \"training_site\": RES_SITE_NAME,\n", "        \"testing_site\": RCPS_SITE_NAME,\n", "        \"timestamp\": timestamp,\n", "        \"model_file\": \"\",  # filled below\n", "        \"features\": int(X_train_res.shape[1]),\n", "        \"threshold\": float(best[\"threshold\"]),\n", "        \"epsg_res\": res_epsg,\n", "        \"epsg_rcps\": rcps_epsg\n", "    },\n", "    \"performance_metrics\": {\n", "        \"res_precision\": float(best[\"precision\"]),\n", "        \"res_recall\": float(best[\"recall\"]),\n", "        \"res_f1\": float(best[\"f1\"]),\n", "        \"res_accuracy\": float(best[\"accuracy\"]),\n", "        \"res_detection_rate_on_positives\": float(res_detection_rate),\n", "        \"rcps_detection_rate\": float(rcps_detection_rate),\n", "        \"rcps_precision_probe_precision\": (float(precision) if precision is not None else None),\n", "        \"rcps_precision_probe_fpr\": (float(fpr) if fpr is not None else None),\n", "        \"rcps_prob_min\": (float(y_prob_rcps.min()) if len(y_prob_rcps) else None),\n", "        \"rcps_prob_p50\": (float(np.median(y_prob_rcps)) if len(y_prob_rcps) else None),\n", "        \"rcps_prob_max\": (float(y_prob_rcps.max()) if len(y_prob_rcps) else None),\n", "        \"performance_difference\": float(perf_diff),\n", "        \"generalization_status\": status\n", "    },\n", "    \"data_info\": {\n", "        \"res_pile_locations\": int(len(res_piles_xy)),\n", "        \"rcps_pile_locations\": int(len(rcps_piles_xy)),\n", "        \"train_pos_used\": int(len(X_pos_res)),\n", "        \"train_neg_used\": int(len(X_neg_res)),\n", "        \"patch_radius\": float(PATCH_RADIUS),\n", "        \"target_patch_size\": int(TARGET_PATCH_SIZE),\n", "        \"min_points\": int(MIN_POINTS),\n", "        \"min_points_after_mask\": int(MIN_POINTS_AFTER_MASK),\n", "        \"core_exclude_radius\": float(CORE_EXCLUDE_RADIUS),\n", "        \"rcps_precision_probe_negatives\": int(rcps_negatives_count),\n", "        \"speed_mode\": SPEED_MODE\n", "    }\n", "}\n", "model_path = out_dir / f\"res_trained_gb_{timestamp}.pkl\"\n", "joblib.dump(model, model_path)\n", "summary[\"experiment_info\"][\"model_file\"] = str(model_path)\n", "results_file = out_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, \"w\") as f:\n", "    json.dump(summary, f, indent=2)\n", "print(f\"[OK] results saved → {results_file}\")\n", "print(f\"[RES] model saved → {model_path}\")\n", "\n", "# Export RCPS predictions for QGIS (CSV only)\n", "if len(rcps_kept_xy):\n", "    gdf_rcps = gpd.GeoDataFrame(\n", "        {\n", "            \"pile_id\": np.arange(len(rcps_kept_xy)),\n", "            \"utm_x\": rcps_kept_xy[:,0],\n", "            \"utm_y\": rcps_kept_xy[:,1],\n", "            \"predicted_pile\": y_pred_rcps.astype(int),\n", "            \"confidence\": y_prob_rcps.astype(float),\n", "            \"training_site\": RES_SITE_NAME,\n", "            \"test_site\": RCPS_SITE_NAME,\n", "            \"detection_status\": np.where(y_pred_rcps==1, \"Detected\",\"Missed\"),\n", "            \"generalization_type\": \"cross_site_pile_detection\",\n", "        },\n", "        geometry=[Point(xy) for xy in rcps_kept_xy],\n", "        crs=rcps_epsg\n", "    )\n", "    gdf_wgs84 = gdf_rcps.to_crs(\"EPSG:4326\")\n", "    rcps_csv = out_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "    df_out = pd.DataFrame(gdf_rcps.drop(columns=\"geometry\"))\n", "    df_out[\"longitude\"] = gdf_wgs84.geometry.x\n", "    df_out[\"latitude\"]  = gdf_wgs84.geometry.y\n", "    df_out.to_csv(rcps_csv, index=False)\n", "    print(f\"[OK] QGIS CSV (with lon/lat) → {rcps_csv}\")\n", "\n", "print(\"Cross-site generalization test complete.\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}