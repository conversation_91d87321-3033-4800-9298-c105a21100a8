{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Fixed Classical ML Pile Detection\n", "\n", "This notebook trains classical ML models using 3D coordinate patch data.\n", "\n", "**Goal**: Compare classical ML vs PointNet++ (94.2% F1-score)\n", "\n", "**Models**: Random Forest, <PERSON><PERSON><PERSON> Boosting, SVM, Logistic Regression\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": 1, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import pickle\n", "import json\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# ML libraries\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# Visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "print(\"Libraries imported successfully\")"]}, {"cell_type": "code", "execution_count": 2, "id": "config", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Target to beat - PointNet++ F1: 0.942\n"]}], "source": ["# Configuration\n", "DATA_PATH = \"../../00_data_preprocessing/experimentation/output/ml_patch_data\"\n", "OUTPUT_DIR = \"output_runs/fixed_classical_ml\"\n", "RANDOM_STATE = 42\n", "\n", "# PointNet++ baseline to beat\n", "POINTNET_BASELINE = {\n", "    'accuracy': 0.905,\n", "    'f1_score': 0.942,\n", "    'precision': 0.923,\n", "    'recall': 0.962\n", "}\n", "\n", "print(f\"Target to beat - PointNet++ F1: {POINTNET_BASELINE['f1_score']:.3f}\")\n", "\n", "# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)"]}, {"cell_type": "code", "execution_count": 3, "id": "load_data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading patch data...\n", "Using patch data from: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks/modeling/pile_detection/00_data_preprocessing/experimentation/output/ml_patch_data/patches_20250722_160244\n", "  train: 3345 patches, sample shape: (32, 3)\n", "  val: 717 patches, sample shape: (30, 3)\n", "  test: 717 patches, sample shape: (81, 3)\n", "Data loading complete!\n"]}], "source": ["def load_patch_data(base_dir):\n", "    \"\"\"Load patch data and inspect structure\"\"\"\n", "    patch_root = Path(base_dir).resolve()\n", "    \n", "    # Find the latest patch directory\n", "    patch_dirs = sorted(patch_root.glob(\"patches_*\"))\n", "    latest_patch_dir = patch_dirs[-1]\n", "    print(f\"Using patch data from: {latest_patch_dir}\")\n", "    \n", "    datasets = {}\n", "    for split in ['train', 'val', 'test']:\n", "        patch_file = latest_patch_dir / f\"{split}_patches.pkl\"\n", "        meta_file = latest_patch_dir / f\"{split}_metadata.json\"\n", "        \n", "        with open(patch_file, 'rb') as f:\n", "            patches = pickle.load(f)\n", "        with open(meta_file, 'r') as f:\n", "            metadata = json.load(f)\n", "        \n", "        datasets[split] = {'patches': patches, 'metadata': metadata}\n", "        \n", "        # Inspect data structure\n", "        if len(patches) > 0:\n", "            sample_patch = patches[0]\n", "            print(f\"  {split}: {len(patches)} patches, sample shape: {sample_patch.shape}\")\n", "        else:\n", "            print(f\"  {split}: {len(patches)} patches (empty)\")\n", "    \n", "    return datasets\n", "\n", "# Load data\n", "print(\"Loading patch data...\")\n", "datasets = load_patch_data(DATA_PATH)\n", "print(\"Data loading complete!\")"]}, {"cell_type": "code", "execution_count": 4, "id": "extract_features", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting classical ML features...\n", "Feature extraction complete:\n", "  Train: 3345 samples, 22 features\n", "  Val: 717 samples, 22 features\n", "  Test: 717 samples, 22 features\n", "  Class distribution - Train: [ 661 2684], Test: [142 575]\n"]}], "source": ["def extract_classical_features(patches, metadata):\n", "    \"\"\"Extract classical ML features from 3D coordinate patch data\"\"\"\n", "    features = []\n", "    labels = []\n", "    \n", "    for patch, meta in zip(patches, metadata):\n", "        if len(patch) == 0:\n", "            continue\n", "            \n", "        # Extract 3D coordinates\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        \n", "        # Compute derived features\n", "        radial_dist = np.sqrt(x**2 + y**2)\n", "        height_above_min = z - np.min(z)\n", "        \n", "        # Statistical features (22 features total)\n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch),  # num_points\n", "            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio\n", "            np.percentile(radial_dist, 90),  # 90th percentile radial distance\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height\n", "        ]\n", "        \n", "        features.append(feature_vector)\n", "        \n", "        # Extract label\n", "        if isinstance(meta, dict):\n", "            label = meta.get('label', meta.get('patch_type') == 'positive')\n", "        else:\n", "            label = meta\n", "        \n", "        labels.append(int(label))\n", "    \n", "    return np.array(features, dtype=np.float32), np.array(labels, dtype=np.int64)\n", "\n", "# Extract features\n", "print(\"Extracting classical ML features...\")\n", "X_train, y_train = extract_classical_features(datasets['train']['patches'], datasets['train']['metadata'])\n", "X_val, y_val = extract_classical_features(datasets['val']['patches'], datasets['val']['metadata'])\n", "X_test, y_test = extract_classical_features(datasets['test']['patches'], datasets['test']['metadata'])\n", "\n", "print(f\"Feature extraction complete:\")\n", "print(f\"  Train: {X_train.shape[0]} samples, {X_train.shape[1]} features\")\n", "print(f\"  Val: {X_val.shape[0]} samples, {X_val.shape[1]} features\")\n", "print(f\"  Test: {X_test.shape[0]} samples, {X_test.shape[1]} features\")\n", "print(f\"  Class distribution - Train: {np.bincount(y_train)}, Test: {np.bincount(y_test)}\")"]}, {"cell_type": "code", "execution_count": 5, "id": "preprocessing", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature scaling complete\n"]}], "source": ["# Feature scaling for SVM and Logistic Regression\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"Feature scaling complete\")"]}, {"cell_type": "code", "execution_count": 6, "id": "train_models", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training classical ML models...\n", "==================================================\n"]}], "source": ["def evaluate_model(model, X_test, y_test, model_name):\n", "    \"\"\"Evaluate model and return metrics\"\"\"\n", "    y_pred = model.predict(X_test)\n", "    \n", "    metrics = {\n", "        'accuracy': accuracy_score(y_test, y_pred),\n", "        'precision': precision_score(y_test, y_pred),\n", "        'recall': recall_score(y_test, y_pred),\n", "        'f1_score': f1_score(y_test, y_pred)\n", "    }\n", "    \n", "    print(f\"\\n{model_name} Results:\")\n", "    print(f\"  Accuracy: {metrics['accuracy']:.4f}\")\n", "    print(f\"  Precision: {metrics['precision']:.4f}\")\n", "    print(f\"  Recall: {metrics['recall']:.4f}\")\n", "    print(f\"  F1-Score: {metrics['f1_score']:.4f}\")\n", "    \n", "    return metrics, y_pred\n", "\n", "# Train models\n", "results = {}\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "print(\"Training classical ML models...\")\n", "print(\"=\" * 50)"]}, {"cell_type": "code", "execution_count": 7, "id": "train_all_models", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training Random Forest...\n", "\n", "Random Forest Results:\n", "  Accuracy: 0.9456\n", "  Precision: 0.9621\n", "  Recall: 0.9704\n", "  F1-Score: 0.9662\n", "Training Gradient Boosting...\n", "\n", "Gradient Boosting Results:\n", "  Accuracy: 0.9470\n", "  Precision: 0.9605\n", "  Recall: 0.9739\n", "  F1-Score: 0.9672\n", "Training SVM...\n", "\n", "SVM Results:\n", "  Accuracy: 0.9344\n", "  Precision: 0.9490\n", "  Recall: 0.9704\n", "  F1-Score: 0.9596\n", "Training Logistic Regression...\n", "\n", "Logistic Regression Results:\n", "  Accuracy: 0.8898\n", "  Precision: 0.9106\n", "  Recall: 0.9565\n", "  F1-Score: 0.9330\n", "\n", "All models trained successfully!\n"]}], "source": ["# Random Forest\n", "print(\"Training Random Forest...\")\n", "rf_model = RandomForestClassifier(\n", "    n_estimators=100, max_depth=20, min_samples_split=5, \n", "    min_samples_leaf=2, random_state=RANDOM_STATE\n", ")\n", "rf_model.fit(X_train, y_train)\n", "rf_metrics, rf_pred = evaluate_model(rf_model, X_test, y_test, \"Random Forest\")\n", "results['random_forest'] = {'model': rf_model, 'metrics': rf_metrics, 'predictions': rf_pred}\n", "\n", "# Gradient Boosting\n", "print(\"Training Gradient Boosting...\")\n", "gb_model = GradientBoostingClassifier(\n", "    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=RANDOM_STATE\n", ")\n", "gb_model.fit(X_train, y_train)\n", "gb_metrics, gb_pred = evaluate_model(gb_model, X_test, y_test, \"Gradient Boosting\")\n", "results['gradient_boosting'] = {'model': gb_model, 'metrics': gb_metrics, 'predictions': gb_pred}\n", "\n", "# SVM (using scaled data)\n", "print(\"Training SVM...\")\n", "svm_model = SVC(kernel='rbf', C=1.0, gamma='scale', random_state=RANDOM_STATE)\n", "svm_model.fit(X_train_scaled, y_train)\n", "svm_metrics, svm_pred = evaluate_model(svm_model, X_test_scaled, y_test, \"SVM\")\n", "results['svm'] = {'model': svm_model, 'metrics': svm_metrics, 'predictions': svm_pred, 'uses_scaled_data': True}\n", "\n", "# Logistic Regression (using scaled data)\n", "print(\"Training Logistic Regression...\")\n", "lr_model = LogisticRegression(C=1.0, max_iter=1000, random_state=RANDOM_STATE)\n", "lr_model.fit(X_train_scaled, y_train)\n", "lr_metrics, lr_pred = evaluate_model(lr_model, X_test_scaled, y_test, \"Logistic Regression\")\n", "results['logistic'] = {'model': lr_model, 'metrics': lr_metrics, 'predictions': lr_pred, 'uses_scaled_data': True}\n", "\n", "print(\"\\nAll models trained successfully!\")"]}, {"cell_type": "code", "execution_count": 8, "id": "comparison", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "CLASSICAL ML MODELS PERFORMANCE COMPARISON\n", "============================================================\n", "            Model Accuracy Precision Recall F1-Score\n", "    Random Forest   0.9456    0.9621 0.9704   0.9662\n", "Gradient Boosting   0.9470    0.9605 0.9739   0.9672\n", "              Svm   0.9344    0.9490 0.9704   0.9596\n", "         Logistic   0.8898    0.9106 0.9565   0.9330\n", "\n", "============================================================\n", "COMPARISON WITH POINTNET++ BASELINE\n", "============================================================\n", "PointNet++ Baseline - F1: 0.9420, Accuracy: 0.9050\n", "\n", "Random Forest: F1=0.9662 (****%) - BEATS BASELINE\n", "Gradient Boosting: F1=0.9672 (****%) - BEATS BASELINE\n", "Svm: F1=0.9596 (****%) - BEATS BASELINE\n", "Logistic: F1=0.9330 (-1.0%) - Below baseline\n", "\n", "Best Classical Model: <PERSON><PERSON><PERSON> (F1: 0.9672)\n"]}], "source": ["# Create comparison table\n", "comparison_data = []\n", "for model_name, result in results.items():\n", "    metrics = result['metrics']\n", "    comparison_data.append({\n", "        'Model': model_name.replace('_', ' ').title(),\n", "        'Accuracy': f\"{metrics['accuracy']:.4f}\",\n", "        'Precision': f\"{metrics['precision']:.4f}\",\n", "        'Recall': f\"{metrics['recall']:.4f}\",\n", "        'F1-Score': f\"{metrics['f1_score']:.4f}\"\n", "    })\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"CLASSICAL ML MODELS PERFORMANCE COMPARISON\")\n", "print(\"=\" * 60)\n", "print(comparison_df.to_string(index=False))\n", "\n", "# Compare with PointNet++ baseline\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"COMPARISON WITH POINTNET++ BASELINE\")\n", "print(\"=\" * 60)\n", "print(f\"PointNet++ Baseline - F1: {POINTNET_BASELINE['f1_score']:.4f}, Accuracy: {POINTNET_BASELINE['accuracy']:.4f}\")\n", "print()\n", "\n", "best_model = None\n", "best_f1 = 0\n", "for model_name, result in results.items():\n", "    f1 = result['metrics']['f1_score']\n", "    improvement = ((f1 - POINTNET_BASELINE['f1_score']) / POINTNET_BASELINE['f1_score']) * 100\n", "    status = \"- BEATS BASELINE\" if f1 > POINTNET_BASELINE['f1_score'] else \"- Below baseline\"\n", "    print(f\"{model_name.replace('_', ' ').title()}: F1={f1:.4f} ({improvement:+.1f}%) {status}\")\n", "    \n", "    if f1 > best_f1:\n", "        best_f1 = f1\n", "        best_model = model_name\n", "\n", "print(f\"\\nBest Classical Model: {best_model.replace('_', ' ').title()} (F1: {best_f1:.4f})\")"]}, {"cell_type": "code", "execution_count": 11, "id": "save_models", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "SAVING TRAINED MODELS\n", "============================================================\n", "- Saved random_forest: random_forest_model_20250807_194855.pkl\n", "- Saved gradient_boosting: gradient_boosting_model_20250807_194855.pkl\n", "- Saved svm: svm_model_20250807_194855.pkl\n", "- Saved logistic: logistic_model_20250807_194855.pkl\n", "- Saved feature scaler: feature_scaler_20250807_194855.pkl\n", "- Model registry saved: model_registry_20250807_194855.json\n", "\n", "SUMMARY:\n", "  Best model: gradient_boosting (F1: 0.9672)\n", "  Models saved: 4\n", "  Registry: model_registry_20250807_194855.json\n"]}], "source": ["# Save trained models for validation\n", "import joblib\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"SAVING TRAINED MODELS\")\n", "print(\"=\" * 60)\n", "\n", "model_files = {}\n", "\n", "for model_name, result in results.items():\n", "    # Save model\n", "    model_path = output_dir / f\"{model_name}_model_{timestamp}.pkl\"\n", "    joblib.dump(result['model'], model_path)\n", "    model_files[model_name] = str(model_path)\n", "    \n", "    print(f\"- Saved {model_name}: {model_path.name}\")\n", "\n", "# Save scaler for models that need it\n", "scaler_path = output_dir / f\"feature_scaler_{timestamp}.pkl\"\n", "joblib.dump(scaler, scaler_path)\n", "print(f\"- Saved feature scaler: {scaler_path.name}\")\n", "\n", "# Save model registry for easy loading\n", "model_registry = {\n", "    'timestamp': timestamp,\n", "    'best_model': best_model,\n", "    'best_f1_score': best_f1,\n", "    'model_files': model_files,\n", "    'scaler_file': str(scaler_path),\n", "    'models_requiring_scaling': ['svm', 'logistic'],\n", "    'feature_count': X_train.shape[1],\n", "    'training_samples': len(X_train),\n", "    'pointnet_baseline': POINTNET_BASELINE,\n", "    'all_results': {name: result['metrics'] for name, result in results.items()}\n", "}\n", "\n", "registry_path = output_dir / f\"model_registry_{timestamp}.json\"\n", "with open(registry_path, 'w') as f:\n", "    json.dump(model_registry, f, indent=2)\n", "\n", "print(f\"- Model registry saved: {registry_path.name}\")\n", "\n", "print(f\"\\nSUMMARY:\")\n", "print(f\"  Best model: {best_model} (F1: {best_f1:.4f})\")\n", "print(f\"  Models saved: {len(model_files)}\")\n", "print(f\"  Registry: {registry_path.name}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}