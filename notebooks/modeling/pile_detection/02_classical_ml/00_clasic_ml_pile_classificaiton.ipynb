{"cells": [{"cell_type": "markdown", "id": "e6a12695", "metadata": {}, "source": ["# Classical ML Pile Detection\n", "\n", "This notebook implements classical machine learning approaches for pile detection\n", "using engineered features from 3D point cloud data.\n", "\n", "**Objectives**:\n", "- Extract geometric and spatial features from PointNet-format data\n", "- Compare Random Forest, Gradient Boosting, SVM, and Logistic Regression\n", "- Analyze spatial distribution of predictions vs ground truth\n", "- Generate prediction outputs in CSV and KML formats for GIS analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: June 2025\n", "**Project**: As Built Analytics for Solar Array Inspection\n"]}, {"cell_type": "code", "execution_count": 39, "id": "4040a5c5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Notebooks root: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/notebooks\n"]}], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[3] if '__file__' in globals() else Path.cwd().parents[3]\n", "print(f\"Notebooks root: {notebooks_root}\")\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import (\n", "    get_data_path,\n", "    get_output_path,\n", "    get_mlflow_tracking_uri,\n", "    find_latest_file,\n", "    get_processed_data_path\n", ")\n"]}, {"cell_type": "markdown", "id": "d5cbcb10", "metadata": {}, "source": ["## Configuration\n"]}, {"cell_type": "code", "execution_count": 40, "id": "a7570ad0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input data path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling\n", "Output path: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml\n", "MLflow tracking URI: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/mlruns\n", "Coordinate system: EPSG:32632\n", "Reference paths: {'pile_dataset': PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ml_patch_data/harmonized_pile_dataset_final.csv'), 'ifc_metadata': PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/trino_enel_enhanced_metadata.csv'), 'kml_ground_truth': PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/kml/pile.kml')}\n", "Model types: ['random_forest', 'gradient_boosting', 'svm', 'logistic_regression']\n"]}], "source": ["# Papermill parameters - these will be injected by Papermill\n", "from datetime import datetime\n", "\n", "site_name = \"trino_enel\"  # Site name for output file naming\n", "project_type = \"ENEL\"  # Options: \"ENEL\", \"USA\"\n", "method_name = \"classical_ml\"  # Method identifier for this analysis\n", "timestamp = None  # Auto-generated if None\n", "model_types = [\"random_forest\", \"gradient_boosting\", \"svm\", \"logistic_regression\"]  # Models to train\n", "\n", "# Generate timestamp if not provided\n", "if timestamp is None:\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# Standardized paths using your path functions\n", "input_data_path = get_processed_data_path(site_name, \"modelling\")\n", "output_path = get_processed_data_path(site_name, f\"modelling/{method_name}\")\n", "\n", "# Create output directories\n", "for folder in [\"models\", \"predictions\", \"figures\",\"kmls\", \"results\"]:\n", "    (output_path / folder).mkdir(parents=True, exist_ok=True)\n", "\n", "mlflow_tracking_uri = get_mlflow_tracking_uri()\n", "\n", "coordinate_system = \"EPSG:32632\"\n", "\n", "REFERENCE_PATHS = {\n", "    'pile_dataset': get_processed_data_path(site_name, f'ml_patch_data/harmonized_pile_dataset_final.csv'),\n", "    'ifc_metadata': get_processed_data_path(site_name, f'ifc_metadata/{site_name}_enhanced_metadata.csv'),\n", "    'kml_ground_truth': get_data_path(site_name)/'kml/pile.kml'\n", "}\n", "\n", "print(f\"Input data path: {input_data_path}\")\n", "print(f\"Output path: {output_path}\")\n", "print(f\"MLflow tracking URI: {mlflow_tracking_uri}\")\n", "print(f\"Coordinate system: {coordinate_system}\")\n", "print(f\"Reference paths: {REFERENCE_PATHS}\")\n", "print(f\"Model types: {model_types}\")\n"]}, {"cell_type": "markdown", "id": "9e4cc189", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": 41, "id": "06218b6f", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import pickle\n", "from pathlib import Path\n", "from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier\n", "from sklearn.svm import SVC\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score\n", "from sklearn.preprocessing import StandardScaler\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "import joblib\n", "import json\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Configure matplotlib and pandas\n", "plt.style.use('default')\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "markdown", "id": "86da42da", "metadata": {}, "source": ["## Feature Extraction Functions"]}, {"cell_type": "code", "execution_count": 42, "id": "ce1ebf0d", "metadata": {}, "outputs": [], "source": ["def calculate_feature_vector(x_real, y_real, z_real, height_real, dist_real, density_real):\n", "    \"\"\"Calculate 25-dimensional feature vector from valid points.\"\"\"\n", "    \n", "    return [\n", "        # Basic counts and density\n", "        len(x_real),                    # Number of real points\n", "        len(x_real) / 1024,            # Density ratio\n", "        \n", "        # Height statistics (normalized)\n", "        np.mean(height_real),\n", "        np.std(height_real),\n", "        np.min(height_real),\n", "        np.max(height_real),\n", "        np.percentile(height_real, 75) - np.percentile(height_real, 25),  # IQR\n", "        \n", "        # Z-coordinate statistics (relative to pile)\n", "        np.mean(z_real),\n", "        np.std(z_real),\n", "        np.min(z_real),\n", "        np.max(z_real),\n", "        \n", "        # Distance statistics (normalized)\n", "        np.mean(dist_real),\n", "        np.std(dist_real),\n", "        np.min(dist_real),\n", "        np.max(dist_real),\n", "        \n", "        # Spatial distribution\n", "        np.std(x_real),\n", "        np.std(y_real),\n", "        np.corrcoef(x_real, y_real)[0, 1] if len(x_real) > 1 and np.std(x_real) > 0 and np.std(y_real) > 0 else 0,\n", "        \n", "        # Density features\n", "        np.mean(density_real),\n", "        np.std(density_real),\n", "        \n", "        # Shape analysis (using normalized distances)\n", "        np.sum(dist_real < 0.3) / len(dist_real),      # Close points\n", "        np.sum((dist_real >= 0.3) & (dist_real < 0.7)) / len(dist_real),  # Medium points\n", "        np.sum(dist_real >= 0.7) / len(dist_real),     # Far points\n", "        \n", "        # Vertical structure indicators\n", "        np.sum(height_real > np.mean(height_real) + 0.5 * np.std(height_real)) / len(height_real) if np.std(height_real) > 0 else 0,\n", "        \n", "        # Radial symmetry (pile indicator)\n", "        np.std(np.sqrt(x_real**2 + y_real**2)) / np.mean(np.sqrt(x_real**2 + y_real**2)) if np.mean(np.sqrt(x_real**2 + y_real**2)) > 0 else 0\n", "    ]\n", "\n", "def extract_patch_features(patches):\n", "    \"\"\"\n", "    Extract comprehensive features from point cloud patches.\n", "    \"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        # Extract coordinates and attributes\n", "        x_rel = patch[:, 0]          # Relative X\n", "        y_rel = patch[:, 1]          # Relative Y  \n", "        z_rel = patch[:, 2]          # Relative Z\n", "        height_norm = patch[:, 3]    # Normalized height\n", "        distance_norm = patch[:, 4]  # Normalized distance\n", "        density = patch[:, 5]        # Density\n", "        \n", "        # Filter out padded points\n", "        valid_mask = (np.abs(x_rel) + np.abs(y_rel) + np.abs(z_rel)) > 1e-6\n", "        \n", "        # Apply mask to get real points only\n", "        x_real = x_rel[valid_mask]\n", "        y_real = y_rel[valid_mask]\n", "        z_real = z_rel[valid_mask]\n", "        height_real = height_norm[valid_mask]\n", "        dist_real = distance_norm[valid_mask]\n", "        density_real = density[valid_mask]\n", "        \n", "        # Handle edge case of no valid points\n", "        if len(x_real) == 0:\n", "            feature_vector = [0] * 25\n", "        else:\n", "            # Calculate comprehensive features\n", "            feature_vector = calculate_feature_vector(\n", "                x_real, y_real, z_real, height_real, dist_real, density_real\n", "            )\n", "        \n", "        features.append(feature_vector)\n", "    \n", "    return features\n", "\n", "def load_and_extract_features():\n", "    \"\"\"\n", "    Load PointNet patches and extract classical ML features.\n", "    \"\"\"\n", "    datasets = {}\n", "    \n", "    file_mapping = {\n", "        'train': 'train_pointnet.pkl',\n", "        'val': 'val_pointnet.pkl', \n", "        'test': 'test_pointnet.pkl'\n", "    }\n", "    \n", "    for split, filename in file_mapping.items():\n", "        filepath = input_data_path/\"ml_patch_data\" / filename\n", "        \n", "        print(f\"Loading {filepath}...\")\n", "        with open(filepath, 'rb') as f:\n", "            data = pickle.load(f)\n", "        \n", "        patches = data['points']  # (N, 1024, 6)\n", "        labels = data['labels']   # (N,)\n", "        metadata = data.get('metadata', [])\n", "        \n", "        print(f\"Loaded {split}: {patches.shape}, labels: {len(labels)}\")\n", "        \n", "        # Extract aggregate features from each patch\n", "        features = extract_patch_features(patches)\n", "        \n", "        datasets[split] = {\n", "            'features': np.array(features),\n", "            'labels': labels,\n", "            'metadata': metadata,\n", "            'n_samples': len(features)\n", "        }\n", "        \n", "        print(f\"Extracted features for {split}: {datasets[split]['features'].shape}\")\n", "    \n", "    return datasets"]}, {"cell_type": "code", "execution_count": 43, "id": "55daa54b", "metadata": {}, "outputs": [], "source": ["\n", "def extract_patch_features(patches):\n", "    \"\"\"\n", "    Extract comprehensive features from point cloud patches.\n", "    \"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        # Extract coordinates and attributes\n", "        x_rel = patch[:, 0]          # Relative X\n", "        y_rel = patch[:, 1]          # Relative Y  \n", "        z_rel = patch[:, 2]          # Relative Z\n", "        height_norm = patch[:, 3]    # Normalized height\n", "        distance_norm = patch[:, 4]  # Normalized distance\n", "        density = patch[:, 5]        # Density\n", "        \n", "        # Filter out padded points\n", "        valid_mask = (np.abs(x_rel) + np.abs(y_rel) + np.abs(z_rel)) > 1e-6\n", "        \n", "        # Apply mask to get real points only\n", "        x_real = x_rel[valid_mask]\n", "        y_real = y_rel[valid_mask]\n", "        z_real = z_rel[valid_mask]\n", "        height_real = height_norm[valid_mask]\n", "        dist_real = distance_norm[valid_mask]\n", "        density_real = density[valid_mask]\n", "        \n", "        # Handle edge case of no valid points\n", "        if len(x_real) == 0:\n", "            feature_vector = [0] * 25\n", "        else:\n", "            # Calculate comprehensive features\n", "            feature_vector = calculate_feature_vector(\n", "                x_real, y_real, z_real, height_real, dist_real, density_real\n", "            )\n", "        \n", "        features.append(feature_vector)\n", "    \n", "    return features\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "id": "d5d594b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature extraction functions defined\n"]}], "source": ["def calculate_feature_vector(x_real, y_real, z_real, height_real, dist_real, density_real):\n", "    \"\"\"Calculate 25-dimensional feature vector from valid points.\"\"\"\n", "    \n", "    return [\n", "        # Basic counts and density\n", "        len(x_real),                    # Number of real points\n", "        len(x_real) / 1024,            # Density ratio\n", "        \n", "        # Height statistics (normalized)\n", "        np.mean(height_real),\n", "        np.std(height_real),\n", "        np.min(height_real),\n", "        np.max(height_real),\n", "        np.percentile(height_real, 75) - np.percentile(height_real, 25),  # IQR\n", "        \n", "        # Z-coordinate statistics (relative to pile)\n", "        np.mean(z_real),\n", "        np.std(z_real),\n", "        np.min(z_real),\n", "        np.max(z_real),\n", "        \n", "        # Distance statistics (normalized)\n", "        np.mean(dist_real),\n", "        np.std(dist_real),\n", "        np.min(dist_real),\n", "        np.max(dist_real),\n", "        \n", "        # Spatial distribution\n", "        np.std(x_real),\n", "        np.std(y_real),\n", "        np.corrcoef(x_real, y_real)[0, 1] if len(x_real) > 1 and np.std(x_real) > 0 and np.std(y_real) > 0 else 0,\n", "        \n", "        # Density features\n", "        np.mean(density_real),\n", "        np.std(density_real),\n", "        \n", "        # Shape analysis (using normalized distances)\n", "        np.sum(dist_real < 0.3) / len(dist_real),      # Close points\n", "        np.sum((dist_real >= 0.3) & (dist_real < 0.7)) / len(dist_real),  # Medium points\n", "        np.sum(dist_real >= 0.7) / len(dist_real),     # Far points\n", "        \n", "        # Vertical structure indicators\n", "        np.sum(height_real > np.mean(height_real) + 0.5 * np.std(height_real)) / len(height_real) if np.std(height_real) > 0 else 0,\n", "        \n", "        # Radial symmetry (pile indicator)\n", "        np.std(np.sqrt(x_real**2 + y_real**2)) / np.mean(np.sqrt(x_real**2 + y_real**2)) if np.mean(np.sqrt(x_real**2 + y_real**2)) > 0 else 0\n", "    ]\n", "\n", "\n", "print(\"Feature extraction functions defined\")"]}, {"cell_type": "markdown", "id": "e7f5e799", "metadata": {}, "source": ["## Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": 45, "id": "bb39737b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading PointNet data and extracting features...\n", "Loading /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/ml_patch_data/train_pointnet.pkl...\n", "Loaded train: (1287, 1024, 20), labels: 1287\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Extracted features for train: (1287, 25)\n", "Loading /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/ml_patch_data/val_pointnet.pkl...\n", "Loaded val: (418, 1024, 20), labels: 418\n", "Extracted features for val: (418, 25)\n", "Loading /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/ml_patch_data/test_pointnet.pkl...\n", "Loaded test: (418, 1024, 20), labels: 418\n", "Extracted features for test: (418, 25)\n", "Feature names (25): ['real_point_count', 'point_density_ratio', 'height_mean', 'height_std', 'height_min', 'height_max', 'height_iqr', 'z_rel_mean', 'z_rel_std', 'z_rel_min', 'z_rel_max', 'dist_norm_mean', 'dist_norm_std', 'dist_norm_min', 'dist_norm_max', 'x_rel_std', 'y_rel_std', 'xy_correlation', 'density_mean', 'density_std', 'close_point_ratio', 'medium_point_ratio', 'far_point_ratio', 'high_point_ratio', 'radial_symmetry']\n", "\n", "Data shapes:\n", "Training: (1287, 25)\n", "Validation: (418, 25)\n", "Test: (418, 25)\n", "\n", "Class distribution:\n", "Training: [426 861] (pos/neg)\n", "Validation: [131 287] (pos/neg)\n", "Test: [131 287] (pos/neg)\n"]}], "source": ["# Load and extract features\n", "print(\"Loading PointNet data and extracting features...\")\n", "datasets = load_and_extract_features()\n", "\n", "# Feature names for interpretation\n", "feature_names = [\n", "    'real_point_count', 'point_density_ratio',\n", "    'height_mean', 'height_std', 'height_min', 'height_max', 'height_iqr',\n", "    'z_rel_mean', 'z_rel_std', 'z_rel_min', 'z_rel_max',\n", "    'dist_norm_mean', 'dist_norm_std', 'dist_norm_min', 'dist_norm_max',\n", "    'x_rel_std', 'y_rel_std', 'xy_correlation',\n", "    'density_mean', 'density_std',\n", "    'close_point_ratio', 'medium_point_ratio', 'far_point_ratio',\n", "    'high_point_ratio', 'radial_symmetry'\n", "]\n", "\n", "print(f\"Feature names ({len(feature_names)}): {feature_names}\")\n", "\n", "# Prepare data arrays\n", "X_train = datasets['train']['features']\n", "y_train = datasets['train']['labels']\n", "X_val = datasets['val']['features']\n", "y_val = datasets['val']['labels']\n", "X_test = datasets['test']['features']\n", "y_test = datasets['test']['labels']\n", "\n", "# Handle any NaN values\n", "X_train = np.nan_to_num(X_train)\n", "X_val = np.nan_to_num(X_val)\n", "X_test = np.nan_to_num(X_test)\n", "\n", "# Scale features for algorithms that need it\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_val_scaled = scaler.transform(X_val)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(f\"\\nData shapes:\")\n", "print(f\"Training: {X_train.shape}\")\n", "print(f\"Validation: {X_val.shape}\")\n", "print(f\"Test: {X_test.shape}\")\n", "\n", "print(f\"\\nClass distribution:\")\n", "print(f\"Training: {np.bincount(y_train)} (pos/neg)\")\n", "print(f\"Validation: {np.bincount(y_val)} (pos/neg)\")\n", "print(f\"Test: {np.bincount(y_test)} (pos/neg)\")"]}, {"cell_type": "markdown", "id": "9f0560ff", "metadata": {}, "source": ["## Model Training and Evaluation"]}, {"cell_type": "code", "execution_count": 46, "id": "caecaa64", "metadata": {}, "outputs": [], "source": ["# Define models to compare\n", "models = {\n", "    'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10),\n", "    'Gradient Boosting': GradientBoostingClassifier(n_estimators=100, random_state=42, max_depth=6),\n", "    'SVM': SVC(random_state=42, probability=True),\n", "    'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000)\n", "}"]}, {"cell_type": "code", "execution_count": 47, "id": "e66240ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Training Random Forest...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Random Forest Results:\n", "  Train Acc: 0.986, Val Acc: 0.928, Test Acc: 0.926\n", "  Train AUC: 0.999, Val AUC: 0.980, Test AUC: 0.974\n", "\n", "Training Gradient Boosting...\n", "Gradient Boosting Results:\n", "  Train Acc: 1.000, Val Acc: 0.926, Test Acc: 0.933\n", "  Train AUC: 1.000, Val AUC: 0.978, Test AUC: 0.977\n", "\n", "Training SVM...\n", "SVM Results:\n", "  Train Acc: 0.834, Val Acc: 0.782, Test Acc: 0.823\n", "  Train AUC: 0.893, Val AUC: 0.857, Test AUC: 0.865\n", "\n", "Training Logistic Regression...\n", "Logistic Regression Results:\n", "  Train Acc: 0.689, Val Acc: 0.694, Test Acc: 0.711\n", "  Train AUC: 0.683, Val AUC: 0.675, Test AUC: 0.704\n"]}], "source": ["results = {}\n", "\n", "for name, model in models.items():\n", "    print(f\"\\nTraining {name}...\")\n", "    \n", "    # Use scaled data for SVM and Logistic Regression, raw for tree-based\n", "    if name in ['SVM', 'Logistic Regression']:\n", "        X_tr, X_v, X_te = X_train_scaled, X_val_scaled, X_test_scaled\n", "    else:\n", "        X_tr, X_v, X_te = X_train, X_val, X_test\n", "    \n", "    # Train model\n", "    model.fit(X_tr, y_train)\n", "    \n", "    # Generate predictions\n", "    y_train_pred = model.predict(X_tr)\n", "    y_val_pred = model.predict(X_v)\n", "    y_test_pred = model.predict(X_te)\n", "    \n", "    # Get prediction probabilities for AUC\n", "    y_train_proba = model.predict_proba(X_tr)[:, 1]\n", "    y_val_proba = model.predict_proba(X_v)[:, 1]\n", "    y_test_proba = model.predict_proba(X_te)[:, 1]\n", "    \n", "    # Calculate metrics\n", "    train_acc = np.mean(y_train_pred == y_train)\n", "    val_acc = np.mean(y_val_pred == y_val)\n", "    test_acc = np.mean(y_test_pred == y_test)\n", "    \n", "    train_auc = roc_auc_score(y_train, y_train_proba)\n", "    val_auc = roc_auc_score(y_val, y_val_proba)\n", "    test_auc = roc_auc_score(y_test, y_test_proba)\n", "    \n", "    # Store results\n", "    results[name] = {\n", "        'train_acc': train_acc, 'val_acc': val_acc, 'test_acc': test_acc,\n", "        'train_auc': train_auc, 'val_auc': val_auc, 'test_auc': test_auc,\n", "        'model': model, 'test_predictions': y_test_pred, 'test_probabilities': y_test_proba\n", "    }\n", "    \n", "    print(f\"{name} Results:\")\n", "    print(f\"  Train Acc: {train_acc:.3f}, Val Acc: {val_acc:.3f}, Test Acc: {test_acc:.3f}\")\n", "    print(f\"  Train AUC: {train_auc:.3f}, Val AUC: {val_auc:.3f}, Test AUC: {test_auc:.3f}\")"]}, {"cell_type": "markdown", "id": "2341356e", "metadata": {}, "source": ["## Best Model Selection and Analysis"]}, {"cell_type": "code", "execution_count": 48, "id": "674e8ece", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MODEL COMPARISON SUMMARY\n", "============================================================\n", "Best model: <PERSON><PERSON><PERSON>\n", "Test accuracy: 0.933\n", "Test AUC: 0.977\n"]}, {"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Find best model\n", "best_model_name = max(results.keys(), key=lambda x: results[x]['test_acc'])\n", "best_model = results[best_model_name]['model']\n", "\n", "print(\"MODEL COMPARISON SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Best model: {best_model_name}\")\n", "print(f\"Test accuracy: {results[best_model_name]['test_acc']:.3f}\")\n", "print(f\"Test AUC: {results[best_model_name]['test_auc']:.3f}\")\n", "\n", "# Feature importance analysis for tree-based models\n", "if best_model_name in ['Random Forest', 'Gradient Boosting']:\n", "    feature_importance = best_model.feature_importances_\n", "    \n", "    # Plot feature importance\n", "    plt.figure(figsize=(12, 8))\n", "    importance_df = pd.DataFrame({\n", "        'feature': feature_names,\n", "        'importance': feature_importance\n", "    }).sort_values('importance', ascending=True)\n", "    \n", "    plt.barh(range(len(importance_df)), importance_df['importance'])\n", "    plt.yticks(range(len(importance_df)), importance_df['feature'])\n", "    plt.xlabel('Feature Importance')\n", "    plt.title(f'Feature Importance - {best_model_name}')\n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "id": "5856fe4f", "metadata": {}, "source": ["## Confusion Matrix and Classification Report"]}, {"cell_type": "code", "execution_count": 49, "id": "8fb8cdfe", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Classification Report - Gradient Boosting:\n", "              precision    recall  f1-score   support\n", "\n", "     No Pile       0.87      0.92      0.90       131\n", "        <PERSON><PERSON>       0.96      0.94      0.95       287\n", "\n", "    accuracy                           0.93       418\n", "   macro avg       0.92      0.93      0.92       418\n", "weighted avg       0.93      0.93      0.93       418\n", "\n"]}], "source": ["# Generate confusion matrix\n", "best_results = results[best_model_name]\n", "y_pred_best = best_results['test_predictions']\n", "\n", "plt.figure(figsize=(8, 6))\n", "cm = confusion_matrix(y_test, y_pred_best)\n", "sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['<PERSON> <PERSON><PERSON>', '<PERSON><PERSON>'], yticklabels=['No <PERSON><PERSON>', '<PERSON><PERSON>'])\n", "plt.title(f'Confusion Matrix - {best_model_name}')\n", "plt.ylabel('True Label')\n", "plt.xlabel('Predicted Label')\n", "plt.show()\n", "\n", "# Print classification report\n", "print(f\"Classification Report - {best_model_name}:\")\n", "print(classification_report(y_test, y_pred_best, target_names=['<PERSON> Pile', '<PERSON><PERSON>']))"]}, {"cell_type": "markdown", "id": "5d604110", "metadata": {}, "source": ["## Coordinate Reconstruction - Diagnosis"]}, {"cell_type": "code", "execution_count": 50, "id": "117ef7a9", "metadata": {}, "outputs": [], "source": ["def reconstruct_coordinates_from_pile_ids():\n", "    \"\"\"\n", "    Reconstruct spatial coordinates from pile IDs in test metadata.\n", "    \"\"\"\n", "    try:\n", "        # Load original pile dataset\n", "        # TODO: fix path\n", "\n", "        pile_df = pd.read_csv(f'{input_data_path}/harmonized_pile_dataset_final.csv')\n", "        print(f\"Loaded original pile dataset: {len(pile_df)} samples\")\n", "        \n", "        # Load test metadata\n", "        test_metadata = datasets['test']['metadata']\n", "        labels = datasets['test']['labels']\n", "        \n", "        # Reconstruct coordinates by matching pile IDs\n", "        reconstructed_coords = []\n", "        \n", "        for i, (meta, label) in enumerate(zip(test_metadata, labels)):\n", "            pile_id = meta.get('pile_id') if isinstance(meta, dict) else None\n", "            \n", "            if pile_id and pile_id in pile_df['pile_id'].values:\n", "                pile_row = pile_df[pile_df['pile_id'] == pile_id].iloc[0]\n", "                reconstructed_coords.append({\n", "                    'sample_idx': i,\n", "                    'x': pile_row['x'],\n", "                    'y': pile_row['y'],\n", "                    'z': pile_row.get('z', 0),\n", "                    'pile_id': pile_id,\n", "                    'actual_label': label\n", "                })\n", "            else:\n", "                # Use placeholder coordinates for samples without matches\n", "                reconstructed_coords.append({\n", "                    'sample_idx': i, 'x': 0, 'y': 0, 'z': 0,\n", "                    'pile_id': f'unknown_{i}', 'actual_label': label\n", "                })\n", "        \n", "        coords_df = pd.DataFrame(reconstructed_coords)\n", "        \n", "        # Filter out zero coordinates\n", "        valid_coords_df = coords_df[(coords_df['x'] != 0) | (coords_df['y'] != 0)]\n", "        \n", "        print(f\"Reconstructed coordinates: {len(valid_coords_df)}/{len(coords_df)} valid\")\n", "        \n", "        if len(valid_coords_df) > 0:\n", "            print(f\"Coordinate ranges:\")\n", "            print(f\"  X: {valid_coords_df['x'].min():.2f} to {valid_coords_df['x'].max():.2f}\")\n", "            print(f\"  Y: {valid_coords_df['y'].min():.2f} to {valid_coords_df['y'].max():.2f}\")\n", "        \n", "        return valid_coords_df\n", "        \n", "    except FileNotFoundError:\n", "        print(\"Warning: Original pile dataset not found\")\n", "        return None\n", "    except Exception as e:\n", "        print(f\"Error reconstructing coordinates: {e}\")\n", "        return None"]}, {"cell_type": "code", "execution_count": 51, "id": "2ef3fc5f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reconstructing coordinates for spatial analysis...\n", "Loaded original pile dataset: 1348 samples\n", "Reconstructed coordinates: 211/418 valid\n", "Coordinate ranges:\n", "  X: 435305.20 to 436557.26\n", "  Y: 5010921.93 to 5012460.69\n", "Successfully reconstructed 211 coordinates\n", "Coordinate ranges: X[435305, 436557], Y[5010922, 5012461]\n"]}], "source": ["# Run coordinate reconstruction\n", "print(\"Reconstructing coordinates for spatial analysis...\")\n", "coords_df = reconstruct_coordinates_from_pile_ids()\n", "\n", "if coords_df is not None and len(coords_df) > 0:\n", "    print(f\"Successfully reconstructed {len(coords_df)} coordinates\")\n", "    print(f\"Coordinate ranges: X[{coords_df['x'].min():.0f}, {coords_df['x'].max():.0f}], \"\n", "          f\"Y[{coords_df['y'].min():.0f}, {coords_df['y'].max():.0f}]\")\n", "else:\n", "    print(\"Warning: Could not reconstruct coordinates\")"]}, {"cell_type": "markdown", "id": "3db5db29", "metadata": {}, "source": ["## Spatial Predictions Generation"]}, {"cell_type": "code", "execution_count": 52, "id": "3e871b4b", "metadata": {}, "outputs": [], "source": ["def generate_spatial_predictions():\n", "    \"\"\"Generate prediction files with coordinates for all models.\"\"\"\n", "    \n", "    if coords_df is None or len(coords_df) == 0:\n", "        print(\"Warning: No valid coordinates available for spatial analysis\")\n", "        return None, []\n", "    \n", "    # Create comprehensive predictions DataFrame\n", "    predictions_data = []\n", "    \n", "    for i, row in coords_df.iterrows():\n", "        sample_idx = row['sample_idx']\n", "        \n", "        # Get predictions from all models for this sample\n", "        sample_data = {\n", "            'sample_id': sample_idx,\n", "            'pile_id': row['pile_id'],\n", "            'x': row['x'],\n", "            'y': row['y'],\n", "            'z': row['z'],\n", "            'actual_label': row['actual_label']\n", "        }\n", "        \n", "        # Add model predictions and probabilities\n", "        for model_name, model_results in results.items():\n", "            sample_data[f'{model_name}_prediction'] = model_results['test_predictions'][sample_idx]\n", "            sample_data[f'{model_name}_probability'] = model_results['test_probabilities'][sample_idx]\n", "            sample_data[f'{model_name}_correct'] = (\n", "                model_results['test_predictions'][sample_idx] == row['actual_label']\n", "            )\n", "        \n", "        predictions_data.append(sample_data)\n", "    \n", "    # Create comprehensive DataFrame\n", "    predictions_df = pd.DataFrame(predictions_data)\n", "    \n", "    # Save comprehensive CSV\n", "    csv_filename = output_path / 'predictions' / 'ml_predictions_all_models_with_coordinates.csv'\n", "    predictions_df.to_csv(csv_filename, index=False)\n", "    print(f\"Saved comprehensive predictions: {csv_filename}\")\n", "    \n", "    # Generate individual model files\n", "    output_files = [csv_filename]\n", "    \n", "    for model_name in results.keys():\n", "        # Create model-specific DataFrame\n", "        model_cols = [\n", "            'sample_id', 'pile_id', 'x', 'y', 'z', 'actual_label',\n", "            f'{model_name}_prediction', f'{model_name}_probability', f'{model_name}_correct'\n", "        ]\n", "        model_df = predictions_df[model_cols].copy()\n", "        \n", "        # Rename columns for clarity\n", "        model_df = model_df.rename(columns={\n", "            f'{model_name}_prediction': 'predicted_label',\n", "            f'{model_name}_probability': 'pile_probability',\n", "            f'{model_name}_correct': 'correct_prediction'\n", "        })\n", "        \n", "        # Save model-specific CSV\n", "        model_csv = output_path / 'predictions' / f'predictions_{model_name.lower().replace(\" \", \"_\")}.csv'\n", "        model_df.to_csv(model_csv, index=False)\n", "        output_files.append(model_csv)\n", "        \n", "        # Generate KML for predicted piles\n", "        predicted_piles = model_df[model_df['predicted_label'] == 1].copy()\n", "        \n", "        if len(predicted_piles) > 0:\n", "            try:\n", "                # Create GeoDataFrame\n", "                geometry = [Point(x, y) for x, y in zip(predicted_piles['x'], predicted_piles['y'])]\n", "                gdf_predictions = gpd.GeoDataFrame(predicted_piles, geometry=geometry, crs='EPSG:32632')\n", "                \n", "                # Prepare KML-compatible data\n", "                kml_data = gdf_predictions[['pile_id', 'pile_probability', 'actual_label', 'correct_prediction', 'geometry']].copy()\n", "                kml_data['Name'] = kml_data['pile_id'].astype(str)\n", "                kml_data['Description'] = (\n", "                    'Prob: ' + kml_data['pile_probability'].round(3).astype(str) +\n", "                    ' | Actual: ' + kml_data['actual_label'].astype(str) +\n", "                    ' | Correct: ' + kml_data['correct_prediction'].astype(str)\n", "                )\n", "                \n", "                # Keep only essential columns and reproject to WGS84\n", "                kml_final = kml_data[['Name', 'Description', 'geometry']].to_crs('EPSG:4326')\n", "                \n", "                # Save KML\n", "                model_kml = output_path / 'kmls' / f'predicted_piles_{model_name.lower().replace(\" \", \"_\")}.kml'\n", "                kml_final.to_file(model_kml, driver='KML')\n", "                output_files.append(model_kml)\n", "                \n", "                print(f\"{model_name}: {model_csv}, {model_kml} ({len(predicted_piles)} predicted piles)\")\n", "                \n", "            except Exception as e:\n", "                print(f\"Warning: KML export failed for {model_name}: {e}\")\n", "        else:\n", "            print(f\"{model_name}: {model_csv} (no piles predicted)\")\n", "    \n", "    return predictions_df, output_files"]}, {"cell_type": "code", "execution_count": 53, "id": "aa717933", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating spatial predictions...\n", "Saved comprehensive predictions: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/ml_predictions_all_models_with_coordinates.csv\n", "Random Forest: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_random_forest.csv, /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_random_forest.kml (206 predicted piles)\n", "Gradient Boosting: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_gradient_boosting.csv, /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_gradient_boosting.kml (207 predicted piles)\n", "SVM: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_svm.csv, /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_svm.kml (195 predicted piles)\n", "Logistic Regression: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_logistic_regression.csv, /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_logistic_regression.kml (201 predicted piles)\n", "Generated predictions for 211 samples\n", "Output files: [PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/ml_predictions_all_models_with_coordinates.csv'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_random_forest.csv'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_random_forest.kml'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_gradient_boosting.csv'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_gradient_boosting.kml'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_svm.csv'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_svm.kml'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/predictions/predictions_logistic_regression.csv'), PosixPath('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/kmls/predicted_piles_logistic_regression.kml')]\n"]}], "source": ["# Generate spatial prediction files\n", "if coords_df is not None:\n", "    print(\"Generating spatial predictions...\")\n", "    predictions_df, output_files = generate_spatial_predictions()\n", "    \n", "    if predictions_df is not None:\n", "        print(f\"Generated predictions for {len(predictions_df)} samples\")\n", "        print(f\"Output files: {output_files}\")\n", "else:\n", "    print(\"Skipping spatial predictions - no coordinates available\")"]}, {"cell_type": "markdown", "id": "bb001c37", "metadata": {}, "source": ["## Spatial Visualization"]}, {"cell_type": "code", "execution_count": 54, "id": "f0b877b5", "metadata": {}, "outputs": [], "source": ["def create_spatial_visualization():\n", "    \"\"\"Create comprehensive spatial overlay visualization.\"\"\"\n", "    \n", "    if predictions_df is None or len(predictions_df) == 0:\n", "        print(\"Warning: No prediction data available for visualization\")\n", "        return\n", "    \n", "    # Load reference spatial data\n", "    try:\n", "        # Load IFC coordinates\n", "        ifc_df = pd.read_csv('/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv')\n", "        ifc_coords = ifc_df[['X', 'Y']].values\n", "        \n", "        # Load KML coordinates  \n", "        kml_path = \"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/kml/pile.kml\"\n", "        gdf_kml = gpd.read_file(kml_path, driver='KML')\n", "        gdf_kml = gdf_kml.set_crs(epsg=4326).to_crs(epsg=32632)\n", "        gdf_kml['geometry'] = gdf_kml.geometry.centroid\n", "        kml_coords = np.stack([gdf_kml.geometry.x.values, gdf_kml.geometry.y.values], axis=1)\n", "        \n", "        reference_available = True\n", "        print(f\"Reference data loaded: IFC ({len(ifc_coords)}), KML ({len(kml_coords)})\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Warning: Could not load reference data: {e}\")\n", "        reference_available = False\n", "        ifc_coords = None\n", "        kml_coords = None\n", "    \n", "    # Create visualization\n", "    fig, axes = plt.subplots(2, 2, figsize=(20, 16))\n", "    axes = axes.flatten()\n", "    \n", "    model_names = list(results.keys())\n", "    \n", "    for idx, model_name in enumerate(model_names):\n", "        ax = axes[idx]\n", "        \n", "        # Plot reference data if available\n", "        if reference_available:\n", "            if ifc_coords is not None:\n", "                ax.scatter(ifc_coords[::50, 0], ifc_coords[::50, 1], \n", "                          s=0.5, alpha=0.3, color='lightgray', label='IFC (subsampled)')\n", "            \n", "            if kml_coords is not None:\n", "                ax.scatter(kml_coords[:, 0], kml_coords[:, 1], \n", "                          s=8, alpha=0.7, color='green', label='KML Ground Truth')\n", "        \n", "        # Get model predictions\n", "        model_results = results[model_name]\n", "        \n", "        # Map predictions to coordinates\n", "        coords_with_preds = predictions_df.copy()\n", "        sample_indices = coords_with_preds['sample_id'].values\n", "        y_pred_subset = model_results['test_predictions'][sample_indices]\n", "        y_test_subset = coords_with_preds['actual_label'].values\n", "        \n", "        # Calculate accuracy for this subset\n", "        subset_accuracy = np.mean(y_pred_subset == y_test_subset)\n", "        \n", "        # Plot predictions\n", "        correct_mask = (y_pred_subset == y_test_subset)\n", "        incorrect_mask = ~correct_mask\n", "        \n", "        if np.sum(correct_mask) > 0:\n", "            ax.scatter(coords_with_preds.loc[correct_mask, 'x'], \n", "                      coords_with_preds.loc[correct_mask, 'y'],\n", "                      c='blue', s=30, alpha=0.8, marker='o', \n", "                      label=f'Correct ({np.sum(correct_mask)})')\n", "        \n", "        if np.sum(incorrect_mask) > 0:\n", "            ax.scatter(coords_with_preds.loc[incorrect_mask, 'x'], \n", "                      coords_with_preds.loc[incorrect_mask, 'y'],\n", "                      c='red', s=50, alpha=0.9, marker='x', linewidth=2,\n", "                      label=f'Errors ({np.sum(incorrect_mask)})')\n", "        \n", "        # Formatting\n", "        ax.set_title(f'{model_name}\\nAccuracy: {model_results[\"test_acc\"]:.3f}, AUC: {model_results[\"test_auc\"]:.3f}')\n", "        ax.set_xlabel('X (UTM)')\n", "        ax.set_ylabel('Y (UTM)')\n", "        ax.legend(fontsize=8)\n", "        ax.grid(True, alpha=0.3)\n", "        ax.set_aspect('equal', adjustable='box')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(output_path / 'figures' / 'spatial_overlay_all_models.png', dpi=150, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    print(\"Saved spatial overlay: spatial_overlay_all_models.png\")\n"]}, {"cell_type": "code", "execution_count": 55, "id": "47722991", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Creating spatial overlay visualization...\n", "Reference data loaded: IFC (14460), KML (1288)\n"]}, {"data": {"image/png": "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***********************************************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", "text/plain": ["<Figure size 2000x1600 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Saved spatial overlay: spatial_overlay_all_models.png\n"]}], "source": ["# Create spatial overlay\n", "if 'predictions_df' in locals() and predictions_df is not None:\n", "    print(\"Creating spatial overlay visualization...\")\n", "    create_spatial_visualization()\n", "else:\n", "    print(\"Skipping spatial visualization - no prediction data available\")"]}, {"cell_type": "markdown", "id": "4a85ac76", "metadata": {}, "source": ["## Model Persistence and Results Export"]}, {"cell_type": "code", "execution_count": 56, "id": "2dd8a581", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved model: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/models/best_model_gradient_boosting.pkl\n", "Saved results: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/modelling/classical_ml/results/classical_ml_results.json\n"]}], "source": ["# Save best model and scaler\n", "model_filename = output_path / 'models' / f'best_model_{best_model_name.lower().replace(\" \", \"_\")}.pkl'\n", "scaler_filename = output_path / 'models' / 'feature_scaler.pkl'\n", "\n", "joblib.dump(best_model, model_filename)\n", "joblib.dump(scaler, scaler_filename)\n", "\n", "# Save comprehensive results\n", "final_results = {\n", "    'model_comparison': {\n", "        name: {\n", "            'test_accuracy': float(res['test_acc']),\n", "            'test_auc': float(res['test_auc']),\n", "            'validation_accuracy': float(res['val_acc'])\n", "        }\n", "        for name, res in results.items()\n", "    },\n", "    'best_model': {\n", "        'name': best_model_name,\n", "        'test_accuracy': float(results[best_model_name]['test_acc']),\n", "        'test_auc': float(results[best_model_name]['test_auc'])\n", "    },\n", "    'feature_names': feature_names,\n", "    'dataset_info': {\n", "        'n_features': len(feature_names),\n", "        'train_samples': len(X_train),\n", "        'val_samples': len(X_val),\n", "        'test_samples': len(X_test)\n", "    }\n", "}\n", "\n", "results_filename = output_path / 'results' / 'classical_ml_results.json'\n", "with open(results_filename, 'w') as f:\n", "    json.dump(final_results, f, indent=2)\n", "\n", "print(f\"Saved model: {model_filename}\")\n", "print(f\"Saved results: {results_filename}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}