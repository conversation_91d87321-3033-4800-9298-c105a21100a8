!ls -lh ../../../../data/raw

import os
from pathlib import Path
from datetime import datetime

# Parameters
SITE_NAME = "nortan_res"  
SITE_EPSG = "EPSG:32614"        
SITE_CRS = "EPSG:32614"

POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
BUFFER_KML_PATH = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"
OUTPUT_DIR = "output_runs/clean_validation"

# ---- Patch & model params (classical ML) ----
PATCH_RADIUS_M = 3.0  # meters - reduced for reasonable patch sizes
MIN_POINTS_PER_PATCH = 20
TARGET_PATCH_SIZE = 64  # Target points per patch (training had 32-81)

# ---- Negative sampling ----
NEG_BUFFER_M   = 2.0           # stay this far away from known piles (in site CRS)
NEG_MARGIN_M   = 100.0         # margin inside point cloud bbox
NEG_COUNT_MULT = 1.0           # negatives ~= positives; set >1.0 to add more negatives
NEG_MAX_ATTEMPTS = 10000        # max attempts to find valid negative locations

# ---- Confidence filter for geometric-analysis export ----
CONFIDENCE_THRESHOLD = 0.50    # keep detected piles with prob >= this when exporting "detected centers"

print(f"Config loaded for site '{SITE_NAME}' | EPSG={SITE_EPSG}")


import warnings; warnings.filterwarnings('ignore')

import numpy as np
import pandas as pd
import laspy, geopandas as gpd

from shapely.geometry import Point
from scipy.spatial import cKDTree

from sklearn.pipeline import make_pipeline
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

import matplotlib.pyplot as plt


def load_point_cloud_xyz(las_path: str) -> np.ndarray:
    las = laspy.read(las_path)
    pts = np.vstack([las.x, las.y, las.z]).T.astype(np.float32)
    return pts

def load_kml_centroids_projected(kml_path: str, site_epsg: str) -> gpd.GeoDataFrame:
    """Read KML, ensure CRS set (WGS84 default), project to site EPSG, return GeoDataFrame with centroids."""
    gdf = gpd.read_file(kml_path)
    if gdf.crs is None:
        # KML commonly stores in EPSG:4326 (WGS84 lon/lat)
        gdf = gdf.set_crs(4326, allow_override=True)
    gdf = gdf.to_crs(site_epsg)
    # centroid of each geometry (handles Point/Polygon)
    gdf_cent = gdf.copy()
    gdf_cent["geometry"] = gdf_cent.geometry.centroid
    return gdf_cent

def geoms_to_xy_array(gdf_proj: gpd.GeoDataFrame) -> np.ndarray:
    xs = [geom.x for geom in gdf_proj.geometry]
    ys = [geom.y for geom in gdf_proj.geometry]
    return np.c_[xs, ys].astype(np.float32)

print(f"Loading point cloud: {POINT_CLOUD_PATH}")
points = load_point_cloud_xyz(POINT_CLOUD_PATH)
print(f"Loaded {len(points):,} points")

print(f"Loading and projecting Buffer-KML: {BUFFER_KML_PATH} → {SITE_EPSG}")
gdf_piles = load_kml_centroids_projected(BUFFER_KML_PATH, SITE_EPSG)
pile_locations = geoms_to_xy_array(gdf_piles)
print(f"Piles (from KML): {len(pile_locations)}")

# Clip piles to point cloud extent (safety)
pc_xmin, pc_xmax = points[:,0].min(), points[:,0].max()
pc_ymin, pc_ymax = points[:,1].min(), points[:,1].max()
in_pc = (pile_locations[:,0] >= pc_xmin) & (pile_locations[:,0] <= pc_xmax) & \
        (pile_locations[:,1] >= pc_ymin) & (pile_locations[:,1] <= pc_ymax)
pile_locations = pile_locations[in_pc]
print(f"Piles within point-cloud bbox: {len(pile_locations)}")


# Reproject KML coordinates from geographic to UTM Zone 14N
print("\nReprojecting KML coordinates to UTM Zone 14N (EPSG:32614)...")

# Create GeoDataFrame with geographic coordinates
gdf_geo = gpd.GeoDataFrame(
    geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),
    crs='EPSG:4326'  # WGS84 geographic
)

# Reproject to UTM Zone 14N
gdf_utm = gdf_geo.to_crs('EPSG:32614')

# Extract reprojected coordinates
pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])

print(f"Reprojected {len(pile_locations_utm)} pile locations")
print(f"UTM bounds: X[{pile_locations_utm[:, 0].min():.1f}, {pile_locations_utm[:, 0].max():.1f}], Y[{pile_locations_utm[:, 1].min():.1f}, {pile_locations_utm[:, 1].max():.1f}]")

# Check overlap with point cloud
pc_x_min, pc_x_max = points[:, 0].min(), points[:, 0].max()
pc_y_min, pc_y_max = points[:, 1].min(), points[:, 1].max()

x_overlap = (pile_locations_utm[:, 0].min() <= pc_x_max and pile_locations_utm[:, 0].max() >= pc_x_min)
y_overlap = (pile_locations_utm[:, 1].min() <= pc_y_max and pile_locations_utm[:, 1].max() >= pc_y_min)

if x_overlap and y_overlap:
    print("Reprojected coordinates overlap with point cloud!")
    pile_locations_final = pile_locations_utm
else:
    print("Reprojected coordinates don't overlap with point cloud")
    pile_locations_final = pile_locations_utm

print(f"Using {len(pile_locations_final)} pile locations for validation")

def subsample_to_target(patch_xyz: np.ndarray, target_n: int, seed: int=42) -> np.ndarray:
    if len(patch_xyz) <= target_n:
        return patch_xyz
    rng = np.random.default_rng(seed)
    idx = rng.choice(len(patch_xyz), target_n, replace=False)
    return patch_xyz[idx]

def extract_patches(points_xyz: np.ndarray,
                    centers_xy: np.ndarray,
                    radius_m: float = PATCH_RADIUS_M,
                    min_pts: int = MIN_POINTS_PER_PATCH,
                    target_n: int = TARGET_PATCH_SIZE) -> tuple[list, np.ndarray]:
    """Return (list of centered patches Nx3, valid_centers_xy)"""
    tree = cKDTree(points_xyz[:, :2])
    patches, valid_centers = [], []

    for cx, cy in centers_xy:
        idxs = tree.query_ball_point([cx, cy], radius_m)
        if len(idxs) < min_pts:
            continue
        patch = points_xyz[idxs].copy()

        # center in XY around (cx, cy); keep Z structure but stabilize origin
        patch[:, 0] -= cx
        patch[:, 1] -= cy
        # keep Z as-is, but height features will use z - z_min
        patch = subsample_to_target(patch, target_n)
        patches.append(patch)
        valid_centers.append([cx, cy])

    valid_centers = np.array(valid_centers, dtype=np.float32)
    return patches, valid_centers

print("Extracting positive patches…")
pos_patches, pos_centers = extract_patches(points, pile_locations)
print(f"Positives: {len(pos_patches)} / {len(pile_locations)} centers yielded patches")


from shapely.ops import unary_union
from shapely.geometry import Point
import numpy as np

def make_kml_union(gdf_proj: gpd.GeoDataFrame, buffer_m: float) -> object:
    u = unary_union(gdf_proj.geometry)
    return u.buffer(max(0.0, float(buffer_m))) if buffer_m and buffer_m > 0 else u

def _safe_bounds(points_xyz: np.ndarray, margin_m: float):
    xmin, xmax = float(points_xyz[:,0].min()), float(points_xyz[:,0].max())
    ymin, ymax = float(points_xyz[:,1].min()), float(points_xyz[:,1].max())
    width  = max(1e-6, xmax - xmin)
    height = max(1e-6, ymax - ymin)

    # Adapt margin to bbox (<=10% of span each side)
    m = min(margin_m, 0.10*width, 0.10*height)
    # If margin still too big, shrink to 1% span
    if (width - 2*m) <= 0 or (height - 2*m) <= 0:
        m = min(0.01*width, 0.01*height)

    # Final guard: if still degenerate, set to tiny epsilon
    if (width - 2*m) <= 0 or (height - 2*m) <= 0:
        m = 0.0

    return xmin+m, xmax-m, ymin+m, ymax-m

def sample_neg_centers(points_xyz: np.ndarray,
                       kml_union: object,
                       n_target: int,
                       margin_m: float = 100.0,
                       max_tries: int = 20000,
                       seed: int = 123) -> np.ndarray:
    """Sample random XY within PC bbox (minus margin), outside KML union. Adaptive & robust."""
    rng = np.random.default_rng(seed)
    xmin, xmax, ymin, ymax = _safe_bounds(points_xyz, margin_m)

    if not (xmax > xmin and ymax > ymin):
        # Degenerate after margins: use raw bbox
        xmin, xmax = float(points_xyz[:,0].min()), float(points_xyz[:,0].max())
        ymin, ymax = float(points_xyz[:,1].min()), float(points_xyz[:,1].max())

    xs, ys = [], []
    tries = 0
    # Rejection sampling
    while len(xs) < n_target and tries < max_tries:
        tries += 1
        x = rng.uniform(xmin, xmax)
        y = rng.uniform(ymin, ymax)
        if not kml_union.contains(Point(x, y)):
            xs.append(x); ys.append(y)

    # Fallback: coarse grid then filter
    if len(xs) < n_target:
        nx = ny = int(np.ceil(np.sqrt(n_target*1.5)))  # oversample a bit
        gx = np.linspace(xmin, xmax, max(nx, 2))
        gy = np.linspace(ymin, ymax, max(ny, 2))
        cand = np.array([(xx, yy) for xx in gx for yy in gy], dtype=float)
        keep = [not kml_union.contains(Point(cx, cy)) for cx, cy in cand]
        cand = cand[keep]
        if len(cand) > 0:
            need = n_target - len(xs)
            take = cand[:need] if len(cand) >= need else cand
            if len(take) > 0:
                xs.extend(take[:,0].tolist()); ys.extend(take[:,1].tolist())

    return np.c_[xs, ys].astype(np.float32)


kml_union = make_kml_union(gdf_piles, buffer_m=NEG_BUFFER_M)
neg_target = int(max(1, NEG_COUNT_MULT * len(pos_patches)))
neg_centers = sample_neg_centers(points, kml_union, neg_target, margin_m=NEG_MARGIN_M)
print(f"Sampled {len(neg_centers)} negative centers")


from sklearn.neighbors import KDTree
import numpy as np

def extract_patch_at_location(tree, points, center_x, center_y, radius=3.0, num_points=64):
    """
    Extract a fixed-size patch from a point cloud around a given center.
    """
    indices = tree.query_radius([[center_x, center_y]], r=radius)[0]
    if len(indices) < 5:
        return None  # not enough points, skip

    patch_points = points[indices].copy()

    # Sample or pad to fixed size
    if len(patch_points) > num_points:
        sel = np.random.choice(len(patch_points), num_points, replace=False)
        patch_points = patch_points[sel]
    elif len(patch_points) < num_points:
        sel = np.random.choice(len(patch_points), num_points, replace=True)
        patch_points = patch_points[sel]

    # Normalize XY to patch center
    patch_points[:, 0] -= center_x
    patch_points[:, 1] -= center_y

    return patch_points


def extract_features_22(patches: list[np.ndarray]) -> np.ndarray:
    feats = []
    for P in patches:
        x, y, z = P[:,0], P[:,1], P[:,2]
        r = np.sqrt(x**2 + y**2)
        h = z - np.min(z)  # height above local min

        fv = [
            # Spatial stats (9)
            np.mean(x), np.std(x), np.ptp(x),
            np.mean(y), np.std(y), np.ptp(y),
            np.mean(z), np.std(z), np.ptp(z),
            # Height features (4)
            np.mean(h), np.std(h),
            np.percentile(h, 75), np.percentile(h, 25),
            # Radial (4)
            np.mean(r), np.std(r), np.min(r), np.max(r),
            # Shape/density (5)
            len(P),
            np.std(x) / (np.std(y) + 1e-6),
            np.std(z) / (np.std(x)+np.std(y)+1e-6),
            np.percentile(r, 90),
            np.mean(h > np.mean(h)),
        ]
        feats.append(fv)
    return np.array(feats, dtype=np.float32)

print("Extracting negative patches…")
neg_patches, neg_centers_valid = extract_patches(
    points, neg_centers,
    radius_m=PATCH_RADIUS,
    min_pts=MIN_POINTS_PER_PATCH,
    target_n=TARGET_PATCH_SIZE
)
print(f"Negatives: {len(neg_patches)} patches from {len(neg_centers)} centers")

print("Extracting features…")
X_pos = extract_features_22(pos_patches) if pos_patches else np.empty((0, 22))
X_neg = extract_features_22(neg_patches) if neg_patches else np.empty((0, 22))
print(f"X_pos: {X_pos.shape}, X_neg: {X_neg.shape}")


if len(X_pos) == 0 or len(X_neg) == 0:
    raise RuntimeError("Not enough positive/negative patches. Adjust parameters.")

# Build dataset
X = np.vstack([X_pos, X_neg])
y = np.hstack([np.ones(len(X_pos), dtype=int), np.zeros(len(X_neg), dtype=int)])

# Split
X_train, X_test, y_train, y_test = train_test_split(
    X, y, test_size=0.30, random_state=42, stratify=y
)

# Model pipeline (scaler + GBDT)
model = make_pipeline(
    StandardScaler(),
    GradientBoostingClassifier(n_estimators=120, max_depth=6, learning_rate=0.08, random_state=42)
)
model.fit(X_train, y_train)

# Test metrics
y_pred_test = model.predict(X_test)
test_acc = accuracy_score(y_test, y_pred_test)
test_f1  = f1_score(y_test, y_pred_test)
test_pr  = precision_score(y_test, y_pred_test)
test_re  = recall_score(y_test, y_pred_test)

print(f"Test Accuracy:  {test_acc:.3f}")
print(f"Test F1-score:  {test_f1:.3f}")
print(f"Test Precision: {test_pr:.3f}")
print(f"Test Recall:    {test_re:.3f}")

# Validation on all pile locations (positives only)
y_pred_pos = model.predict(X_pos)
y_prob_pos = model.predict_proba(X_pos)[:,1]
det_rate   = float(np.mean(y_pred_pos))
avg_conf   = float(np.mean(y_prob_pos))

print("\nValidation on known piles:")
print(f"Detected as piles: {np.sum(y_pred_pos)}/{len(y_pred_pos)} ({det_rate*100:.1f}%)")
print(f"Average confidence: {avg_conf:.3f}")


results = {
    "site_name": SITE_NAME,
    "timestamp": timestamp,
    "validation_type": "zone_restricted_single_site",
    "crs": SITE_EPSG,
    "patch_params": {
        "radius_m": PATCH_RADIUS_M,
        "min_points": MIN_POINTS_PER_PATCH,
        "target_points": TARGET_PATCH_SIZE
    },
    "negatives": {
        "buffer_m": NEG_BUFFER_M,
        "margin_m": NEG_MARGIN_M,
        "count_mult": NEG_COUNT_MULT
    },
    "metrics": {
        "test_accuracy": float(test_acc),
        "test_f1": float(test_f1),
        "test_precision": float(test_pr),
        "test_recall": float(test_re),
        "pile_detection_rate": det_rate,
        "avg_confidence": avg_conf
    },
    "counts": {
        "pos_patches": int(len(X_pos)),
        "neg_patches": int(len(X_neg))
    }
}

json_path = Path(OUTPUT_DIR) / f"{SITE_NAME}_classical_validation_{timestamp}.json"
with open(json_path, "w") as f:
    import json; json.dump(results, f, indent=2)
print(f"Saved JSON: {json_path}")

# Per-pile CSV in site CRS + WGS84 for GIS
from shapely.geometry import Point
gdf_pos = gpd.GeoDataFrame(
    {"pile_id": np.arange(len(pos_centers)),
     "utm_x": pos_centers[:,0],
     "utm_y": pos_centers[:,1],
     "predicted_pile": y_pred_pos.astype(int),
     "confidence": y_prob_pos,
     "site_name": SITE_NAME,
     "crs": SITE_EPSG,
     "detection_status": np.where(y_pred_pos==1, "Detected","Missed")},
    geometry=[Point(xy) for xy in pos_centers],
    crs=SITE_EPSG
)

gdf_wgs84 = gdf_pos.to_crs(4326)
df_out = gdf_pos.drop(columns="geometry").copy()
df_out["longitude"] = gdf_wgs84.geometry.x
df_out["latitude"]  = gdf_wgs84.geometry.y

csv_path = Path(OUTPUT_DIR) / f"{SITE_NAME}_classical_validation_results_{timestamp}.csv"
df_out.to_csv(csv_path, index=False)
print(f"Saved per-pile CSV: {csv_path}")



# Produces a minimal CSV your geometric-analysis notebook can ingest directly.

# %%
det_mask = (y_pred_pos == 1) & (y_prob_pos >= CONFIDENCE_THRESHOLD)
det_centers = pos_centers[det_mask]

ga_csv = Path(OUTPUT_DIR) / f"{SITE_NAME}_ml_detected_piles_{timestamp}.csv"
pd.DataFrame({
    "utm_x": det_centers[:,0],
    "utm_y": det_centers[:,1],
    "confidence": y_prob_pos[det_mask],
    "site_name": SITE_NAME,
    "crs": SITE_EPSG
}).to_csv(ga_csv, index=False)

print(f"Saved for geometric analysis: {ga_csv}")
print(f"Detected centers: {len(det_centers)} (conf >= {CONFIDENCE_THRESHOLD})")

try:
    plt.figure()
    plt.hist(y_prob_pos, bins=20)
    plt.xlabel("Predicted probability (p=pile) on known piles")
    plt.ylabel("Count"); plt.title(f"{SITE_NAME}: pile probs (pos set)")
    plt.show()
except Exception as e:
    print("Plotting skipped:", e)
