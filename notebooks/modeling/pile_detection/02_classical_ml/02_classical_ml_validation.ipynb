{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# Classical ML Validation - Clean Version\n", "\n", "Validate classical ML models on RES/RCPS sites using Buffer KML pile locations.\n", "\n", "**Approach**: Train new model on validation-style data for proper generalization.\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": 29, "id": "945aaaa9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["total 0\n", "drwxr-xr-x@  7 <USER>  <GROUP>   224B Aug  7 22:04 \u001b[34malthea_rpcs\u001b[m\u001b[m\n", "drwxr-xr-x   8 <USER>  <GROUP>   256B Aug  1 14:25 \u001b[34<PERSON><PERSON>_guay<PERSON>o\u001b[m\u001b[m\n", "drwxr-xr-x@  7 <USER>  <GROUP>   224B Aug  1 14:26 \u001b[34mmotali_de_castro\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Aug  6 16:38 \u001b[34mmudjar_enel\u001b[m\u001b[m\n", "drwxr-xr-x@  4 <USER>  <GROUP>   128B Jul 25 14:35 \u001b[34mnevados\u001b[m\u001b[m\n", "drwxr-xr-x@ 10 <USER>  <GROUP>   320B Aug  7 22:04 \u001b[34mnortan_res\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Jul 25 11:48 \u001b[34<PERSON><PERSON><PERSON>_<PERSON>_giorgio\u001b[m\u001b[m\n", "drwxr-xr-x@  6 <USER>  <GROUP>   192B Aug  6 16:37 \u001b[34msunstreams_mccarthy\u001b[m\u001b[m\n", "drwxr-xr-x@  8 <USER>  <GROUP>   256B Jul 20 10:53 \u001b[34mtrino_enel\u001b[m\u001b[m\n"]}], "source": ["!ls -lh ../../../../data/raw"]}, {"cell_type": "code", "execution_count": 30, "id": "params", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Config loaded for site 'nortan_res' | EPSG=EPSG:32614\n"]}], "source": ["import os\n", "from pathlib import Path\n", "from datetime import datetime\n", "\n", "# Parameters\n", "SITE_NAME = \"nortan_res\"  \n", "SITE_EPSG = \"EPSG:32614\"        \n", "SITE_CRS = \"EPSG:32614\"\n", "\n", "POINT_CLOUD_PATH = \"../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "BUFFER_KML_PATH = \"../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "OUTPUT_DIR = \"output_runs/clean_validation\"\n", "\n", "# ---- Patch & model params (classical ML) ----\n", "PATCH_RADIUS_M = 3.0  # meters - reduced for reasonable patch sizes\n", "MIN_POINTS_PER_PATCH = 20\n", "TARGET_PATCH_SIZE = 64  # Target points per patch (training had 32-81)\n", "\n", "# ---- Negative sampling ----\n", "NEG_BUFFER_M   = 2.0           # stay this far away from known piles (in site CRS)\n", "NEG_MARGIN_M   = 100.0         # margin inside point cloud bbox\n", "NEG_COUNT_MULT = 1.0           # negatives ~= positives; set >1.0 to add more negatives\n", "NEG_MAX_ATTEMPTS = 10000        # max attempts to find valid negative locations\n", "\n", "# ---- Confidence filter for geometric-analysis export ----\n", "CONFIDENCE_THRESHOLD = 0.50    # keep detected piles with prob >= this when exporting \"detected centers\"\n", "\n", "print(f\"Config loaded for site '{SITE_NAME}' | EPSG={SITE_EPSG}\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "imports", "metadata": {}, "outputs": [], "source": ["import warnings; warnings.filterwarnings('ignore')\n", "\n", "import numpy as np\n", "import pandas as pd\n", "import laspy, geopandas as gpd\n", "\n", "from shapely.geometry import Point\n", "from scipy.spatial import cKDTree\n", "\n", "from sklearn.pipeline import make_pipeline\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "\n", "import matplotlib.pyplot as plt\n"]}, {"cell_type": "markdown", "id": "a4a5a5e0", "metadata": {}, "source": ["## 2) I/O Helpers"]}, {"cell_type": "code", "execution_count": 32, "id": "61f21b19", "metadata": {}, "outputs": [], "source": ["def load_point_cloud_xyz(las_path: str) -> np.ndarray:\n", "    las = laspy.read(las_path)\n", "    pts = np.vstack([las.x, las.y, las.z]).T.astype(np.float32)\n", "    return pts\n", "\n", "def load_kml_centroids_projected(kml_path: str, site_epsg: str) -> gpd.GeoDataFrame:\n", "    \"\"\"Read KML, ensure CRS set (WGS84 default), project to site EPSG, return GeoDataFrame with centroids.\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    if gdf.crs is None:\n", "        # KML commonly stores in EPSG:4326 (WGS84 lon/lat)\n", "        gdf = gdf.set_crs(4326, allow_override=True)\n", "    gdf = gdf.to_crs(site_epsg)\n", "    # centroid of each geometry (handles Point/Polygon)\n", "    gdf_cent = gdf.copy()\n", "    gdf_cent[\"geometry\"] = gdf_cent.geometry.centroid\n", "    return gdf_cent\n", "\n", "def geoms_to_xy_array(gdf_proj: gpd.GeoDataFrame) -> np.ndarray:\n", "    xs = [geom.x for geom in gdf_proj.geometry]\n", "    ys = [geom.y for geom in gdf_proj.geometry]\n", "    return np.c_[xs, ys].astype(np.float32)"]}, {"cell_type": "markdown", "id": "a29bc51a", "metadata": {}, "source": ["## 3) Load Data (Point Cloud + Buffer-KML)\n"]}, {"cell_type": "code", "execution_count": 33, "id": "load_data", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud: ../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Loaded 35,565,352 points\n", "Loading and projecting Buffer-KML: ../../../../data/raw/nortan_res/kml/Buffer_2m.kml → EPSG:32614\n", "Piles (from KML): 368\n", "Piles within point-cloud bbox: 368\n"]}], "source": ["print(f\"Loading point cloud: {POINT_CLOUD_PATH}\")\n", "points = load_point_cloud_xyz(POINT_CLOUD_PATH)\n", "print(f\"Loaded {len(points):,} points\")\n", "\n", "print(f\"Loading and projecting Buffer-<PERSON><PERSON>: {BUFFER_KML_PATH} → {SITE_EPSG}\")\n", "gdf_piles = load_kml_centroids_projected(BUFFER_KML_PATH, SITE_EPSG)\n", "pile_locations = geoms_to_xy_array(gdf_piles)\n", "print(f\"Piles (from KML): {len(pile_locations)}\")\n", "\n", "# Clip piles to point cloud extent (safety)\n", "pc_xmin, pc_xmax = points[:,0].min(), points[:,0].max()\n", "pc_ymin, pc_ymax = points[:,1].min(), points[:,1].max()\n", "in_pc = (pile_locations[:,0] >= pc_xmin) & (pile_locations[:,0] <= pc_xmax) & \\\n", "        (pile_locations[:,1] >= pc_ymin) & (pile_locations[:,1] <= pc_ymax)\n", "pile_locations = pile_locations[in_pc]\n", "print(f\"Piles within point-cloud bbox: {len(pile_locations)}\")\n"]}, {"cell_type": "markdown", "id": "5ef869ae", "metadata": {}, "source": ["## 4) Patch Extraction"]}, {"cell_type": "code", "execution_count": 34, "id": "reproject_coordinates", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Reprojecting KML coordinates to UTM Zone 14N (EPSG:32614)...\n", "Reprojected 368 pile locations\n", "UTM bounds: X[inf, inf], Y[inf, inf]\n", "Reprojected coordinates don't overlap with point cloud\n", "Using 368 pile locations for validation\n"]}], "source": ["# Reproject KML coordinates from geographic to UTM Zone 14N\n", "print(\"\\nReprojecting KML coordinates to UTM Zone 14N (EPSG:32614)...\")\n", "\n", "# Create GeoDataFrame with geographic coordinates\n", "gdf_geo = gpd.GeoDataFrame(\n", "    geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "    crs='EPSG:4326'  # WGS84 geographic\n", ")\n", "\n", "# Reproject to UTM Zone 14N\n", "gdf_utm = gdf_geo.to_crs('EPSG:32614')\n", "\n", "# Extract reprojected coordinates\n", "pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "\n", "print(f\"Reprojected {len(pile_locations_utm)} pile locations\")\n", "print(f\"UTM bounds: X[{pile_locations_utm[:, 0].min():.1f}, {pile_locations_utm[:, 0].max():.1f}], Y[{pile_locations_utm[:, 1].min():.1f}, {pile_locations_utm[:, 1].max():.1f}]\")\n", "\n", "# Check overlap with point cloud\n", "pc_x_min, pc_x_max = points[:, 0].min(), points[:, 0].max()\n", "pc_y_min, pc_y_max = points[:, 1].min(), points[:, 1].max()\n", "\n", "x_overlap = (pile_locations_utm[:, 0].min() <= pc_x_max and pile_locations_utm[:, 0].max() >= pc_x_min)\n", "y_overlap = (pile_locations_utm[:, 1].min() <= pc_y_max and pile_locations_utm[:, 1].max() >= pc_y_min)\n", "\n", "if x_overlap and y_overlap:\n", "    print(\"Reprojected coordinates overlap with point cloud!\")\n", "    pile_locations_final = pile_locations_utm\n", "else:\n", "    print(\"Reprojected coordinates don't overlap with point cloud\")\n", "    pile_locations_final = pile_locations_utm\n", "\n", "print(f\"Using {len(pile_locations_final)} pile locations for validation\")"]}, {"cell_type": "code", "execution_count": 35, "id": "extract_patches", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting positive patches…\n", "Positives: 368 / 368 centers yielded patches\n"]}], "source": ["def subsample_to_target(patch_xyz: np.ndarray, target_n: int, seed: int=42) -> np.ndarray:\n", "    if len(patch_xyz) <= target_n:\n", "        return patch_xyz\n", "    rng = np.random.default_rng(seed)\n", "    idx = rng.choice(len(patch_xyz), target_n, replace=False)\n", "    return patch_xyz[idx]\n", "\n", "def extract_patches(points_xyz: np.ndarray,\n", "                    centers_xy: np.n<PERSON><PERSON>,\n", "                    radius_m: float = PATCH_RADIUS_M,\n", "                    min_pts: int = MIN_POINTS_PER_PATCH,\n", "                    target_n: int = TARGET_PATCH_SIZE) -> tuple[list, np.ndarray]:\n", "    \"\"\"Return (list of centered patches Nx3, valid_centers_xy)\"\"\"\n", "    tree = cKDTree(points_xyz[:, :2])\n", "    patches, valid_centers = [], []\n", "\n", "    for cx, cy in centers_xy:\n", "        idxs = tree.query_ball_point([cx, cy], radius_m)\n", "        if len(idxs) < min_pts:\n", "            continue\n", "        patch = points_xyz[idxs].copy()\n", "\n", "        # center in XY around (cx, cy); keep Z structure but stabilize origin\n", "        patch[:, 0] -= cx\n", "        patch[:, 1] -= cy\n", "        # keep Z as-is, but height features will use z - z_min\n", "        patch = subsample_to_target(patch, target_n)\n", "        patches.append(patch)\n", "        valid_centers.append([cx, cy])\n", "\n", "    valid_centers = np.array(valid_centers, dtype=np.float32)\n", "    return patches, valid_centers\n", "\n", "print(\"Extracting positive patches…\")\n", "pos_patches, pos_centers = extract_patches(points, pile_locations)\n", "print(f\"Positives: {len(pos_patches)} / {len(pile_locations)} centers yielded patches\")\n"]}, {"cell_type": "markdown", "id": "69454013", "metadata": {}, "source": ["## 5) Zone-Aware Negative Sampling"]}, {"cell_type": "code", "execution_count": 36, "id": "extract_features", "metadata": {}, "outputs": [], "source": ["from shapely.ops import unary_union\n", "from shapely.geometry import Point\n", "import numpy as np\n", "\n", "def make_kml_union(gdf_proj: gpd.GeoDataFrame, buffer_m: float) -> object:\n", "    u = unary_union(gdf_proj.geometry)\n", "    return u.buffer(max(0.0, float(buffer_m))) if buffer_m and buffer_m > 0 else u\n", "\n", "def _safe_bounds(points_xyz: np.n<PERSON><PERSON>, margin_m: float):\n", "    xmin, xmax = float(points_xyz[:,0].min()), float(points_xyz[:,0].max())\n", "    ymin, ymax = float(points_xyz[:,1].min()), float(points_xyz[:,1].max())\n", "    width  = max(1e-6, xmax - xmin)\n", "    height = max(1e-6, ymax - ymin)\n", "\n", "    # Adapt margin to bbox (<=10% of span each side)\n", "    m = min(margin_m, 0.10*width, 0.10*height)\n", "    # If margin still too big, shrink to 1% span\n", "    if (width - 2*m) <= 0 or (height - 2*m) <= 0:\n", "        m = min(0.01*width, 0.01*height)\n", "\n", "    # Final guard: if still degenerate, set to tiny epsilon\n", "    if (width - 2*m) <= 0 or (height - 2*m) <= 0:\n", "        m = 0.0\n", "\n", "    return xmin+m, xmax-m, ymin+m, ymax-m\n", "\n", "def sample_neg_centers(points_xyz: np.n<PERSON>ray,\n", "                       kml_union: object,\n", "                       n_target: int,\n", "                       margin_m: float = 100.0,\n", "                       max_tries: int = 20000,\n", "                       seed: int = 123) -> np.ndarray:\n", "    \"\"\"Sample random XY within PC bbox (minus margin), outside KML union. Adaptive & robust.\"\"\"\n", "    rng = np.random.default_rng(seed)\n", "    xmin, xmax, ymin, ymax = _safe_bounds(points_xyz, margin_m)\n", "\n", "    if not (xmax > xmin and ymax > ymin):\n", "        # Degenerate after margins: use raw bbox\n", "        xmin, xmax = float(points_xyz[:,0].min()), float(points_xyz[:,0].max())\n", "        ymin, ymax = float(points_xyz[:,1].min()), float(points_xyz[:,1].max())\n", "\n", "    xs, ys = [], []\n", "    tries = 0\n", "    # Rejection sampling\n", "    while len(xs) < n_target and tries < max_tries:\n", "        tries += 1\n", "        x = rng.uniform(xmin, xmax)\n", "        y = rng.uniform(ymin, ymax)\n", "        if not kml_union.contains(Point(x, y)):\n", "            xs.append(x); ys.append(y)\n", "\n", "    # Fallback: coarse grid then filter\n", "    if len(xs) < n_target:\n", "        nx = ny = int(np.ceil(np.sqrt(n_target*1.5)))  # oversample a bit\n", "        gx = np.linspace(xmin, xmax, max(nx, 2))\n", "        gy = np.linspace(ymin, ymax, max(ny, 2))\n", "        cand = np.array([(xx, yy) for xx in gx for yy in gy], dtype=float)\n", "        keep = [not kml_union.contains(Point(cx, cy)) for cx, cy in cand]\n", "        cand = cand[keep]\n", "        if len(cand) > 0:\n", "            need = n_target - len(xs)\n", "            take = cand[:need] if len(cand) >= need else cand\n", "            if len(take) > 0:\n", "                xs.extend(take[:,0].tolist()); ys.extend(take[:,1].tolist())\n", "\n", "    return np.c_[xs, ys].astype(np.float32)\n"]}, {"cell_type": "code", "execution_count": 37, "id": "0fa19843", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sampled 368 negative centers\n"]}], "source": ["kml_union = make_kml_union(gdf_piles, buffer_m=NEG_BUFFER_M)\n", "neg_target = int(max(1, NEG_COUNT_MULT * len(pos_patches)))\n", "neg_centers = sample_neg_centers(points, kml_union, neg_target, margin_m=NEG_MARGIN_M)\n", "print(f\"Sampled {len(neg_centers)} negative centers\")\n"]}, {"cell_type": "markdown", "id": "adf3e986", "metadata": {}, "source": ["## Feature Extraction "]}, {"cell_type": "code", "execution_count": 38, "id": "26807d47", "metadata": {}, "outputs": [], "source": ["from sklearn.neighbors import KDTree\n", "import numpy as np\n", "\n", "def extract_patch_at_location(tree, points, center_x, center_y, radius=3.0, num_points=64):\n", "    \"\"\"\n", "    Extract a fixed-size patch from a point cloud around a given center.\n", "    \"\"\"\n", "    indices = tree.query_radius([[center_x, center_y]], r=radius)[0]\n", "    if len(indices) < 5:\n", "        return None  # not enough points, skip\n", "\n", "    patch_points = points[indices].copy()\n", "\n", "    # Sample or pad to fixed size\n", "    if len(patch_points) > num_points:\n", "        sel = np.random.choice(len(patch_points), num_points, replace=False)\n", "        patch_points = patch_points[sel]\n", "    elif len(patch_points) < num_points:\n", "        sel = np.random.choice(len(patch_points), num_points, replace=True)\n", "        patch_points = patch_points[sel]\n", "\n", "    # Normalize XY to patch center\n", "    patch_points[:, 0] -= center_x\n", "    patch_points[:, 1] -= center_y\n", "\n", "    return patch_points\n"]}, {"cell_type": "code", "execution_count": 39, "id": "8cb8d68f", "metadata": {}, "outputs": [], "source": ["def extract_features_22(patches: list[np.ndarray]) -> np.ndarray:\n", "    feats = []\n", "    for P in patches:\n", "        x, y, z = P[:,0], P[:,1], P[:,2]\n", "        r = np.sqrt(x**2 + y**2)\n", "        h = z - np.min(z)  # height above local min\n", "\n", "        fv = [\n", "            # Spatial stats (9)\n", "            np.mean(x), np.std(x), np.ptp(x),\n", "            np.mean(y), np.std(y), np.ptp(y),\n", "            np.mean(z), np.std(z), np.ptp(z),\n", "            # Height features (4)\n", "            np.mean(h), np.std(h),\n", "            np.percentile(h, 75), np.percentile(h, 25),\n", "            # Radial (4)\n", "            np.mean(r), np.std(r), np.min(r), np.max(r),\n", "            # Shape/density (5)\n", "            len(P),\n", "            np.std(x) / (np.std(y) + 1e-6),\n", "            np.std(z) / (np.std(x)+np.std(y)+1e-6),\n", "            np.percentile(r, 90),\n", "            np.mean(h > np.mean(h)),\n", "        ]\n", "        feats.append(fv)\n", "    return np.array(feats, dtype=np.float32)"]}, {"cell_type": "code", "execution_count": 40, "id": "7f16d949", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Extracting negative patches…\n", "Negatives: 352 patches from 368 centers\n", "Extracting features…\n", "X_pos: (368, 22), X_neg: (352, 22)\n"]}], "source": ["print(\"Extracting negative patches…\")\n", "neg_patches, neg_centers_valid = extract_patches(\n", "    points, neg_centers,\n", "    radius_m=PATCH_RADIUS,\n", "    min_pts=MIN_POINTS_PER_PATCH,\n", "    target_n=TARGET_PATCH_SIZE\n", ")\n", "print(f\"Negatives: {len(neg_patches)} patches from {len(neg_centers)} centers\")\n", "\n", "print(\"Extracting features…\")\n", "X_pos = extract_features_22(pos_patches) if pos_patches else np.empty((0, 22))\n", "X_neg = extract_features_22(neg_patches) if neg_patches else np.empty((0, 22))\n", "print(f\"X_pos: {X_pos.shape}, X_neg: {X_neg.shape}\")\n"]}, {"cell_type": "markdown", "id": "1d237f2f", "metadata": {}, "source": ["# 7) Train/Test & Validation on Known Piles"]}, {"cell_type": "code", "execution_count": 41, "id": "train_and_validate", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Test Accuracy:  1.000\n", "Test F1-score:  1.000\n", "Test Precision: 1.000\n", "Test Recall:    1.000\n", "\n", "Validation on known piles:\n", "Detected as piles: 368/368 (100.0%)\n", "Average confidence: 1.000\n"]}], "source": ["if len(X_pos) == 0 or len(X_neg) == 0:\n", "    raise RuntimeError(\"Not enough positive/negative patches. Adjust parameters.\")\n", "\n", "# Build dataset\n", "X = np.vstack([X_pos, X_neg])\n", "y = np.hstack([np.ones(len(X_pos), dtype=int), np.zeros(len(X_neg), dtype=int)])\n", "\n", "# Split\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.30, random_state=42, stratify=y\n", ")\n", "\n", "# Model pipeline (scaler + GBDT)\n", "model = make_pipeline(\n", "    StandardScaler(),\n", "    GradientBoostingClassifier(n_estimators=120, max_depth=6, learning_rate=0.08, random_state=42)\n", ")\n", "model.fit(X_train, y_train)\n", "\n", "# Test metrics\n", "y_pred_test = model.predict(X_test)\n", "test_acc = accuracy_score(y_test, y_pred_test)\n", "test_f1  = f1_score(y_test, y_pred_test)\n", "test_pr  = precision_score(y_test, y_pred_test)\n", "test_re  = recall_score(y_test, y_pred_test)\n", "\n", "print(f\"Test Accuracy:  {test_acc:.3f}\")\n", "print(f\"Test F1-score:  {test_f1:.3f}\")\n", "print(f\"Test Precision: {test_pr:.3f}\")\n", "print(f\"Test Recall:    {test_re:.3f}\")\n", "\n", "# Validation on all pile locations (positives only)\n", "y_pred_pos = model.predict(X_pos)\n", "y_prob_pos = model.predict_proba(X_pos)[:,1]\n", "det_rate   = float(np.mean(y_pred_pos))\n", "avg_conf   = float(np.mean(y_prob_pos))\n", "\n", "print(\"\\nValidation on known piles:\")\n", "print(f\"Detected as piles: {np.sum(y_pred_pos)}/{len(y_pred_pos)} ({det_rate*100:.1f}%)\")\n", "print(f\"Average confidence: {avg_conf:.3f}\")\n"]}, {"cell_type": "markdown", "id": "c4e600f3", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 42, "id": "false_positive_test", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved JSON: output_runs/clean_validation/nortan_res_classical_validation_20250815_101635.json\n", "Saved per-pile CSV: output_runs/clean_validation/nortan_res_classical_validation_results_20250815_101635.csv\n"]}], "source": ["results = {\n", "    \"site_name\": SITE_NAME,\n", "    \"timestamp\": timestamp,\n", "    \"validation_type\": \"zone_restricted_single_site\",\n", "    \"crs\": SITE_EPSG,\n", "    \"patch_params\": {\n", "        \"radius_m\": PATCH_RADIUS_M,\n", "        \"min_points\": MIN_POINTS_PER_PATCH,\n", "        \"target_points\": TARGET_PATCH_SIZE\n", "    },\n", "    \"negatives\": {\n", "        \"buffer_m\": NEG_BUFFER_M,\n", "        \"margin_m\": NEG_MARGIN_M,\n", "        \"count_mult\": NEG_COUNT_MULT\n", "    },\n", "    \"metrics\": {\n", "        \"test_accuracy\": float(test_acc),\n", "        \"test_f1\": float(test_f1),\n", "        \"test_precision\": float(test_pr),\n", "        \"test_recall\": float(test_re),\n", "        \"pile_detection_rate\": det_rate,\n", "        \"avg_confidence\": avg_conf\n", "    },\n", "    \"counts\": {\n", "        \"pos_patches\": int(len(X_pos)),\n", "        \"neg_patches\": int(len(X_neg))\n", "    }\n", "}\n", "\n", "json_path = Path(OUTPUT_DIR) / f\"{SITE_NAME}_classical_validation_{timestamp}.json\"\n", "with open(json_path, \"w\") as f:\n", "    import json; json.dump(results, f, indent=2)\n", "print(f\"Saved JSON: {json_path}\")\n", "\n", "# Per-pile CSV in site CRS + WGS84 for GIS\n", "from shapely.geometry import Point\n", "gdf_pos = gpd.GeoDataFrame(\n", "    {\"pile_id\": np.arange(len(pos_centers)),\n", "     \"utm_x\": pos_centers[:,0],\n", "     \"utm_y\": pos_centers[:,1],\n", "     \"predicted_pile\": y_pred_pos.astype(int),\n", "     \"confidence\": y_prob_pos,\n", "     \"site_name\": SITE_NAME,\n", "     \"crs\": SITE_EPSG,\n", "     \"detection_status\": np.where(y_pred_pos==1, \"Detected\",\"Missed\")},\n", "    geometry=[Point(xy) for xy in pos_centers],\n", "    crs=SITE_EPSG\n", ")\n", "\n", "gdf_wgs84 = gdf_pos.to_crs(4326)\n", "df_out = gdf_pos.drop(columns=\"geometry\").copy()\n", "df_out[\"longitude\"] = gdf_wgs84.geometry.x\n", "df_out[\"latitude\"]  = gdf_wgs84.geometry.y\n", "\n", "csv_path = Path(OUTPUT_DIR) / f\"{SITE_NAME}_classical_validation_results_{timestamp}.csv\"\n", "df_out.to_csv(csv_path, index=False)\n", "print(f\"Saved per-pile CSV: {csv_path}\")\n", "\n"]}, {"cell_type": "markdown", "id": "73b64910", "metadata": {}, "source": ["## 9) Export \"Detected Pile Centers\" for Geometric Analysis"]}, {"cell_type": "code", "execution_count": 43, "id": "7661750c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved for geometric analysis: output_runs/clean_validation/nortan_res_ml_detected_piles_20250815_101635.csv\n", "Detected centers: 368 (conf >= 0.5)\n"]}], "source": ["# Produces a minimal CSV your geometric-analysis notebook can ingest directly.\n", "\n", "# %%\n", "det_mask = (y_pred_pos == 1) & (y_prob_pos >= CONFIDENCE_THRESHOLD)\n", "det_centers = pos_centers[det_mask]\n", "\n", "ga_csv = Path(OUTPUT_DIR) / f\"{SITE_NAME}_ml_detected_piles_{timestamp}.csv\"\n", "pd.DataFrame({\n", "    \"utm_x\": det_centers[:,0],\n", "    \"utm_y\": det_centers[:,1],\n", "    \"confidence\": y_prob_pos[det_mask],\n", "    \"site_name\": SITE_NAME,\n", "    \"crs\": SITE_EPSG\n", "}).to_csv(ga_csv, index=False)\n", "\n", "print(f\"Saved for geometric analysis: {ga_csv}\")\n", "print(f\"Detected centers: {len(det_centers)} (conf >= {CONFIDENCE_THRESHOLD})\")"]}, {"cell_type": "markdown", "id": "e22118f9", "metadata": {}, "source": ["## 10) Quick sanity plots (optional)"]}, {"cell_type": "code", "execution_count": 44, "id": "ee7c807f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["try:\n", "    plt.figure()\n", "    plt.hist(y_prob_pos, bins=20)\n", "    plt.xlabel(\"Predicted probability (p=pile) on known piles\")\n", "    plt.ylabel(\"Count\"); plt.title(f\"{SITE_NAME}: pile probs (pos set)\")\n", "    plt.show()\n", "except Exception as e:\n", "    print(\"Plotting skipped:\", e)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}