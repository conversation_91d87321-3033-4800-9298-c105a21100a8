# Site names
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# Data paths
RES_POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH  = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

RCPS_POINT_CLOUD_PATH = "../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH  = "../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

# Optional: strategic negatives for RES (UTM X/Y columns: utm_x, utm_y)
RES_STRATEGIC_NEG_CSV = "../../../../data/ground_truth/nortan_res_negative_samples.csv"

# Output
OUTPUT_DIR = "output_runs/true_generalization"

# === Speed & run flags ===
SPEED_MODE = "fast"          # "balanced" or "fast"
RUN_PRECISION_PROBE = True   # set False for routine runs
PROBE_NEG_MAX = 400          # cap negatives for RCPS precision probe (lower to run faster)

# Patch & sampling (defaults; overridden by preset below)
PATCH_RADIUS = 3.0                # meters (same for pos/neg)
MIN_POINTS   = 20                 # before masking
MIN_POINTS_AFTER_MASK = 15        # after masking for negatives
TARGET_PATCH_SIZE = 64
CORE_EXCLUDE_RADIUS = 0.40        # meters: remove pile core points for negative features
RING_INNER_MARGIN  = 0.60         # min offset from core when placing neg centers
RING_OUTER_MARGIN  = 0.20         # keep center inside coverage radius by this margin
MAX_PER_PILE = 2
MAX_ANGLE_TRIALS = 30

if SPEED_MODE == "fast":
    MAX_PER_PILE = 1
    MAX_ANGLE_TRIALS = 12
    MIN_POINTS_AFTER_MASK = 12
    TARGET_PATCH_SIZE = 48
    CORE_EXCLUDE_RADIUS = 0.35
    RING_INNER_MARGIN = 0.80
    PROBE_NEG_MAX = min(PROBE_NEG_MAX, 400)

# Model & eval
GB_PARAMS = dict(n_estimators=150, max_depth=6, learning_rate=0.08, random_state=42)
THRESH_GRID = [round(x, 2) for x in list(__import__("numpy").linspace(0.2, 0.9, 28))]  # fewer steps for speed

# Logging
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
out_dir = Path(OUTPUT_DIR); out_dir.mkdir(parents=True, exist_ok=True)


# Plots
SAVE_PLOTS = True
SHOW_PLOTS = False  # set True to pop up windows
if SAVE_PLOTS and not SHOW_PLOTS:
    import matplotlib
    matplotlib.use("Agg")  # safe headless backend

import json
import numpy as np
import pandas as pd
import joblib
import laspy
import geopandas as gpd
from shapely.geometry import Point
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import (
    precision_recall_fscore_support,
    accuracy_score,
    precision_recall_curve,
    confusion_matrix,
)
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def rng_for(i: int, base_seed: int = 42):
    return np.random.default_rng(base_seed + int(i))

def utm_epsg_from_lonlat(lon: float, lat: float) -> str:
    zone = int(np.floor((lon + 180) / 6) + 1)
    north = lat >= 0
    return f"EPSG:{326 if north else 327}{zone:02d}"

def load_and_reproject_kml_to_utm_points(kml_path: str | Path) -> tuple[np.ndarray, str]:
    gdf = gpd.read_file(kml_path)
    pts_ll = []
    for geom in gdf.geometry:
        if geom is None: continue
        if geom.geom_type == "Point":
            pts_ll.append([geom.x, geom.y])
        elif geom.geom_type in ("Polygon","MultiPolygon","LineString","MultiLineString"):
            c = geom.centroid; pts_ll.append([c.x, c.y])
    if not pts_ll:
        return np.empty((0,2), float), "EPSG:4326"
    pts_ll = np.asarray(pts_ll, float)
    mean_lon, mean_lat = float(np.mean(pts_ll[:,0])), float(np.mean(pts_ll[:,1]))
    utm = utm_epsg_from_lonlat(mean_lon, mean_lat)
    gdf_ll = gpd.GeoDataFrame(geometry=gpd.points_from_xy(pts_ll[:,0], pts_ll[:,1]), crs="EPSG:4326")
    gdf_utm = gdf_ll.to_crs(utm)
    pts_utm = np.array([[p.x, p.y] for p in gdf_utm.geometry], float)
    return pts_utm, utm

def read_las_points_xyz(las_path: str | Path) -> np.ndarray:
    las = laspy.read(las_path)
    return np.vstack([las.x, las.y, las.z]).T.astype(np.float32, copy=False)

def subsample_patch(patch_points: np.ndarray, target_size: int, idx_seed: int) -> np.ndarray:
    n = len(patch_points)
    if n <= target_size: return patch_points
    rs = rng_for(idx_seed)
    idx = rs.choice(n, size=target_size, replace=False)
    return patch_points[idx]

def extract_patches(points_xyz: np.ndarray,
                    locations_xy: np.ndarray,
                    radius: float,
                    min_points: int,
                    target_size: int,
                    tree_points_xy: cKDTree | None = None) -> tuple[list[np.ndarray], np.ndarray]:
    if len(points_xyz) == 0 or len(locations_xy) == 0:
        return [], np.empty((0,2), float)
    tree = tree_points_xy or cKDTree(points_xyz[:, :2])
    patches, kept_locs = [], []
    for i, loc in enumerate(locations_xy):
        idxs = tree.query_ball_point(loc, radius)
        if len(idxs) >= min_points:
            patch = points_xyz[np.asarray(idxs)]
            center = np.array([loc[0], loc[1], float(np.mean(patch[:,2]))], np.float32)
            patch_c = patch - center
            patch_c = subsample_patch(patch_c, target_size, idx_seed=i)
            patches.append(patch_c.astype(np.float32, copy=False))
            kept_locs.append(loc)
    return patches, np.asarray(kept_locs, float)

def extract_features(patches):
    """Exactly 22 features (thesis baseline)."""
    feats, eps = [], 1e-6
    for patch in patches:
        x, y, z = patch[:,0], patch[:,1], patch[:,2]
        r = np.sqrt(x**2 + y**2)
        zmin = np.min(z); h = z - zmin
        feats.append([
            # Basic spatial (9)
            float(np.mean(x)), float(np.std(x)), float(np.ptp(x)),
            float(np.mean(y)), float(np.std(y)), float(np.ptp(y)),
            float(np.mean(z)), float(np.std(z)), float(np.ptp(z)),
            # Height (4)
            float(np.mean(h)), float(np.std(h)),
            float(np.percentile(h, 75)), float(np.percentile(h, 25)),
            # Radial (4)
            float(np.mean(r)), float(np.std(r)), float(np.min(r)), float(np.max(r)),
            # Shape/density (5)
            float(len(patch)),
            float(np.std(x) / (np.std(y) + eps)),
            float(np.std(z) / (np.std(x) + np.std(y) + eps)),
            float(np.percentile(r, 90)),
            float(np.mean(h > np.mean(h))),
        ])
    X = np.asarray(feats, np.float32)
    if X.size and X.shape[1] != 22:
        raise RuntimeError(f"Feature drift: expected 22, got {X.shape[1]}")
    return X

def mask_core_points(patch_points_xyz: np.ndarray,
                     tree_piles: cKDTree,
                     core_radius: float) -> np.ndarray:
    """Remove points within core_radius (in XY) of any pile center."""
    if len(patch_points_xyz) == 0:
        return patch_points_xyz
    d, _ = tree_piles.query(patch_points_xyz[:, :2], k=1)
    return patch_points_xyz[d > core_radius]

def estimate_coverage_radius(tree_points: cKDTree, points_xy: np.ndarray,
                             center_xy: np.ndarray, search_radius: float) -> float:
    """Rough local coverage radius based on 95th percentile distance of points near the center."""
    idxs = tree_points.query_ball_point(center_xy, r=search_radius)
    if not idxs: return search_radius
    dists = np.linalg.norm(points_xy[idxs] - center_xy, axis=1)
    if len(dists) < 8: return search_radius
    return float(np.percentile(dists, 95))

def sample_negatives_with_core_mask(points_xyz: np.ndarray,
                                    tree_points: cKDTree,
                                    tree_piles: cKDTree,
                                    pile_centers_xy: np.ndarray,
                                    n_needed: int,
                                    radius: float,
                                    min_points_before: int,
                                    min_points_after: int,
                                    core_radius: float,
                                    ring_inner_margin: float,
                                    ring_outer_margin: float,
                                    max_per_pile: int,
                                    max_angle_trials: int,
                                    seed: int = 99) -> tuple[list[np.ndarray], np.ndarray]:
    """
    For each pile center, place candidate centers within the buffer ring, extract patches,
    mask out core points, and keep patches with enough remaining points.
    Returns (patches, centers_xy_kept).
    """
    rs = np.random.default_rng(seed)
    pts_xy = points_xyz[:, :2]

    patches, centers = [], []
    per_pile_counts = np.zeros(len(pile_centers_xy), dtype=int)

    order = np.arange(len(pile_centers_xy))
    rs.shuffle(order)

    for idx in order:
        if len(patches) >= n_needed: break
        c = pile_centers_xy[idx]
        if per_pile_counts[idx] >= max_per_pile: continue

        cov_r = estimate_coverage_radius(tree_points, pts_xy, c, search_radius=radius*2.0)
        r_min = core_radius + ring_inner_margin
        r_max = max(r_min + 0.15, cov_r - ring_outer_margin)
        got_for_this_pile = 0

        for _ in range(max_angle_trials):
            if len(patches) >= n_needed or got_for_this_pile >= max_per_pile: break
            theta = rs.uniform(0, 2*np.pi)
            rr = rs.uniform(r_min, r_max)
            center_xy = c + np.array([rr*np.cos(theta), rr*np.sin(theta)])

            idxs = tree_points.query_ball_point(center_xy, r=radius)
            if len(idxs) < min_points_before: continue
            patch = points_xyz[np.asarray(idxs)]

            patch_masked = mask_core_points(patch, tree_piles, core_radius)
            if len(patch_masked) < min_points_after: continue

            center_z = float(np.mean(patch_masked[:,2]))
            centered = patch_masked - np.array([center_xy[0], center_xy[1], center_z], np.float32)
            centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=len(patches))
            patches.append(centered.astype(np.float32, copy=False))
            centers.append(center_xy)
            got_for_this_pile += 1
            per_pile_counts[idx] += 1

    return patches, (np.asarray(centers, float) if centers else np.empty((0,2), float))

def features_from_core_masked_xy(points_xyz: np.ndarray,
                                 tree_points: cKDTree,
                                 tree_piles: cKDTree,
                                 centers_xy: np.ndarray,
                                 radius: float,
                                 min_points_before: int,
                                 min_points_after: int,
                                 core_radius: float) -> tuple[np.ndarray, list[np.ndarray]]:
    """Extract 22-feature vectors from given centers with core masking (tree reuse)."""
    if len(centers_xy) == 0:
        return np.empty((0,22), np.float32), []
    X_list, patches_list = [], []
    for i, center_xy in enumerate(centers_xy):
        idxs = tree_points.query_ball_point(center_xy, r=radius)
        if len(idxs) < min_points_before: continue
        patch = points_xyz[np.asarray(idxs)]
        patch_m = mask_core_points(patch, tree_piles, core_radius)
        if len(patch_m) < min_points_after: continue

        center_z = float(np.mean(patch_m[:,2]))
        centered = patch_m - np.array([center_xy[0], center_xy[1], center_z], np.float32)
        centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=i+1234)
        X_list.append(extract_features([centered])[0])
        patches_list.append(centered)
    X = np.asarray(X_list, np.float32) if X_list else np.empty((0,22), np.float32)
    return X, patches_list

def tune_threshold(y_true: np.ndarray, y_prob: np.ndarray, grid: list[float]):
    best = dict(threshold=0.5, f1=-1.0, precision=0.0, recall=0.0, accuracy=0.0)
    y_true = np.asarray(y_true).astype(int)
    for t in grid:
        y_pred = (y_prob >= t).astype(int)
        p, r, f1, _ = precision_recall_fscore_support(y_true, y_pred, average="binary", zero_division=0)
        acc = accuracy_score(y_true, y_pred)
        if f1 > best["f1"]:
            best.update(dict(threshold=float(t), f1=float(f1), precision=float(p), recall=float(r), accuracy=float(acc)))
    return best

def save_fig(fig, name: str):
    if not SAVE_PLOTS:
        if SHOW_PLOTS: plt.show()
        else: plt.close(fig)
        return
    path = out_dir / f"{name}_{timestamp}.png"
    fig.tight_layout()
    fig.savefig(path, dpi=150)
    if SHOW_PLOTS:
        plt.show()
    else:
        plt.close(fig)
    print(f"[PLOT] saved → {path}")

# Load points & piles (RES)
res_pts = read_las_points_xyz(RES_POINT_CLOUD_PATH)
print(f"[RES] points: {len(res_pts):,}")

res_piles_xy, res_epsg = load_and_reproject_kml_to_utm_points(RES_BUFFER_KML_PATH)
print(f"[RES] pile locations: {len(res_piles_xy)}  CRS={res_epsg}")

# Build KD-trees once (speed)
res_tree_pts = cKDTree(res_pts[:, :2])
res_tree_piles = cKDTree(res_piles_xy)

# Positive patches
res_pos_patches, res_pos_locs = extract_patches(
    res_pts, res_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=res_tree_pts
)
print(f"[RES] positive patches: {len(res_pos_patches)}")
if len(res_pos_patches) == 0:
    raise RuntimeError("No positive patches on RES; check KML / radius / min_points.")
n_pos_full = len(res_pos_patches)

# Strategic negatives (locations) → core-masked features
neg_from_csv = None
try:
    df_neg = pd.read_csv(RES_STRATEGIC_NEG_CSV)
    if {"utm_x","utm_y"}.issubset(df_neg.columns):
        neg_from_csv = df_neg[["utm_x","utm_y"]].to_numpy(float)
        print(f"[RES] strategic negatives loaded (locations): {len(neg_from_csv)}")
    else:
        print("[RES] strategic negatives CSV missing 'utm_x','utm_y' → ignored")
except FileNotFoundError:
    print("[RES] strategic negatives CSV not found → skipping")

X_neg_csv, neg_csv_patches = (np.empty((0,22), np.float32), [])
if neg_from_csv is not None and len(neg_from_csv):
    X_neg_csv, neg_csv_patches = features_from_core_masked_xy(
        res_pts, res_tree_pts, res_tree_piles, neg_from_csv,
        radius=PATCH_RADIUS,
        min_points_before=MIN_POINTS,
        min_points_after=MIN_POINTS_AFTER_MASK,
        core_radius=CORE_EXCLUDE_RADIUS,
    )
    print(f"[RES] negatives from strategic_csv (usable after mask): {len(X_neg_csv)}")


# Within-buffer core-masked negatives (top-up to match positives)
need = max(0, n_pos_full - len(X_neg_csv))
neg_patches_extra, _ = sample_negatives_with_core_mask(
    points_xyz=res_pts,
    tree_points=res_tree_pts,
    tree_piles=res_tree_piles,
    pile_centers_xy=res_piles_xy,
    n_needed=need,
    radius=PATCH_RADIUS,
    min_points_before=MIN_POINTS,
    min_points_after=MIN_POINTS_AFTER_MASK,
    core_radius=CORE_EXCLUDE_RADIUS,
    ring_inner_margin=RING_INNER_MARGIN,
    ring_outer_margin=RING_OUTER_MARGIN,
    max_per_pile=MAX_PER_PILE,
    max_angle_trials=MAX_ANGLE_TRIALS,
    seed=77,
)
X_neg_extra = extract_features(neg_patches_extra) if len(neg_patches_extra) else np.empty((0,22), np.float32)
print(f"[RES] negatives from within-buffer sampler: {len(X_neg_extra)}")

# Final negatives + balance
X_pos_res = extract_features(res_pos_patches)
X_neg_res = np.vstack([arr for arr in (X_neg_csv, X_neg_extra) if len(arr)]) if (len(X_neg_csv) or len(X_neg_extra)) else np.empty((0,22), np.float32)
if len(X_neg_res) == 0:
    raise RuntimeError("No usable negatives; tune CORE_EXCLUDE_RADIUS / RING margins / MIN_POINTS_AFTER_MASK.")

n_use = min(len(X_pos_res), len(X_neg_res))
X_pos_res, X_neg_res = X_pos_res[:n_use], X_neg_res[:n_use]
print(f"[RES] using balanced features → pos={len(X_pos_res)}, neg={len(X_neg_res)}")


# Train
X_train_res = np.vstack([X_pos_res, X_neg_res]).astype(np.float32)
y_train_res = np.hstack([np.ones(len(X_pos_res), int), np.zeros(len(X_neg_res), int)])
print(f"[RES] train samples: {len(X_train_res)} | features: {X_train_res.shape[1]}")

model = GradientBoostingClassifier(**GB_PARAMS).fit(X_train_res, y_train_res)

# Threshold tuning on RES
y_prob_res = model.predict_proba(X_train_res)[:,1]
best = tune_threshold(y_train_res, y_prob_res, THRESH_GRID)
print(f"[RES] tuned threshold = {best['threshold']:.2f} | P={best['precision']:.2f} R={best['recall']:.2f} F1={best['f1']:.2f} Acc={best['accuracy']:.2f}")

# ---- PLOTS: RES PR curve, confusion matrix, and prob histograms ----
# PR curve (RES)
prec, rec, thr = precision_recall_curve(y_train_res, y_prob_res)
fig = plt.figure(figsize=(5.5, 4.0))
plt.plot(rec, prec, label="PR curve")
# Mark chosen threshold (approximate match by nearest point)
idx_thr = np.argmin(np.abs(thr - best["threshold"])) if len(thr) else None
if idx_thr is not None and idx_thr < len(rec):
    plt.scatter(rec[idx_thr], prec[idx_thr], marker="o", label=f"thr={best['threshold']:.2f}")
plt.xlabel("Recall"); plt.ylabel("Precision"); plt.title("RES Precision-Recall")
plt.legend()
save_fig(fig, "res_pr_curve")

# Confusion matrix (RES, at tuned threshold)
y_pred_res_train = (y_prob_res >= best["threshold"]).astype(int)
cm = confusion_matrix(y_train_res, y_pred_res_train)
fig = plt.figure(figsize=(4.5, 4.0))
plt.imshow(cm, interpolation="nearest")
plt.xticks([0,1], ["Neg","Pos"]); plt.yticks([0,1], ["Neg","Pos"])
plt.xlabel("Predicted"); plt.ylabel("True"); plt.title("RES Confusion Matrix")
for (i,j), v in np.ndenumerate(cm):
    plt.text(j, i, int(v), ha="center", va="center")
save_fig(fig, "res_confusion_matrix")

# Probability histograms (RES)
fig = plt.figure(figsize=(5.5, 4.0))
plt.hist(y_prob_res[y_train_res==1], bins=30, alpha=0.6, label="Positives")
plt.hist(y_prob_res[y_train_res==0], bins=30, alpha=0.6, label="Negatives")
plt.axvline(best["threshold"], linestyle="--", label=f"thr={best['threshold']:.2f}")
plt.xlabel("Predicted probability"); plt.ylabel("Count"); plt.title("RES probability distributions")
plt.legend()
save_fig(fig, "res_prob_hist")

# Save model
model_path = out_dir / f"res_trained_gb_{timestamp}.pkl"
joblib.dump(model, model_path)
print(f"[RES] model saved → {model_path}")

rcps_pts = read_las_points_xyz(RCPS_POINT_CLOUD_PATH)
print(f"[RCPS] points: {len(rcps_pts):,}")

rcps_piles_xy, rcps_epsg = load_and_reproject_kml_to_utm_points(RCPS_BUFFER_KML_PATH)
print(f"[RCPS] pile locations: {len(rcps_piles_xy)}  CRS={rcps_epsg}")

# Build KD-trees once
rcps_tree_pts = cKDTree(rcps_pts[:, :2])
rcps_tree_piles = cKDTree(rcps_piles_xy)

rcps_patches, rcps_kept_xy = extract_patches(
    rcps_pts, rcps_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=rcps_tree_pts
)
print(f"[RCPS] patches extracted: {len(rcps_patches)}")
X_rcps = extract_features(rcps_patches) if len(rcps_patches) else np.empty((0,22), np.float32)

y_prob_rcps = model.predict_proba(X_rcps)[:,1] if len(X_rcps) else np.array([])
y_pred_rcps = (y_prob_rcps >= best["threshold"]).astype(int) if len(y_prob_rcps) else np.array([])
rcps_detection_rate = float(np.mean(y_pred_rcps)) if len(y_pred_rcps) else 0.0

# RES positive recall at tuned threshold:
res_pos_prob = model.predict_proba(X_pos_res)[:,1]
res_detection_rate = float(np.mean(res_pos_prob >= best["threshold"]))
perf_diff = rcps_detection_rate - res_detection_rate

if len(y_prob_rcps):
    print(f"[RCPS] prob stats → min={y_prob_rcps.min():.3f}, p50={np.median(y_prob_rcps):.3f}, max={y_prob_rcps.max():.3f}, thr={best['threshold']:.2f}")
print(f"[RCPS] detection rate: {rcps_detection_rate*100:.1f}% | diff vs RES: {perf_diff*100:+.1f} pp")


# ---- PLOTS: RCPS probability histogram (positives only; add negatives if probe runs) ----
if len(y_prob_rcps):
    fig = plt.figure(figsize=(5.5, 4.0))
    plt.hist(y_prob_rcps, bins=30, alpha=0.7, label="RCPS piles")
    plt.axvline(best["threshold"], linestyle="--", label=f"thr={best['threshold']:.2f}")
    plt.xlabel("Predicted probability"); plt.ylabel("Count"); plt.title("RCPS pile probabilities")
    plt.legend()
    save_fig(fig, "rcps_prob_hist_pos")

# --- RCPS precision probe: within-buffer core-masked negatives (CAPPED) ---
precision = None; fpr = None; rcps_negatives_count = 0
if RUN_PRECISION_PROBE:
    print("[RCPS] precision probe with core-masked within-buffer negatives...")
    probe_n = min(len(rcps_kept_xy), PROBE_NEG_MAX)
    neg_patches_probe, neg_centers_xy = sample_negatives_with_core_mask(
        points_xyz=rcps_pts,
        tree_points=rcps_tree_pts,
        tree_piles=rcps_tree_piles,
        pile_centers_xy=rcps_piles_xy,
        n_needed=probe_n,
        radius=PATCH_RADIUS,
        min_points_before=MIN_POINTS,
        min_points_after=MIN_POINTS_AFTER_MASK,
        core_radius=CORE_EXCLUDE_RADIUS,
        ring_inner_margin=RING_INNER_MARGIN,
        ring_outer_margin=RING_OUTER_MARGIN,
        max_per_pile=MAX_PER_PILE,
        max_angle_trials=MAX_ANGLE_TRIALS,
        seed=131,
    )
    X_rcps_neg = extract_features(neg_patches_probe) if len(neg_patches_probe) else np.empty((0,22), np.float32)

    if len(X_rcps_neg):
        y_prob_neg = model.predict_proba(X_rcps_neg)[:,1]
        y_pred_neg = (y_prob_neg >= best["threshold"]).astype(int)
        fp = int(np.sum(y_pred_neg == 1))
        tn = int(np.sum(y_pred_neg == 0))
        rcps_negatives_count = int(len(X_rcps_neg))
        precision = float(len(y_pred_rcps) / (len(y_pred_rcps) + fp)) if (len(y_pred_rcps)+fp) > 0 else 0.0
        fpr = fp / (fp + tn) if (fp+tn) > 0 else 0.0
        print(f"[RCPS] negatives probed (capped): {rcps_negatives_count} | FP={fp}, TN={tn} | Precision≈{precision:.3f} | FPR={fpr:.3f}")

        # Plot RCPS pos vs neg probability hist
        if len(y_prob_rcps):
            fig = plt.figure(figsize=(5.5, 4.0))
            plt.hist(y_prob_rcps, bins=30, alpha=0.6, label="RCPS piles (+)")
            plt.hist(y_prob_neg, bins=30, alpha=0.6, label="RCPS off-pile (−)")
            plt.axvline(best["threshold"], linestyle="--", label=f"thr={best['threshold']:.2f}")
            plt.xlabel("Predicted probability"); plt.ylabel("Count")
            plt.title("RCPS pos vs off-pile prob distributions")
            plt.legend()
            save_fig(fig, "rcps_prob_hist_pos_vs_neg")

        # Optional CSV for audit
        probe_csv = out_dir / f"rcps_precision_probe_{timestamp}.csv"
        pd.DataFrame({
            "utm_x": neg_centers_xy[:len(y_pred_neg),0],
            "utm_y": neg_centers_xy[:len(y_pred_neg),1],
            "pred": y_pred_neg,
            "prob": y_prob_neg
        }).to_csv(probe_csv, index=False)
        print(f"[OK] RCPS precision probe CSV → {probe_csv}")
    else:
        print("[RCPS] precision probe skipped (no usable core-masked negatives).")
else:
    print("[RCPS] precision probe skipped by flag (RUN_PRECISION_PROBE=False).")

# Status gate: require both high recall (on piles) and high precision (off-pile probe)
precision_ok = (precision is not None) and (precision >= 0.90)
recall_ok    = (rcps_detection_rate >= 0.90)

if precision_ok and recall_ok:
    status = "EXCELLENT CROSS-SITE GENERALIZATION"
elif (rcps_detection_rate >= 0.70) and (precision is not None) and (precision >= 0.70):
    status = "MODERATE CROSS-SITE GENERALIZATION"
else:
    status = "POOR CROSS-SITE GENERALIZATION"

summary = {
    "experiment_info": {
        "type": "true_model_generalization",
        "training_site": RES_SITE_NAME,
        "testing_site": RCPS_SITE_NAME,
        "timestamp": timestamp,
        "model_file": "",  # filled below
        "features": int(X_train_res.shape[1]),
        "threshold": float(best["threshold"]),
        "epsg_res": res_epsg,
        "epsg_rcps": rcps_epsg
    },
    "performance_metrics": {
        "res_precision": float(best["precision"]),
        "res_recall": float(best["recall"]),
        "res_f1": float(best["f1"]),
        "res_accuracy": float(best["accuracy"]),
        "res_detection_rate_on_positives": float(res_detection_rate),
        "rcps_detection_rate": float(rcps_detection_rate),
        "rcps_precision_probe_precision": (float(precision) if precision is not None else None),
        "rcps_precision_probe_fpr": (float(fpr) if fpr is not None else None),
        "rcps_prob_min": (float(y_prob_rcps.min()) if len(y_prob_rcps) else None),
        "rcps_prob_p50": (float(np.median(y_prob_rcps)) if len(y_prob_rcps) else None),
        "rcps_prob_max": (float(y_prob_rcps.max()) if len(y_prob_rcps) else None),
        "performance_difference": float(perf_diff),
        "generalization_status": status
    },
    "data_info": {
        "res_pile_locations": int(len(res_piles_xy)),
        "rcps_pile_locations": int(len(rcps_piles_xy)),
        "train_pos_used": int(len(X_pos_res)),
        "train_neg_used": int(len(X_neg_res)),
        "patch_radius": float(PATCH_RADIUS),
        "target_patch_size": int(TARGET_PATCH_SIZE),
        "min_points": int(MIN_POINTS),
        "min_points_after_mask": int(MIN_POINTS_AFTER_MASK),
        "core_exclude_radius": float(CORE_EXCLUDE_RADIUS),
        "rcps_precision_probe_negatives": int(rcps_negatives_count),
        "speed_mode": SPEED_MODE
    }
}
model_path = out_dir / f"res_trained_gb_{timestamp}.pkl"
joblib.dump(model, model_path)
summary["experiment_info"]["model_file"] = str(model_path)
results_file = out_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, "w") as f:
    json.dump(summary, f, indent=2)
print(f"[OK] results saved → {results_file}")
print(f"[RES] model saved → {model_path}")

# Export RCPS predictions for QGIS (CSV only)
if len(rcps_kept_xy):
    gdf_rcps = gpd.GeoDataFrame(
        {
            "pile_id": np.arange(len(rcps_kept_xy)),
            "utm_x": rcps_kept_xy[:,0],
            "utm_y": rcps_kept_xy[:,1],
            "predicted_pile": y_pred_rcps.astype(int),
            "confidence": y_prob_rcps.astype(float),
            "training_site": RES_SITE_NAME,
            "test_site": RCPS_SITE_NAME,
            "detection_status": np.where(y_pred_rcps==1, "Detected","Missed"),
            "generalization_type": "cross_site_pile_detection",
        },
        geometry=[Point(xy) for xy in rcps_kept_xy],
        crs=rcps_epsg
    )
    gdf_wgs84 = gdf_rcps.to_crs("EPSG:4326")
    rcps_csv = out_dir / f"rcps_generalization_results_{timestamp}.csv"
    df_out = pd.DataFrame(gdf_rcps.drop(columns="geometry"))
    df_out["longitude"] = gdf_wgs84.geometry.x
    df_out["latitude"]  = gdf_wgs84.geometry.y
    df_out.to_csv(rcps_csv, index=False)
    print(f"[OK] QGIS CSV (with lon/lat) → {rcps_csv}")

print("Cross-site generalization test complete.")
