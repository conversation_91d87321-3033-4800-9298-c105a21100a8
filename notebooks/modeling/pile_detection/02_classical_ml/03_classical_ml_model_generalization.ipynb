# Parameters
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# RES data paths
RES_POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

# RCPS data paths
RCPS_POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH = "../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

OUTPUT_DIR = "output_runs/true_generalization"

# Patch parameters (consistent across both sites)
PATCH_RADIUS = 3.0  # meters
MIN_POINTS = 20
TARGET_PATCH_SIZE = 64  # points per patch

print(f"TRUE MODEL GENERALIZATION TEST")
print(f"Training site: {RES_SITE_NAME}")
print(f"Testing site: {RCPS_SITE_NAME}")
print(f"No retraining - pure model transfer")

import numpy as np
import pandas as pd
import json
import joblib
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Point cloud and spatial
import laspy
import geopandas as gpd
from scipy.spatial import cKDTree

# ML
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

# Viz
import matplotlib.pyplot as plt

print("Libraries imported")

# Create output dir
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)  # For reproducibility
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):
    """Extract patches around locations"""
    print(f"Extracting patches (radius={radius}m, min_points={min_points}, target_size={TARGET_PATCH_SIZE})")
    
    tree = cKDTree(points[:, :2])
    patches = []
    valid_locs = []
    original_sizes = []
    
    for i, loc in enumerate(locations):
        indices = tree.query_ball_point(loc[:2], radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            original_sizes.append(len(patch_points))
            
            # Subsample to target size
            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
            
            # Center patch
            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])
            centered_patch = patch_points - center
            
            patches.append(centered_patch)
            valid_locs.append(loc)
    
    print(f"Extracted {len(patches)} valid patches")
    if original_sizes:
        print(f"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}")
        final_sizes = [len(p) for p in patches]
        print(f"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}")
    
    return patches, np.array(valid_locs)

def extract_features(patches):
    """Extract 22 features from patches"""
    features = []
    
    for patch in patches:
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        
        # Compute derived features
        radial_dist = np.sqrt(x**2 + y**2)
        height_above_min = z - np.min(z)
        
        # Statistical features (22 features total)
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch),  # num_points
            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio
            np.percentile(radial_dist, 90),  # 90th percentile radial distance
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height
        ]
        features.append(feature_vector)
    
    return np.array(features)

def load_and_reproject_kml(kml_path):
    """Load KML and reproject to UTM Zone 14N/15N"""
    gdf = gpd.read_file(kml_path)
    
    # Extract coordinates from polygon centroids
    pile_coords = []
    for geom in gdf.geometry:
        if geom.geom_type == 'Point':
            pile_coords.append([geom.x, geom.y])
        elif geom.geom_type == 'Polygon':
            centroid = geom.centroid
            pile_coords.append([centroid.x, centroid.y])
    
    pile_locations = np.array(pile_coords)
    
    # Create GeoDataFrame and reproject
    gdf_geo = gpd.GeoDataFrame(
        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),
        crs='EPSG:4326'  # WGS84 geographic
    )
    
    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)
    if 'nortan' in kml_path:
        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N
    else:
        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N
    
    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])
    
    return pile_locations_utm

print("Utility functions defined")

# STEP 1: TRAIN MODEL ON RES DATA
print("\n" + "=" * 60)
print("STEP 1: TRAINING MODEL ON RES DATA")
print("=" * 60)

# Load RES point cloud
print(f"\nLoading RES point cloud: {RES_POINT_CLOUD_PATH}")
res_las = laspy.read(RES_POINT_CLOUD_PATH)
res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T
print(f"Loaded {len(res_points):,} points")

# Load RES pile locations
print(f"\nLoading RES pile locations: {RES_BUFFER_KML_PATH}")
res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)
print(f"Loaded {len(res_pile_locations)} pile locations")

# Extract positive patches (pile locations)
print(f"\nExtracting positive patches from RES...")
res_pos_patches, _ = extract_patches(res_points, res_pile_locations)
print(f"Extracted {len(res_pos_patches)} positive patches")

# Create negative samples for RES
print(f"\nCreating negative samples for RES...")
np.random.seed(42)
x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()
y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()

n_negative = len(res_pos_patches)
random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)
random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)
random_locations = np.column_stack([random_x, random_y])

res_neg_patches, _ = extract_patches(res_points, random_locations)
print(f"Created {len(res_neg_patches)} negative patches")

# Extract features
print(f"\nExtracting features from RES patches...")
X_pos_res = extract_features(res_pos_patches)
X_neg_res = extract_features(res_neg_patches)

# Combine training data
X_train_res = np.vstack([X_pos_res, X_neg_res])
y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])

print(f"RES training dataset: {len(X_train_res)} samples ({np.sum(y_train_res)} positive, {len(y_train_res) - np.sum(y_train_res)} negative)")
print(f"Features per sample: {X_train_res.shape[1]}")

# Train model on RES data
print(f"\nTraining Gradient Boosting model on RES data...")
res_model = GradientBoostingClassifier(
    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42
)
res_model.fit(X_train_res, y_train_res)

# Test model on RES data (sanity check)
y_pred_res = res_model.predict(X_pos_res)
y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]
res_detection_rate = np.mean(y_pred_res)
res_avg_confidence = np.mean(y_prob_res)

print(f"\nRES model performance (sanity check):")
print(f"  Detection rate on RES piles: {res_detection_rate*100:.1f}%")
print(f"  Average confidence: {res_avg_confidence:.3f}")

# Save trained model
model_file = output_dir / f"res_trained_model_{timestamp}.pkl"
joblib.dump(res_model, model_file)
print(f"\nModel saved: {model_file}")
print(f"RES training complete!")

# STEP 2: TEST MODEL ON RCPS DATA (NO RETRAINING)
print("\n" + "=" * 60)
print("STEP 2: TESTING RES MODEL ON RCPS DATA")
print("=" * 60)
print("NO RETRAINING - PURE MODEL TRANSFER")

# Load RCPS point cloud
print(f"\nLoading RCPS point cloud: {RCPS_POINT_CLOUD_PATH}")
rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)
rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T
print(f"Loaded {len(rcps_points):,} points")

# Load RCPS pile locations
print(f"\nLoading RCPS pile locations: {RCPS_BUFFER_KML_PATH}")
rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)
print(f"Loaded {len(rcps_pile_locations)} pile locations")

# Extract patches from RCPS pile locations
print(f"\nExtracting patches from RCPS pile locations...")
rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)
print(f"Extracted {len(rcps_patches)} patches from RCPS")

# Extract features from RCPS patches
print(f"\nExtracting features from RCPS patches...")
X_rcps = extract_features(rcps_patches)
print(f"RCPS features: {X_rcps.shape}")

# Load trained RES model
print(f"\nLoading trained RES model...")
loaded_model = joblib.load(model_file)
print(f"Model loaded successfully")

# Apply RES model to RCPS data (NO RETRAINING!)
print(f"\nApplying RES-trained model to RCPS data...")
y_pred_rcps = loaded_model.predict(X_rcps)
y_prob_rcps = loaded_model.predict_proba(X_rcps)[:, 1]

rcps_detection_rate = np.mean(y_pred_rcps)
rcps_avg_confidence = np.mean(y_prob_rcps)

# Results
print(f"\nTRUE MODEL GENERALIZATION RESULTS:")
print(f"  Training site: {RES_SITE_NAME}")
print(f"  Testing site: {RCPS_SITE_NAME}")
print(f"  Known pile locations tested: {len(rcps_pile_locations)}")
print(f"  Valid patches extracted: {len(rcps_patches)}")
print(f"  Detected as piles: {np.sum(y_pred_rcps)} ({rcps_detection_rate*100:.1f}%)")
print(f"  Average confidence: {rcps_avg_confidence:.3f}")

# Compare with RES performance
performance_diff = rcps_detection_rate - res_detection_rate

print(f"\nGENERALIZATION ANALYSIS:")
print(f"  RES performance (training site): {res_detection_rate*100:.1f}%")
print(f"  RCPS performance (test site): {rcps_detection_rate*100:.1f}%")
print(f"  Performance difference: {performance_diff*100:+.1f} percentage points")

# Interpretation
if rcps_detection_rate >= 0.9:
    if abs(performance_diff) <= 0.1:
        status = "EXCELLENT GENERALIZATION"
        interpretation = "Model transfers perfectly across construction sites"
    else:
        status = "GOOD GENERALIZATION"
        interpretation = "Model works well but with some site-specific differences"
elif rcps_detection_rate >= 0.7:
    status = "MODERATE GENERALIZATION"
    interpretation = "Model partially generalizes - may need fine-tuning"
else:
    status = "POOR GENERALIZATION"
    interpretation = "Model fails to generalize - site-specific training needed"

print(f"\nCONCLUSION:")
print(f"  {status}")
print(f"  {interpretation}")

# Save results
results = {
    'experiment_info': {
        'type': 'true_model_generalization',
        'training_site': RES_SITE_NAME,
        'testing_site': RCPS_SITE_NAME,
        'timestamp': timestamp,
        'model_file': str(model_file)
    },
    'performance_metrics': {
        'res_detection_rate': float(res_detection_rate),
        'res_avg_confidence': float(res_avg_confidence),
        'rcps_detection_rate': float(rcps_detection_rate),
        'rcps_avg_confidence': float(rcps_avg_confidence),
        'performance_difference': float(performance_diff),
        'generalization_status': status
    },
    'data_info': {
        'res_pile_locations': len(res_pile_locations),
        'res_patches': len(res_pos_patches),
        'rcps_pile_locations': len(rcps_pile_locations),
        'rcps_patches': len(rcps_patches),
        'patch_radius': PATCH_RADIUS,
        'target_patch_size': TARGET_PATCH_SIZE
    }
}

results_file = output_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, 'w') as f:
    json.dump(results, f, indent=2)

print(f"\nResults saved: {results_file}")

# Export RCPS results for QGIS visualization
print(f"\nExporting RCPS generalization results for QGIS...")

# Create comprehensive results DataFrame for QGIS
rcps_results_df = pd.DataFrame({
    'pile_id': range(len(rcps_pile_locations)),
    'utm_x': rcps_pile_locations[:, 0],
    'utm_y': rcps_pile_locations[:, 1], 
    'predicted_pile': y_pred_rcps,
    'confidence': y_prob_rcps,
    'training_site': RES_SITE_NAME,
    'test_site': RCPS_SITE_NAME,
    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],
    'generalization_type': 'true_model_transfer'
})

# Convert UTM coordinates to geographic (WGS84) for QGIS
import geopandas as gpd
from shapely.geometry import Point

# Create GeoDataFrame with UTM coordinates (Zone 15N for RCPS)
geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]
gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')  # UTM Zone 15N

# Convert to WGS84 for QGIS
gdf_wgs84 = gdf.to_crs('EPSG:4326')

# Extract lat/lon from geometry
rcps_results_df['longitude'] = gdf_wgs84.geometry.x
rcps_results_df['latitude'] = gdf_wgs84.geometry.y

# Save CSV for QGIS
csv_file = output_dir / f"rcps_generalization_results_{timestamp}.csv"
rcps_results_df.to_csv(csv_file, index=False)

print(f"QGIS CSV exported: {csv_file}")
print(f"Columns: pile_id, utm_x, utm_y, longitude, latitude, predicted_pile, confidence, detection_status, training_site, test_site")
print(f"Coordinate System: WGS84 (EPSG:4326) + UTM Zone 15N")
print(f"Total RCPS points: {len(rcps_results_df)}")

# Generalization summary for QGIS
detected_count = np.sum(y_pred_rcps)
missed_count = len(y_pred_rcps) - detected_count

print(f"\nQGIS Generalization Visualization:")
print(f"  Training site: {RES_SITE_NAME} (368 piles)")
print(f"  Test site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)")
print(f"  Detected: {detected_count} piles")
print(f"  Missed: {missed_count} piles")
print(f"  Generalization rate: {rcps_detection_rate*100:.1f}%")
print(f"  Average confidence: {rcps_avg_confidence:.3f}")

print(f"\nTrue model generalization test complete!")
print(f"\nSUMMARY:")
print(f"  Trained once on {RES_SITE_NAME}")
print(f"  Tested on {RCPS_SITE_NAME} without retraining")
print(f"  Detection rate: {rcps_detection_rate*100:.1f}%")
print(f"  This is TRUE model generalization!")
print(f"  QGIS visualization ready: {csv_file}")

# FALSE POSITIVE TEST ON RCPS - Critical for generalization validation
if len(rcps_patches) > 0 and 'loaded_model' in locals():
    print("\n" + "=" * 60)
    print("FALSE POSITIVE RATE TEST ON RCPS")
    print("=" * 60)
    print("Testing RES-trained model on RCPS random non-pile locations")
    print("This tests cross-site discrimination ability")
    
    # Generate random test locations in RCPS (away from known piles)
    np.random.seed(456)  # Different seed for RCPS test locations
    n_test_negatives = min(300, len(rcps_patches))  # Test more for larger dataset
    
    # Sample random locations with buffer from known piles
    buffer_distance = 2.0  # 2m buffer from known piles (very reduced for dense RCPS)
    max_attempts = 3000  # Increased attempts for larger site
    test_negative_locations = []
    
    rcps_x_min, rcps_x_max = rcps_points[:, 0].min(), rcps_points[:, 0].max()
    rcps_y_min, rcps_y_max = rcps_points[:, 1].min(), rcps_points[:, 1].max()
    
    for attempt in range(max_attempts):
        if len(test_negative_locations) >= n_test_negatives:
            break
            
        # Random location within RCPS point cloud bounds (reduced margins)
        test_x = np.random.uniform(rcps_x_min + 50, rcps_x_max - 50)
        test_y = np.random.uniform(rcps_y_min + 50, rcps_y_max - 50)
        test_loc = np.array([test_x, test_y])
        
        # Check distance from all known RCPS piles
        distances = np.sqrt(np.sum((rcps_pile_locations[:, :2] - test_loc)**2, axis=1))
        min_distance = np.min(distances)
        
        # Only use if far enough from known piles
        if min_distance > buffer_distance:
            test_negative_locations.append(test_loc)
    
    test_negative_locations = np.array(test_negative_locations)
    print(f"Generated {len(test_negative_locations)} RCPS test locations (>{buffer_distance}m from known piles)")
    print(f"Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives")
    
    if len(test_negative_locations) > 0:
        # Extract patches from RCPS test negative locations (very relaxed parameters)
        test_neg_patches, _ = extract_patches(rcps_points, test_negative_locations, 1.5, 3)  # Very small radius, minimal points
        
        if len(test_neg_patches) > 0:
            # Subsample test negative patches
            test_neg_patches = [subsample_patch(patch, TARGET_PATCH_SIZE) for patch in test_neg_patches]
            
            # Extract features and predict using RES-trained model
            X_test_neg_rcps = extract_features(test_neg_patches)
            y_pred_test_neg = loaded_model.predict(X_test_neg_rcps)
            y_prob_test_neg = loaded_model.predict_proba(X_test_neg_rcps)[:, 1]
            
            false_positive_rate_rcps = np.mean(y_pred_test_neg)
            avg_confidence_fp_rcps = np.mean(y_prob_test_neg)
            
            print(f"\n📊 RCPS FALSE POSITIVE ANALYSIS:")
            print(f"  RCPS test negative locations: {len(test_negative_locations)}")
            print(f"  Valid negative patches: {len(test_neg_patches)}")
            print(f"  False positives: {np.sum(y_pred_test_neg)} / {len(y_pred_test_neg)}")
            print(f"  False positive rate: {false_positive_rate_rcps*100:.1f}%")
            print(f"  Avg confidence on negatives: {avg_confidence_fp_rcps:.3f}")
            
            # Interpretation
            if false_positive_rate_rcps <= 0.1:  # ≤10% false positives
                fp_status_rcps = "✅ EXCELLENT - Low false positives across sites"
            elif false_positive_rate_rcps <= 0.2:  # ≤20% false positives
                fp_status_rcps = "✅ GOOD - Acceptable false positives across sites"
            elif false_positive_rate_rcps <= 0.4:  # ≤40% false positives
                fp_status_rcps = "⚠️ MODERATE - Some false positives across sites"
            else:
                fp_status_rcps = "❌ POOR - Too many false positives across sites"
            
            print(f"  {fp_status_rcps}")
            
            print(f"\n🎯 COMPLETE GENERALIZATION VALIDATION:")
            print(f"  Training site: {RES_SITE_NAME}")
            print(f"  Test site: {RCPS_SITE_NAME}")
            print(f"  Pile detection rate: {rcps_detection_rate*100:.1f}%")
            print(f"  False positive rate: {false_positive_rate_rcps*100:.1f}%")
            print(f"  Cross-site performance: {fp_status_rcps}")
            
            # Overall assessment
            if rcps_detection_rate >= 0.9 and false_positive_rate_rcps <= 0.2:
                overall_status = "🎉 EXCELLENT GENERALIZATION - Ready for deployment"
            elif rcps_detection_rate >= 0.8 and false_positive_rate_rcps <= 0.3:
                overall_status = "✅ GOOD GENERALIZATION - Minor tuning may help"
            else:
                overall_status = "⚠️ GENERALIZATION ISSUES - Needs investigation"
            
            print(f"\n🏆 FINAL ASSESSMENT: {overall_status}")
            
            # Add to CSV for QGIS (negative test points)
            if len(test_negative_locations) > 0:
                # Create negative test results DataFrame
                # Fix array length mismatch: use only valid patches
                neg_test_df = pd.DataFrame({
                    'pile_id': [f'neg_test_{i}' for i in range(len(test_neg_patches))],
                    'utm_x': test_negative_locations[:len(test_neg_patches), 0],
                    'utm_y': test_negative_locations[:len(test_neg_patches), 1],
                    'predicted_pile': y_pred_test_neg,
                    'confidence': y_prob_test_neg,
                    'training_site': RES_SITE_NAME,
                    'test_site': RCPS_SITE_NAME,
                    'detection_status': ['False_Positive' if pred == 1 else 'True_Negative' for pred in y_pred_test_neg],
                    'generalization_type': 'negative_test'
                })
                
                # Convert to geographic coordinates (only valid patches)
                geometry_neg = [Point(xy) for xy in zip(test_negative_locations[:len(test_neg_patches), 0], test_negative_locations[:len(test_neg_patches), 1])]
                gdf_neg = gpd.GeoDataFrame(neg_test_df, geometry=geometry_neg, crs='EPSG:32615')  # UTM Zone 15N
                gdf_neg_wgs84 = gdf_neg.to_crs('EPSG:4326')
                
                neg_test_df['longitude'] = gdf_neg_wgs84.geometry.x
                neg_test_df['latitude'] = gdf_neg_wgs84.geometry.y
                
                # Save negative test results CSV
                neg_csv_file = output_dir / f"rcps_negative_test_results_{timestamp}.csv"
                neg_test_df.to_csv(neg_csv_file, index=False)
                
                print(f"\n📍 QGIS Negative Test CSV: {neg_csv_file}")
                print(f"   Use this to visualize false positive locations")
                print(f"   False positives: {np.sum(y_pred_test_neg)} red points")
                print(f"   True negatives: {len(y_pred_test_neg) - np.sum(y_pred_test_neg)} green points")
            
        else:
            print(f"⚠️ Could not extract valid patches from RCPS test negative locations")
    else:
        print(f"⚠️ Could not generate sufficient RCPS test negative locations")
        
else:
    print("No model available for RCPS false positive testing")

# %% [markdown]
# # True Model Generalization Test
# 
# Train model on RES data, test on RCPS data without retraining.
# 
# **Author**: Preetam Balijepalli  
# **Date**: August 2025

# %% [markdown]
# ## Configuration

# %%
# Parameters
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# Data paths
RES_POINT_CLOUD_PATH = "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH = "../../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

RCPS_POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH = "../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

OUTPUT_DIR = "output_runs/true_generalization"

# Patch parameters
PATCH_RADIUS = 3.0
MIN_POINTS = 20
TARGET_PATCH_SIZE = 64

# %% [markdown]
# ## Imports

# %%
import numpy as np
import pandas as pd
import json
import joblib
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import laspy
import geopandas as gpd
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
import matplotlib.pyplot as plt

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# %% [markdown]
# ## Utility Functions

# %%
def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):
    """Extract patches around locations"""
    tree = cKDTree(points[:, :2])
    patches = []
    valid_locs = []
    
    for i, loc in enumerate(locations):
        indices = tree.query_ball_point(loc[:2], radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
            
            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])
            centered_patch = patch_points - center
            
            patches.append(centered_patch)
            valid_locs.append(loc)
    
    return patches, np.array(valid_locs)

def extract_features(patches):
    """Extract 22 features from patches"""
    features = []
    
    for patch in patches:
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        radial_dist = np.sqrt(x**2 + y**2)
        height_above_min = z - np.min(z)
        
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch),
            np.std(x) / (np.std(y) + 1e-6),
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),
            np.percentile(radial_dist, 90),
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),
        ]
        features.append(feature_vector)
    
    return np.array(features)

def load_and_reproject_kml(kml_path):
    """Load KML and reproject to UTM"""
    gdf = gpd.read_file(kml_path)
    
    pile_coords = []
    for geom in gdf.geometry:
        if geom.geom_type == 'Point':
            pile_coords.append([geom.x, geom.y])
        elif geom.geom_type == 'Polygon':
            centroid = geom.centroid
            pile_coords.append([centroid.x, centroid.y])
    
    pile_locations = np.array(pile_coords)
    
    gdf_geo = gpd.GeoDataFrame(
        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),
        crs='EPSG:4326'
    )
    
    # Reproject to appropriate UTM zone
    if 'nortan' in str(kml_path):
        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N
    else:
        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N
    
    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])
    return pile_locations_utm

# %% [markdown]
# ## Step 1: Train Model on RES Data

# %%
# Load RES point cloud
res_las = laspy.read(RES_POINT_CLOUD_PATH)
res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T
print(f"RES points: {len(res_points):,}")

# Load RES pile locations
res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)
print(f"RES pile locations: {len(res_pile_locations)}")

# Extract positive patches
res_pos_patches, _ = extract_patches(res_points, res_pile_locations)
print(f"RES positive patches: {len(res_pos_patches)}")

# %%
# Create negative samples using random sampling within point cloud bounds
print("Creating negative samples within point cloud coverage...")

np.random.seed(42)
x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()
y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()

n_negative = len(res_pos_patches)
random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)
random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)
random_locations = np.column_stack([random_x, random_y])

res_neg_patches, _ = extract_patches(res_points, random_locations)
print(f"RES negative patches: {len(res_neg_patches)}")
print("Note: Negatives may include pile-adjacent areas due to data filtering")

# %%
# Prepare training data
X_pos_res = extract_features(res_pos_patches)
X_neg_res = extract_features(res_neg_patches)

X_train_res = np.vstack([X_pos_res, X_neg_res])
y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])

print(f"Training data: {len(X_train_res)} samples")
print(f"Positive: {np.sum(y_train_res)}, Negative: {len(y_train_res) - np.sum(y_train_res)}")

# %%
# Train model
res_model = GradientBoostingClassifier(
    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42
)
res_model.fit(X_train_res, y_train_res)

# Sanity check on RES
y_pred_res = res_model.predict(X_pos_res)
y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]
res_detection_rate = np.mean(y_pred_res)

print(f"RES detection rate: {res_detection_rate*100:.1f}%")

# Save model
model_file = output_dir / f"res_trained_model_{timestamp}.pkl"
joblib.dump(res_model, model_file)
print(f"Model saved: {model_file}")

# %% [markdown]
# ## Step 2: Test on RCPS Data (No Retraining)

# %%
# Load RCPS data
rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)
rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T
print(f"RCPS points: {len(rcps_points):,}")

rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)
print(f"RCPS pile locations: {len(rcps_pile_locations)}")

# %%
# Extract RCPS patches
rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)
print(f"RCPS patches: {len(rcps_patches)}")

# Extract features
X_rcps = extract_features(rcps_patches)

# Apply trained model (no retraining)
y_pred_rcps = res_model.predict(X_rcps)
y_prob_rcps = res_model.predict_proba(X_rcps)[:, 1]

rcps_detection_rate = np.mean(y_pred_rcps)
performance_diff = rcps_detection_rate - res_detection_rate

print(f"RCPS detection rate: {rcps_detection_rate*100:.1f}%")
print(f"Performance difference: {performance_diff*100:+.1f} percentage points")



# %% [markdown]
# ## Results and Export

# %%
# Results summary
print("CROSS-SITE GENERALIZATION RESULTS:")
print(f"Training site: {RES_SITE_NAME} ({len(res_pile_locations)} piles)")
print(f"Testing site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)")
print(f"Cross-site detection rate: {rcps_detection_rate*100:.1f}%")
print(f"Performance difference: {performance_diff*100:+.1f} percentage points")

# Overall generalization assessment
if rcps_detection_rate >= 0.9:
    if abs(performance_diff) <= 0.1:
        status = "EXCELLENT CROSS-SITE GENERALIZATION"
    else:
        status = "GOOD CROSS-SITE GENERALIZATION"
elif rcps_detection_rate >= 0.7:
    status = "MODERATE CROSS-SITE GENERALIZATION"
else:
    status = "POOR CROSS-SITE GENERALIZATION"

print(f"Overall status: {status}")
print("Note: Point cloud data pre-filtered to pile buffer regions")

# %%
# Save results
results = {
    'experiment_info': {
        'type': 'true_model_generalization',
        'training_site': RES_SITE_NAME,
        'testing_site': RCPS_SITE_NAME,
        'timestamp': timestamp,
        'model_file': str(model_file)
    },
    'performance_metrics': {
        'res_detection_rate': float(res_detection_rate),
        'rcps_detection_rate': float(rcps_detection_rate),
        'performance_difference': float(performance_diff),
        'generalization_status': status
    },
    'data_info': {
        'res_pile_locations': len(res_pile_locations),
        'rcps_pile_locations': len(rcps_pile_locations),
        'patch_radius': PATCH_RADIUS,
        'target_patch_size': TARGET_PATCH_SIZE,
        'data_scope': 'pile_buffer_regions_only'
    }
}

results_file = output_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, 'w') as f:
    json.dump(results, f, indent=2)

print(f"Results saved: {results_file}")

# %%
# Export for QGIS visualization
rcps_results_df = pd.DataFrame({
    'pile_id': range(len(rcps_pile_locations)),
    'utm_x': rcps_pile_locations[:, 0],
    'utm_y': rcps_pile_locations[:, 1], 
    'predicted_pile': y_pred_rcps,
    'confidence': y_prob_rcps,
    'training_site': RES_SITE_NAME,
    'test_site': RCPS_SITE_NAME,
    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],
    'generalization_type': 'cross_site_pile_detection'
})

# Convert to geographic coordinates
from shapely.geometry import Point
geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]
gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')
gdf_wgs84 = gdf.to_crs('EPSG:4326')

rcps_results_df['longitude'] = gdf_wgs84.geometry.x
rcps_results_df['latitude'] = gdf_wgs84.geometry.y

csv_file = output_dir / f"rcps_generalization_results_{timestamp}.csv"
rcps_results_df.to_csv(csv_file, index=False)

print(f"QGIS CSV exported: {csv_file}")
print("Cross-site generalization test complete")

!ls -lh ../../../../data/raw/nortan_res

# %% [markdown]
# # True Model Generalization Test (RES → RCPS, no retraining)
# Train on RES, select decision threshold on RES, and test as-is on RCPS.
#
# Author: Preetam Balijepalli
# Date: August 2025

# %% [markdown]
# ## Configuration

# %%
from __future__ import annotations
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

# Site names
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# Data paths
RES_POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

RCPS_POINT_CLOUD_PATH = "../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH = "../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

# Optional: strategic negatives for RES (UTM X/Y columns: utm_x, utm_y)
RES_STRATEGIC_NEG_CSV = "../../../../data/ground_truth/nortan_res_negative_samples.csv"

# Output
OUTPUT_DIR = "output_runs/true_generalization"

# Patch & sampling
PATCH_RADIUS = 3.0      # meters
MIN_POINTS   = 20
TARGET_PATCH_SIZE = 64
NEGATIVE_EXCLUSION_RADIUS = 6.0  # don't draw negatives within this 2D distance of any pile

# Model & eval
GB_PARAMS = dict(n_estimators=150, max_depth=6, learning_rate=0.08, random_state=42)
THRESH_GRID = [round(x, 2) for x in list(__import__("numpy").linspace(0.2, 0.9, 36))]  # for max-F1

# Logging
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
out_dir = Path(OUTPUT_DIR); out_dir.mkdir(parents=True, exist_ok=True)

# %% [markdown]
# ## Imports

# %%
import json
import numpy as np
import pandas as pd
import joblib
import laspy
import geopandas as gpd
from shapely.geometry import Point
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import precision_recall_fscore_support, accuracy_score

# %% [markdown]
# ## Utilities

# %%
def rng_for(i: int, base_seed: int = 42):
    """Deterministic RNG per patch index."""
    return np.random.default_rng(base_seed + int(i))

def utm_epsg_from_lonlat(lon: float, lat: float) -> str:
    """Compute UTM EPSG code from lon/lat (WGS84)."""
    zone = int(np.floor((lon + 180) / 6) + 1)
    north = lat >= 0
    return f"EPSG:{326 if north else 327}{zone:02d}"

def load_and_reproject_kml_to_utm_points(kml_path: str | Path) -> tuple[np.ndarray, str]:
    """Load KML geometries, take centroids for polygons, and reproject to UTM."""
    gdf = gpd.read_file(kml_path)
    pts_ll = []
    for geom in gdf.geometry:
        if geom is None:
            continue
        if geom.geom_type == "Point":
            pts_ll.append([geom.x, geom.y])
        elif geom.geom_type in ("Polygon", "MultiPolygon", "LineString", "MultiLineString"):
            centroid = geom.centroid
            pts_ll.append([centroid.x, centroid.y])
    if not pts_ll:
        return np.empty((0, 2), dtype=float), "EPSG:4326"

    pts_ll = np.asarray(pts_ll, dtype=float)
    mean_lon, mean_lat = float(np.mean(pts_ll[:,0])), float(np.mean(pts_ll[:,1]))
    utm_epsg = utm_epsg_from_lonlat(mean_lon, mean_lat)

    gdf_ll = gpd.GeoDataFrame(geometry=gpd.points_from_xy(pts_ll[:,0], pts_ll[:,1]), crs="EPSG:4326")
    gdf_utm = gdf_ll.to_crs(utm_epsg)
    pts_utm = np.array([[p.x, p.y] for p in gdf_utm.geometry], dtype=float)
    return pts_utm, utm_epsg

def read_las_points_xyz(las_path: str | Path) -> np.ndarray:
    """Read LAS/LAZ and return Nx3 float32 array [x,y,z]."""
    las = laspy.read(las_path)
    pts = np.vstack([las.x, las.y, las.z]).T.astype(np.float32, copy=False)
    return pts

def subsample_patch(patch_points: np.ndarray, target_size: int, idx_seed: int) -> np.ndarray:
    """Deterministic subsample to target_size using per-index RNG."""
    n = len(patch_points)
    if n <= target_size:
        return patch_points
    rs = rng_for(idx_seed)
    idx = rs.choice(n, size=target_size, replace=False)
    return patch_points[idx]

def extract_patches(points_xyz: np.ndarray,
                    locations_xy: np.ndarray,
                    radius: float,
                    min_points: int,
                    target_size: int) -> tuple[list[np.ndarray], np.ndarray]:
    """Extract centered patches around each location."""
    if len(points_xyz) == 0 or len(locations_xy) == 0:
        return [], np.empty((0,2), dtype=float)
    tree = cKDTree(points_xyz[:, :2])
    patches, kept_locs = [], []
    for i, loc in enumerate(locations_xy):
        idxs = tree.query_ball_point(loc, radius)
        if len(idxs) >= min_points:
            patch = points_xyz[np.asarray(idxs)]
            center = np.array([loc[0], loc[1], float(np.mean(patch[:,2]))], dtype=np.float32)
            patch_c = patch - center
            patch_c = subsample_patch(patch_c, target_size, idx_seed=i)
            patches.append(patch_c.astype(np.float32, copy=False))
            kept_locs.append(loc)
    return patches, np.asarray(kept_locs, dtype=float)

def extract_features(patches):
    """Return EXACTLY 22 features per patch (thesis baseline)."""
    feats, eps = [], 1e-6
    for patch in patches:
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        r = np.sqrt(x**2 + y**2)
        zmin = np.min(z)
        h = z - zmin  # height above local min
        feats.append([
            # Basic spatial (9)
            float(np.mean(x)), float(np.std(x)), float(np.ptp(x)),
            float(np.mean(y)), float(np.std(y)), float(np.ptp(y)),
            float(np.mean(z)), float(np.std(z)), float(np.ptp(z)),
            # Height-based (4)
            float(np.mean(h)), float(np.std(h)),
            float(np.percentile(h, 75)), float(np.percentile(h, 25)),
            # Radial distance (4)
            float(np.mean(r)), float(np.std(r)), float(np.min(r)), float(np.max(r)),
            # Shape/density (5)
            float(len(patch)),
            float(np.std(x) / (np.std(y) + eps)),
            float(np.std(z) / (np.std(x) + np.std(y) + eps)),
            float(np.percentile(r, 90)),
            float(np.mean(h > np.mean(h))),
        ])
    X = np.asarray(feats, dtype=np.float32)
    if X.size and X.shape[1] != 22:
        raise RuntimeError(f"Feature drift: expected 22, got {X.shape[1]}")
    return X

# --- NEW: density-aware negative sampler (jitter real points, enforce min neighbor count) ---
def sample_negatives_from_points(points_xy: np.ndarray,
                                 n_needed: int,
                                 exclude_xy: np.ndarray,
                                 exclusion_radius: float,
                                 radius: float,
                                 min_points: int,
                                 seed: int = 42,
                                 jitter_sigma: float = 0.40,
                                 max_tries: int = 150000) -> np.ndarray:
    """
    Sample negatives from high-density areas by picking existing point locations and jittering.
    Ensures:
      - far from exclude_xy by exclusion_radius
      - at least min_points neighbors within radius (so patches won't be empty)
    """
    rs = np.random.default_rng(seed)
    tree_pts = cKDTree(points_xy)
    tree_ex = cKDTree(exclude_xy) if len(exclude_xy) else None

    accepted = []
    tries = 0
    N = len(points_xy)
    if N == 0:
        return np.empty((0,2), dtype=float)

    # Adaptive jitter growth if environment is tight
    sigma = float(jitter_sigma)

    while len(accepted) < n_needed and tries < max_tries:
        base = points_xy[rs.integers(0, N)]
        cand = base + rs.normal(0.0, sigma, size=2)

        # exclude near piles
        if tree_ex is not None:
            d, _ = tree_ex.query(cand, k=1)
            if d <= exclusion_radius:
                tries += 1
                if tries % 20000 == 0:
                    sigma *= 1.4
                continue

        # ensure density
        cnt = len(tree_pts.query_ball_point(cand, r=radius))
        if cnt >= min_points:
            accepted.append(cand)

        tries += 1
        if tries % 20000 == 0:
            sigma *= 1.4  # gradually widen search

    return np.asarray(accepted[:n_needed], dtype=float)

# %% [markdown]
# ## Step 1: Train on RES (+ threshold selection)

# %%
# Load points & piles (RES)
res_pts = read_las_points_xyz(RES_POINT_CLOUD_PATH)
print(f"[RES] points: {len(res_pts):,}")

res_piles_xy, res_epsg = load_and_reproject_kml_to_utm_points(RES_BUFFER_KML_PATH)
print(f"[RES] pile locations: {len(res_piles_xy)}  CRS={res_epsg}")

# Positive patches
res_pos_patches, res_pos_locs = extract_patches(res_pts, res_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)
print(f"[RES] positive patches: {len(res_pos_patches)}")
if len(res_pos_patches) == 0:
    raise RuntimeError("No positive patches on RES; check KML / radius / min_points.")
n_pos_full = len(res_pos_patches)

# Strategic negatives (preferred)
neg_from_csv = None
try:
    df_neg = pd.read_csv(RES_STRATEGIC_NEG_CSV)
    if {"utm_x","utm_y"}.issubset(df_neg.columns):
        neg_from_csv = df_neg[["utm_x","utm_y"]].to_numpy(dtype=float)
        print(f"[RES] strategic negatives loaded (locations): {len(neg_from_csv)}")
    else:
        print("[RES] strategic negatives CSV missing columns 'utm_x','utm_y' → ignored")
except FileNotFoundError:
    print("[RES] strategic negatives CSV not found → skipping")

# Build negative patches to MATCH all positives (top-up with density-aware sampling)
res_neg_patches, res_neg_locs = [], []

def _append_neg_from_xy(xy: np.ndarray, label: str):
    if xy is None or len(xy) == 0:
        return 0
    patches, locs = extract_patches(res_pts, xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)
    if patches:
        res_neg_patches.extend(patches); res_neg_locs.extend(locs)
    print(f"[RES] negatives from {label}: +{len(patches)} patches")
    return len(patches)

# 1) strategic negatives first
_ = _append_neg_from_xy(neg_from_csv, "strategic_csv")

# 2) density-aware top-up
need = n_pos_full - len(res_neg_patches)
if need > 0:
    res_neg_xy_dense = sample_negatives_from_points(
        points_xy=res_pts[:, :2],
        n_needed=need,
        exclude_xy=res_piles_xy,
        exclusion_radius=NEGATIVE_EXCLUSION_RADIUS,
        radius=PATCH_RADIUS,
        min_points=MIN_POINTS,
        seed=42,
        jitter_sigma=0.40,
    )
    _ = _append_neg_from_xy(res_neg_xy_dense, "dense_sampler")

# 3) finalize balance
if len(res_neg_patches) < n_pos_full:
    print(f"[RES] WARNING: only {len(res_neg_patches)} negative patches available; trimming positives to match.")
n_use = min(n_pos_full, len(res_neg_patches))
res_pos_patches = res_pos_patches[:n_use]
res_neg_patches = res_neg_patches[:n_use]
print(f"[RES] using balanced patches → pos={len(res_pos_patches)}, neg={len(res_neg_patches)}")

# Features & labels (22 features)
X_pos_res = extract_features(res_pos_patches)
X_neg_res = extract_features(res_neg_patches)
assert X_pos_res.shape[1] == 22 and X_neg_res.shape[1] == 22, "Feature drift: expected 22"
X_train_res = np.vstack([X_pos_res, X_neg_res]).astype(np.float32)
y_train_res = np.hstack([np.ones(len(X_pos_res), dtype=int), np.zeros(len(X_neg_res), dtype=int)])
print(f"[RES] train samples: {len(X_train_res)} | features: {X_train_res.shape[1]}")

# Train model
model = GradientBoostingClassifier(**GB_PARAMS)
model.fit(X_train_res, y_train_res)

# Threshold tuning on RES
y_prob_res = model.predict_proba(X_train_res)[:,1]
best = tune_threshold(y_train_res, y_prob_res, THRESH_GRID)
print(f"[RES] tuned threshold = {best['threshold']:.2f} | "
      f"P={best['precision']:.2f} R={best['recall']:.2f} F1={best['f1']:.2f} Acc={best['accuracy']:.2f}")

# Save model & importances
model_path = out_dir / f"res_trained_gb_{timestamp}.pkl"
joblib.dump(model, model_path)
fi = getattr(model, "feature_importances_", None)
if fi is not None:
    pd.DataFrame({"feature_index": np.arange(len(fi)), "importance": fi}) \
      .sort_values("importance", ascending=False) \
      .to_csv(out_dir / f"feature_importances_{timestamp}.csv", index=False)
print(f"[RES] model saved → {model_path}")

# %% [markdown]
# ## Step 2: Test on RCPS (no retraining)

# %%
rcps_pts = read_las_points_xyz(RCPS_POINT_CLOUD_PATH)
print(f"[RCPS] points: {len(rcps_pts):,}")

rcps_piles_xy, rcps_epsg = load_and_reproject_kml_to_utm_points(RCPS_BUFFER_KML_PATH)
print(f"[RCPS] pile locations: {len(rcps_piles_xy)}  CRS={rcps_epsg}")

rcps_patches, rcps_kept_xy = extract_patches(rcps_pts, rcps_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)
print(f"[RCPS] patches extracted: {len(rcps_patches)}")

if len(rcps_patches) == 0:
    print("[RCPS] No patches extracted — check CRS/radius/min_points.")
    X_rcps = np.empty((0, X_train_res.shape[1]), dtype=np.float32)
else:
    X_rcps = extract_features(rcps_patches)
    assert X_rcps.shape[1] == 22, "Feature drift: expected 22"

y_prob_rcps = model.predict_proba(X_rcps)[:,1] if len(X_rcps) else np.array([])
y_pred_rcps = (y_prob_rcps >= best["threshold"]).astype(int) if len(y_prob_rcps) else np.array([])

# Recall on RCPS (since inputs are piles)
rcps_detection_rate = float(np.mean(y_pred_rcps)) if len(y_pred_rcps) else 0.0

# RES positive recall at tuned threshold:
res_pos_prob = model.predict_proba(X_pos_res)[:,1]
res_detection_rate = float(np.mean(res_pos_prob >= best["threshold"]))
perf_diff = rcps_detection_rate - res_detection_rate

# Probability stats (sanity)
if len(y_prob_rcps):
    print(f"[RCPS] prob stats → min={y_prob_rcps.min():.3f}, "
          f"p50={np.median(y_prob_rcps):.3f}, max={y_prob_rcps.max():.3f}, "
          f"thr={best['threshold']:.2f}")

print(f"[RCPS] detection rate: {rcps_detection_rate*100:.1f}% | Δ vs RES: {perf_diff*100:+.1f} pp")

# --- RCPS precision probe: negatives from dense sampler ---
precision = None; fpr = None; rcps_negatives_count = 0
print("[RCPS] precision probe with off-pile negatives (dense sampler)...")

rcps_neg_xy = sample_negatives_from_points(
    points_xy=rcps_pts[:, :2],
    n_needed=len(rcps_kept_xy),
    exclude_xy=rcps_piles_xy,
    exclusion_radius=NEGATIVE_EXCLUSION_RADIUS,
    radius=PATCH_RADIUS,
    min_points=MIN_POINTS,
    seed=123,
    jitter_sigma=0.40,
)
rcps_neg_patches, _ = extract_patches(rcps_pts, rcps_neg_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE)
X_rcps_neg = extract_features(rcps_neg_patches) if len(rcps_neg_patches) else np.empty((0,22), dtype=np.float32)

if len(X_rcps_neg):
    y_prob_neg = model.predict_proba(X_rcps_neg)[:,1]
    y_pred_neg = (y_prob_neg >= best["threshold"]).astype(int)
    fp = int(np.sum(y_pred_neg == 1))
    tn = int(np.sum(y_pred_neg == 0))
    rcps_negatives_count = int(len(X_rcps_neg))
    precision = len(y_pred_rcps) / (len(y_pred_rcps) + fp) if (len[y_pred_rcps] if hasattr(y_pred_rcps, '__len__') else 0) + fp > 0 else 0.0
    # safer precision calc without fancy checks:
    precision = float(len(y_pred_rcps) / (len(y_pred_rcps) + fp)) if (len(y_pred_rcps)+fp) > 0 else 0.0
    fpr = fp / (fp + tn) if (fp+tn) > 0 else 0.0
    print(f"[RCPS] negatives probed: {rcps_negatives_count} | FP={fp}, TN={tn} | "
          f"Precision≈{precision:.3f} | FPR={fpr:.3f}")

    pd.DataFrame({
        "utm_x": rcps_neg_xy[:len(y_pred_neg),0],
        "utm_y": rcps_neg_xy[:len(y_pred_neg),1],
        "pred": y_pred_neg,
        "prob": y_prob_neg
    }).to_csv(out_dir / f"rcps_precision_probe_{timestamp}.csv", index=False)
    print(f"[OK] RCPS precision probe CSV → {out_dir / f'rcps_precision_probe_{timestamp}.csv'}")
else:
    print("[RCPS] precision probe skipped (dense sampler produced no valid negatives). Consider lowering MIN_POINTS or raising PATCH_RADIUS slightly.")

# %% [markdown]
# ## Results + Exports

# %%
# Status gate: require both high recall (on piles) and high precision (off-pile probe)
precision_ok = (precision is not None) and (precision >= 0.90)
recall_ok    = (rcps_detection_rate >= 0.90)

if precision_ok and recall_ok:
    status = "EXCELLENT CROSS-SITE GENERALIZATION"
elif (rcps_detection_rate >= 0.70) and (precision is not None) and (precision >= 0.70):
    status = "MODERATE CROSS-SITE GENERALIZATION"
else:
    status = "POOR CROSS-SITE GENERALIZATION"

summary = {
    "experiment_info": {
        "type": "true_model_generalization",
        "training_site": RES_SITE_NAME,
        "testing_site": RCPS_SITE_NAME,
        "timestamp": timestamp,
        "model_file": str(model_path),
        "features": int(X_train_res.shape[1]),
        "threshold": float(best["threshold"]),
        "epsg_res": res_epsg,
        "epsg_rcps": rcps_epsg
    },
    "performance_metrics": {
        "res_precision": float(best["precision"]),
        "res_recall": float(best["recall"]),
        "res_f1": float(best["f1"]),
        "res_accuracy": float(best["accuracy"]),
        "res_detection_rate_on_positives": float(res_detection_rate),
        "rcps_detection_rate": float(rcps_detection_rate),
        "rcps_precision_probe_precision": (float(precision) if precision is not None else None),
        "rcps_precision_probe_fpr": (float(fpr) if fpr is not None else None),
        "rcps_prob_min": (float(y_prob_rcps.min()) if len(y_prob_rcps) else None),
        "rcps_prob_p50": (float(np.median(y_prob_rcps)) if len(y_prob_rcps) else None),
        "rcps_prob_max": (float(y_prob_rcps.max()) if len(y_prob_rcps) else None),
        "performance_difference": float(perf_diff),
        "generalization_status": status
    },
    "data_info": {
        "res_pile_locations": int(len(res_piles_xy)),
        "rcps_pile_locations": int(len(rcps_piles_xy)),
        "train_pos_used": int(len(res_pos_patches)),
        "train_neg_used": int(len(res_neg_patches)),
        "patch_radius": float(PATCH_RADIUS),
        "target_patch_size": int(TARGET_PATCH_SIZE),
        "min_points": int(MIN_POINTS),
        "negatives_source": "strategic+dense" if (neg_from_csv is not None) else "dense_only",
        "negative_exclusion_radius": float(NEGATIVE_EXCLUSION_RADIUS),
        "rcps_precision_probe_negatives": int(rcps_negatives_count)
    }
}
results_file = out_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, "w") as f:
    json.dump(summary, f, indent=2)
print(f"[OK] results saved → {results_file}")

# Export RCPS predictions for QGIS
if len(rcps_kept_xy):
    gdf_rcps = gpd.GeoDataFrame(
        {
            "pile_id": np.arange(len(rcps_kept_xy)),
            "utm_x": rcps_kept_xy[:,0],
            "utm_y": rcps_kept_xy[:,1],
            "predicted_pile": y_pred_rcps.astype(int),
            "confidence": y_prob_rcps.astype(float),
            "training_site": RES_SITE_NAME,
            "test_site": RCPS_SITE_NAME,
            "detection_status": np.where(y_pred_rcps==1, "Detected","Missed"),
            "generalization_type": "cross_site_pile_detection",
        },
        geometry=[Point(xy) for xy in rcps_kept_xy],
        crs=rcps_epsg
    )
    gdf_wgs84 = gdf_rcps.to_crs("EPSG:4326")
    rcps_csv = out_dir / f"rcps_generalization_results_{timestamp}.csv"
    df_out = pd.DataFrame(gdf_rcps.drop(columns="geometry"))
    df_out["longitude"] = gdf_wgs84.geometry.x
    df_out["latitude"]  = gdf_wgs84.geometry.y
    df_out.to_csv(rcps_csv, index=False)
    print(f"[OK] QGIS CSV (with lon/lat) → {rcps_csv}")

    # Optional GeoJSON for direct mapping
    geojson_path = out_dir / f"rcps_generalization_results_{timestamp}.geojson"
    gdf_wgs84.to_file(geojson_path, driver="GeoJSON")
    print(f"[OK] GeoJSON → {geojson_path}")

print("Cross-site generalization test complete.")


# %% [markdown]
# # True Model Generalization Test (RES → RCPS, no retraining)
# Train on RES, select decision threshold on RES, and test as-is on RCPS.
#
# Author: Preetam Balijepalli
# Date: August 2025

# %% [markdown]
# ## Configuration

# %%
from __future__ import annotations
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings("ignore")

# Site names
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# Data paths
RES_POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH  = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

RCPS_POINT_CLOUD_PATH = "../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH  = "../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

# Optional: strategic negatives for RES (UTM X/Y columns: utm_x, utm_y)
RES_STRATEGIC_NEG_CSV = "../../../../data/ground_truth/nortan_res_negative_samples.csv"

# Output
OUTPUT_DIR = "output_runs/true_generalization"

# === Speed preset (choose one) ===
SPEED_MODE = "fast"   # "balanced" or "fast"

# Patch & sampling (defaults; overridden by preset below)
PATCH_RADIUS = 3.0                # meters (same for pos/neg)
MIN_POINTS   = 20                 # before masking
MIN_POINTS_AFTER_MASK = 15        # after masking for negatives
TARGET_PATCH_SIZE = 64
CORE_EXCLUDE_RADIUS = 0.40        # meters: remove pile core points for negative features
RING_INNER_MARGIN  = 0.60         # min offset from core when placing neg centers
RING_OUTER_MARGIN  = 0.20         # keep center inside coverage radius by this margin
MAX_PER_PILE = 2
MAX_ANGLE_TRIALS = 30
PROBE_NEG_MAX = 600               # cap negatives for RCPS precision probe

if SPEED_MODE == "fast":
    MAX_PER_PILE = 1
    MAX_ANGLE_TRIALS = 12
    MIN_POINTS_AFTER_MASK = 12
    TARGET_PATCH_SIZE = 48
    CORE_EXCLUDE_RADIUS = 0.35
    RING_INNER_MARGIN = 0.80
    PROBE_NEG_MAX = 400

# Model & eval
GB_PARAMS = dict(n_estimators=150, max_depth=6, learning_rate=0.08, random_state=42)
THRESH_GRID = [round(x, 2) for x in list(__import__("numpy").linspace(0.2, 0.9, 28))]  # fewer steps

# Logging
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
out_dir = Path(OUTPUT_DIR); out_dir.mkdir(parents=True, exist_ok=True)

# %% [markdown]
# ## Imports

# %%
import json
import numpy as np
import pandas as pd
import joblib
import laspy
import geopandas as gpd
from shapely.geometry import Point
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import precision_recall_fscore_support, accuracy_score

# %% [markdown]
# ## Utilities

# %%
def rng_for(i: int, base_seed: int = 42):
    return np.random.default_rng(base_seed + int(i))

def utm_epsg_from_lonlat(lon: float, lat: float) -> str:
    zone = int(np.floor((lon + 180) / 6) + 1)
    north = lat >= 0
    return f"EPSG:{326 if north else 327}{zone:02d}"

def load_and_reproject_kml_to_utm_points(kml_path: str | Path) -> tuple[np.ndarray, str]:
    gdf = gpd.read_file(kml_path)
    pts_ll = []
    for geom in gdf.geometry:
        if geom is None: continue
        if geom.geom_type == "Point":
            pts_ll.append([geom.x, geom.y])
        elif geom.geom_type in ("Polygon","MultiPolygon","LineString","MultiLineString"):
            c = geom.centroid; pts_ll.append([c.x, c.y])
    if not pts_ll:
        return np.empty((0,2), float), "EPSG:4326"
    pts_ll = np.asarray(pts_ll, float)
    mean_lon, mean_lat = float(np.mean(pts_ll[:,0])), float(np.mean(pts_ll[:,1]))
    utm = utm_epsg_from_lonlat(mean_lon, mean_lat)
    gdf_ll = gpd.GeoDataFrame(geometry=gpd.points_from_xy(pts_ll[:,0], pts_ll[:,1]), crs="EPSG:4326")
    gdf_utm = gdf_ll.to_crs(utm)
    pts_utm = np.array([[p.x, p.y] for p in gdf_utm.geometry], float)
    return pts_utm, utm

def read_las_points_xyz(las_path: str | Path) -> np.ndarray:
    las = laspy.read(las_path)
    return np.vstack([las.x, las.y, las.z]).T.astype(np.float32, copy=False)

def subsample_patch(patch_points: np.ndarray, target_size: int, idx_seed: int) -> np.ndarray:
    n = len(patch_points)
    if n <= target_size: return patch_points
    rs = rng_for(idx_seed)
    idx = rs.choice(n, size=target_size, replace=False)
    return patch_points[idx]

def extract_patches(points_xyz: np.ndarray,
                    locations_xy: np.ndarray,
                    radius: float,
                    min_points: int,
                    target_size: int,
                    tree_points_xy: cKDTree | None = None) -> tuple[list[np.ndarray], np.ndarray]:
    if len(points_xyz) == 0 or len(locations_xy) == 0:
        return [], np.empty((0,2), float)
    tree = tree_points_xy or cKDTree(points_xyz[:, :2])
    patches, kept_locs = [], []
    for i, loc in enumerate(locations_xy):
        idxs = tree.query_ball_point(loc, radius)
        if len(idxs) >= min_points:
            patch = points_xyz[np.asarray(idxs)]
            center = np.array([loc[0], loc[1], float(np.mean(patch[:,2]))], np.float32)
            patch_c = patch - center
            patch_c = subsample_patch(patch_c, target_size, idx_seed=i)
            patches.append(patch_c.astype(np.float32, copy=False))
            kept_locs.append(loc)
    return patches, np.asarray(kept_locs, float)

def extract_features(patches):
    """Exactly 22 features (thesis baseline)."""
    feats, eps = [], 1e-6
    for patch in patches:
        x, y, z = patch[:,0], patch[:,1], patch[:,2]
        r = np.sqrt(x**2 + y**2)
        zmin = np.min(z); h = z - zmin
        feats.append([
            # Basic spatial (9)
            float(np.mean(x)), float(np.std(x)), float(np.ptp(x)),
            float(np.mean(y)), float(np.std(y)), float(np.ptp(y)),
            float(np.mean(z)), float(np.std(z)), float(np.ptp(z)),
            # Height (4)
            float(np.mean(h)), float(np.std(h)),
            float(np.percentile(h, 75)), float(np.percentile(h, 25)),
            # Radial (4)
            float(np.mean(r)), float(np.std(r)), float(np.min(r)), float(np.max(r)),
            # Shape/density (5)
            float(len(patch)),
            float(np.std(x) / (np.std(y) + eps)),
            float(np.std(z) / (np.std(x) + np.std(y) + eps)),
            float(np.percentile(r, 90)),
            float(np.mean(h > np.mean(h))),
        ])
    X = np.asarray(feats, np.float32)
    if X.size and X.shape[1] != 22:
        raise RuntimeError(f"Feature drift: expected 22, got {X.shape[1]}")
    return X

# ---- Core-masked negative patch utilities (runs inside buffers) ----

def mask_core_points(patch_points_xyz: np.ndarray,
                     tree_piles: cKDTree,
                     core_radius: float) -> np.ndarray:
    """Remove points within core_radius (in XY) of any pile center."""
    if len(patch_points_xyz) == 0:
        return patch_points_xyz
    d, _ = tree_piles.query(patch_points_xyz[:, :2], k=1)
    return patch_points_xyz[d > core_radius]

def estimate_coverage_radius(tree_points: cKDTree, points_xy: np.ndarray,
                             center_xy: np.ndarray, search_radius: float) -> float:
    """Rough local coverage radius based on 95th percentile distance of points near the center."""
    idxs = tree_points.query_ball_point(center_xy, r=search_radius)
    if not idxs: return search_radius
    dists = np.linalg.norm(points_xy[idxs] - center_xy, axis=1)
    if len(dists) < 8: return search_radius
    return float(np.percentile(dists, 95))

def sample_negatives_with_core_mask(points_xyz: np.ndarray,
                                    tree_points: cKDTree,
                                    tree_piles: cKDTree,
                                    pile_centers_xy: np.ndarray,
                                    n_needed: int,
                                    radius: float,
                                    min_points_before: int,
                                    min_points_after: int,
                                    core_radius: float,
                                    ring_inner_margin: float,
                                    ring_outer_margin: float,
                                    max_per_pile: int,
                                    max_angle_trials: int,
                                    seed: int = 99) -> tuple[list[np.ndarray], np.ndarray]:
    """
    For each pile center, place candidate centers within the buffer ring, extract patches,
    mask out core points, and keep patches with enough remaining points.
    Returns (patches, centers_xy_kept).
    """
    rs = np.random.default_rng(seed)
    pts_xy = points_xyz[:, :2]

    patches, centers = [], []
    per_pile_counts = np.zeros(len(pile_centers_xy), dtype=int)

    order = np.arange(len(pile_centers_xy))
    rs.shuffle(order)

    for idx in order:
        if len(patches) >= n_needed: break
        c = pile_centers_xy[idx]
        if per_pile_counts[idx] >= max_per_pile: continue

        cov_r = estimate_coverage_radius(tree_points, pts_xy, c, search_radius=radius*2.0)
        r_min = core_radius + ring_inner_margin
        r_max = max(r_min + 0.15, cov_r - ring_outer_margin)
        got_for_this_pile = 0

        for _ in range(max_angle_trials):
            if len(patches) >= n_needed or got_for_this_pile >= max_per_pile: break
            theta = rs.uniform(0, 2*np.pi)
            rr = rs.uniform(r_min, r_max)
            center_xy = c + np.array([rr*np.cos(theta), rr*np.sin(theta)])

            idxs = tree_points.query_ball_point(center_xy, r=radius)
            if len(idxs) < min_points_before: continue
            patch = points_xyz[np.asarray(idxs)]

            patch_masked = mask_core_points(patch, tree_piles, core_radius)
            if len(patch_masked) < min_points_after: continue

            center_z = float(np.mean(patch_masked[:,2]))
            centered = patch_masked - np.array([center_xy[0], center_xy[1], center_z], np.float32)
            centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=len(patches))
            patches.append(centered.astype(np.float32, copy=False))
            centers.append(center_xy)
            got_for_this_pile += 1
            per_pile_counts[idx] += 1

    return patches, (np.asarray(centers, float) if centers else np.empty((0,2), float))

def features_from_core_masked_xy(points_xyz: np.ndarray,
                                 tree_points: cKDTree,
                                 tree_piles: cKDTree,
                                 centers_xy: np.ndarray,
                                 radius: float,
                                 min_points_before: int,
                                 min_points_after: int,
                                 core_radius: float) -> tuple[np.ndarray, list[np.ndarray]]:
    """Extract 22-feature vectors from given centers with core masking (tree reuse)."""
    if len(centers_xy) == 0:
        return np.empty((0,22), np.float32), []
    X_list, patches_list = [], []
    for i, center_xy in enumerate(centers_xy):
        idxs = tree_points.query_ball_point(center_xy, r=radius)
        if len(idxs) < min_points_before: continue
        patch = points_xyz[np.asarray(idxs)]
        patch_m = mask_core_points(patch, tree_piles, core_radius)
        if len(patch_m) < min_points_after: continue

        center_z = float(np.mean(patch_m[:,2]))
        centered = patch_m - np.array([center_xy[0], center_xy[1], center_z], np.float32)
        centered = subsample_patch(centered, TARGET_PATCH_SIZE, idx_seed=i+1234)
        X_list.append(extract_features([centered])[0])
        patches_list.append(centered)
    X = np.asarray(X_list, np.float32) if X_list else np.empty((0,22), np.float32)
    return X, patches_list

def tune_threshold(y_true: np.ndarray, y_prob: np.ndarray, grid: list[float]):
    best = dict(threshold=0.5, f1=-1.0, precision=0.0, recall=0.0, accuracy=0.0)
    y_true = np.asarray(y_true).astype(int)
    for t in grid:
        y_pred = (y_prob >= t).astype(int)
        p, r, f1, _ = precision_recall_fscore_support(y_true, y_pred, average="binary", zero_division=0)
        acc = accuracy_score(y_true, y_pred)
        if f1 > best["f1"]:
            best.update(dict(threshold=float(t), f1=float(f1), precision=float(p), recall=float(r), accuracy=float(acc)))
    return best

# %% [markdown]
# ## Step 1: Train on RES (+ threshold selection)

# %%
# Load points & piles (RES)
res_pts = read_las_points_xyz(RES_POINT_CLOUD_PATH)
print(f"[RES] points: {len(res_pts):,}")

res_piles_xy, res_epsg = load_and_reproject_kml_to_utm_points(RES_BUFFER_KML_PATH)
print(f"[RES] pile locations: {len(res_piles_xy)}  CRS={res_epsg}")

# Build KD-trees once (big speed win)
res_tree_pts = cKDTree(res_pts[:, :2])
res_tree_piles = cKDTree(res_piles_xy)

# Positive patches
res_pos_patches, res_pos_locs = extract_patches(
    res_pts, res_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=res_tree_pts
)
print(f"[RES] positive patches: {len(res_pos_patches)}")
if len(res_pos_patches) == 0:
    raise RuntimeError("No positive patches on RES; check KML / radius / min_points.")
n_pos_full = len(res_pos_patches)

# Strategic negatives (locations) → core-masked features
neg_from_csv = None
try:
    df_neg = pd.read_csv(RES_STRATEGIC_NEG_CSV)
    if {"utm_x","utm_y"}.issubset(df_neg.columns):
        neg_from_csv = df_neg[["utm_x","utm_y"]].to_numpy(float)
        print(f"[RES] strategic negatives loaded (locations): {len(neg_from_csv)}")
    else:
        print("[RES] strategic negatives CSV missing 'utm_x','utm_y' → ignored")
except FileNotFoundError:
    print("[RES] strategic negatives CSV not found → skipping")

X_neg_csv, neg_csv_patches = (np.empty((0,22), np.float32), [])
if neg_from_csv is not None and len(neg_from_csv):
    X_neg_csv, neg_csv_patches = features_from_core_masked_xy(
        res_pts, res_tree_pts, res_tree_piles, neg_from_csv,
        radius=PATCH_RADIUS,
        min_points_before=MIN_POINTS,
        min_points_after=MIN_POINTS_AFTER_MASK,
        core_radius=CORE_EXCLUDE_RADIUS,
    )
    print(f"[RES] negatives from strategic_csv (usable after mask): {len(X_neg_csv)}")

# Within-buffer core-masked negatives (top-up to match positives)
need = max(0, n_pos_full - len(X_neg_csv))
neg_patches_extra, _ = sample_negatives_with_core_mask(
    points_xyz=res_pts,
    tree_points=res_tree_pts,
    tree_piles=res_tree_piles,
    pile_centers_xy=res_piles_xy,
    n_needed=need,
    radius=PATCH_RADIUS,
    min_points_before=MIN_POINTS,
    min_points_after=MIN_POINTS_AFTER_MASK,
    core_radius=CORE_EXCLUDE_RADIUS,
    ring_inner_margin=RING_INNER_MARGIN,
    ring_outer_margin=RING_OUTER_MARGIN,
    max_per_pile=MAX_PER_PILE,
    max_angle_trials=MAX_ANGLE_TRIALS,
    seed=77,
)
X_neg_extra = extract_features(neg_patches_extra) if len(neg_patches_extra) else np.empty((0,22), np.float32)
print(f"[RES] negatives from within-buffer sampler: {len(X_neg_extra)}")

# Final negatives + balance
X_pos_res = extract_features(res_pos_patches)
X_neg_res = np.vstack([arr for arr in (X_neg_csv, X_neg_extra) if len(arr)]) if (len(X_neg_csv) or len(X_neg_extra)) else np.empty((0,22), np.float32)
if len(X_neg_res) == 0:
    raise RuntimeError("No usable negatives; tune CORE_EXCLUDE_RADIUS / RING margins / MIN_POINTS_AFTER_MASK.")

n_use = min(len(X_pos_res), len(X_neg_res))
X_pos_res, X_neg_res = X_pos_res[:n_use], X_neg_res[:n_use]
print(f"[RES] using balanced features → pos={len(X_pos_res)}, neg={len(X_neg_res)}")

# Train
X_train_res = np.vstack([X_pos_res, X_neg_res]).astype(np.float32)
y_train_res = np.hstack([np.ones(len(X_pos_res), int), np.zeros(len(X_neg_res), int)])
print(f"[RES] train samples: {len(X_train_res)} | features: {X_train_res.shape[1]}")

model = GradientBoostingClassifier(**GB_PARAMS).fit(X_train_res, y_train_res)

# Threshold tuning on RES
y_prob_res = model.predict_proba(X_train_res)[:,1]
best = tune_threshold(y_train_res, y_prob_res, THRESH_GRID)
print(f"[RES] tuned threshold = {best['threshold']:.2f} | P={best['precision']:.2f} R={best['recall']:.2f} F1={best['f1']:.2f} Acc={best['accuracy']:.2f}")

# Save model
model_path = out_dir / f"res_trained_gb_{timestamp}.pkl"
joblib.dump(model, model_path)
print(f"[RES] model saved → {model_path}")

# %% [markdown]
# ## Step 2: Test on RCPS (no retraining)

# %%
rcps_pts = read_las_points_xyz(RCPS_POINT_CLOUD_PATH)
print(f"[RCPS] points: {len(rcps_pts):,}")

rcps_piles_xy, rcps_epsg = load_and_reproject_kml_to_utm_points(RCPS_BUFFER_KML_PATH)
print(f"[RCPS] pile locations: {len(rcps_piles_xy)}  CRS={rcps_epsg}")

# Build KD-trees once
rcps_tree_pts = cKDTree(rcps_pts[:, :2])
rcps_tree_piles = cKDTree(rcps_piles_xy)

rcps_patches, rcps_kept_xy = extract_patches(
    rcps_pts, rcps_piles_xy, PATCH_RADIUS, MIN_POINTS, TARGET_PATCH_SIZE, tree_points_xy=rcps_tree_pts
)
print(f"[RCPS] patches extracted: {len(rcps_patches)}")
X_rcps = extract_features(rcps_patches) if len(rcps_patches) else np.empty((0,22), np.float32)

y_prob_rcps = model.predict_proba(X_rcps)[:,1] if len(X_rcps) else np.array([])
y_pred_rcps = (y_prob_rcps >= best["threshold"]).astype(int) if len(y_prob_rcps) else np.array([])
rcps_detection_rate = float(np.mean(y_pred_rcps)) if len(y_pred_rcps) else 0.0

# RES positive recall at tuned threshold:
res_pos_prob = model.predict_proba(X_pos_res)[:,1]
res_detection_rate = float(np.mean(res_pos_prob >= best["threshold"]))
perf_diff = rcps_detection_rate - res_detection_rate

if len(y_prob_rcps):
    print(f"[RCPS] prob stats → min={y_prob_rcps.min():.3f}, p50={np.median(y_prob_rcps):.3f}, max={y_prob_rcps.max():.3f}, thr={best['threshold']:.2f}")
print(f"[RCPS] detection rate: {rcps_detection_rate*100:.1f}% | Δ vs RES: {perf_diff*100:+.1f} pp")

# --- RCPS precision probe: within-buffer core-masked negatives (CAPPED) ---
print("[RCPS] precision probe with core-masked within-buffer negatives...")
probe_n = min(len(rcps_kept_xy), PROBE_NEG_MAX)
neg_patches_probe, _ = sample_negatives_with_core_mask(
    points_xyz=rcps_pts,
    tree_points=rcps_tree_pts,
    tree_piles=rcps_tree_piles,
    pile_centers_xy=rcps_piles_xy,
    n_needed=probe_n,
    radius=PATCH_RADIUS,
    min_points_before=MIN_POINTS,
    min_points_after=MIN_POINTS_AFTER_MASK,
    core_radius=CORE_EXCLUDE_RADIUS,
    ring_inner_margin=RING_INNER_MARGIN,
    ring_outer_margin=RING_OUTER_MARGIN,
    max_per_pile=MAX_PER_PILE,
    max_angle_trials=MAX_ANGLE_TRIALS,
    seed=131,
)
X_rcps_neg = extract_features(neg_patches_probe) if len(neg_patches_probe) else np.empty((0,22), np.float32)

precision = None; fpr = None; rcps_negatives_count = 0
if len(X_rcps_neg):
    y_prob_neg = model.predict_proba(X_rcps_neg)[:,1]
    y_pred_neg = (y_prob_neg >= best["threshold"]).astype(int)
    fp = int(np.sum(y_pred_neg == 1))
    tn = int(np.sum(y_pred_neg == 0))
    rcps_negatives_count = int(len(X_rcps_neg))
    precision = float(len(y_pred_rcps) / (len(y_pred_rcps) + fp)) if (len(y_pred_rcps)+fp) > 0 else 0.0
    fpr = fp / (fp + tn) if (fp+tn) > 0 else 0.0
    print(f"[RCPS] negatives probed (capped): {rcps_negatives_count} | FP={fp}, TN={tn} | Precision≈{precision:.3f} | FPR={fpr:.3f}")
else:
    print("[RCPS] precision probe skipped (no usable core-masked negatives).")

# %% [markdown]
# ## Results + Exports (CSV only; GeoJSON removed for speed)

# %%
# Status gate: require both high recall (on piles) and high precision (off-pile probe)
precision_ok = (precision is not None) and (precision >= 0.90)
recall_ok    = (rcps_detection_rate >= 0.90)

if precision_ok and recall_ok:
    status = "EXCELLENT CROSS-SITE GENERALIZATION"
elif (rcps_detection_rate >= 0.70) and (precision is not None) and (precision >= 0.70):
    status = "MODERATE CROSS-SITE GENERALIZATION"
else:
    status = "POOR CROSS-SITE GENERALIZATION"

summary = {
    "experiment_info": {
        "type": "true_model_generalization",
        "training_site": RES_SITE_NAME,
        "testing_site": RCPS_SITE_NAME,
        "timestamp": timestamp,
        "model_file": str(model_path),
        "features": int(X_train_res.shape[1]),
        "threshold": float(best["threshold"]),
        "epsg_res": res_epsg,
        "epsg_rcps": rcps_epsg
    },
    "performance_metrics": {
        "res_precision": float(best["precision"]),
        "res_recall": float(best["recall"]),
        "res_f1": float(best["f1"]),
        "res_accuracy": float(best["accuracy"]),
        "res_detection_rate_on_positives": float(res_detection_rate),
        "rcps_detection_rate": float(rcps_detection_rate),
        "rcps_precision_probe_precision": (float(precision) if precision is not None else None),
        "rcps_precision_probe_fpr": (float(fpr) if fpr is not None else None),
        "rcps_prob_min": (float(y_prob_rcps.min()) if len(y_prob_rcps) else None),
        "rcps_prob_p50": (float(np.median(y_prob_rcps)) if len(y_prob_rcps) else None),
        "rcps_prob_max": (float(y_prob_rcps.max()) if len(y_prob_rcps) else None),
        "performance_difference": float(perf_diff),
        "generalization_status": status
    },
    "data_info": {
        "res_pile_locations": int(len(res_piles_xy)),
        "rcps_pile_locations": int(len(rcps_piles_xy)),
        "train_pos_used": int(len(X_pos_res)),
        "train_neg_used": int(len(X_neg_res)),
        "patch_radius": float(PATCH_RADIUS),
        "target_patch_size": int(TARGET_PATCH_SIZE),
        "min_points": int(MIN_POINTS),
        "min_points_after_mask": int(MIN_POINTS_AFTER_MASK),
        "core_exclude_radius": float(CORE_EXCLUDE_RADIUS),
        "rcps_precision_probe_negatives": int(rcps_negatives_count),
        "speed_mode": SPEED_MODE
    }
}
results_file = out_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, "w") as f:
    json.dump(summary, f, indent=2)
print(f"[OK] results saved → {results_file}")

# Export RCPS predictions for QGIS (CSV only)
if len(rcps_kept_xy):
    gdf_rcps = gpd.GeoDataFrame(
        {
            "pile_id": np.arange(len(rcps_kept_xy)),
            "utm_x": rcps_kept_xy[:,0],
            "utm_y": rcps_kept_xy[:,1],
            "predicted_pile": y_pred_rcps.astype(int),
            "confidence": y_prob_rcps.astype(float),
            "training_site": RES_SITE_NAME,
            "test_site": RCPS_SITE_NAME,
            "detection_status": np.where(y_pred_rcps==1, "Detected","Missed"),
            "generalization_type": "cross_site_pile_detection",
        },
        geometry=[Point(xy) for xy in rcps_kept_xy],
        crs=rcps_epsg
    )
    gdf_wgs84 = gdf_rcps.to_crs("EPSG:4326")
    rcps_csv = out_dir / f"rcps_generalization_results_{timestamp}.csv"
    df_out = pd.DataFrame(gdf_rcps.drop(columns="geometry"))
    df_out["longitude"] = gdf_wgs84.geometry.x
    df_out["latitude"]  = gdf_wgs84.geometry.y
    df_out.to_csv(rcps_csv, index=False)
    print(f"[OK] QGIS CSV (with lon/lat) → {rcps_csv}")

print("Cross-site generalization test complete.")
