# Parameters
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# RES data paths
RES_POINT_CLOUD_PATH = "../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH = "../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

# RCPS data paths
RCPS_POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH = "../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

OUTPUT_DIR = "output_runs/true_generalization"

# Patch parameters (consistent across both sites)
PATCH_RADIUS = 3.0  # meters
MIN_POINTS = 20
TARGET_PATCH_SIZE = 64  # points per patch

print(f"TRUE MODEL GENERALIZATION TEST")
print(f"Training site: {RES_SITE_NAME}")
print(f"Testing site: {RCPS_SITE_NAME}")
print(f"No retraining - pure model transfer")

import numpy as np
import pandas as pd
import json
import joblib
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Point cloud and spatial
import laspy
import geopandas as gpd
from scipy.spatial import cKDTree

# ML
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score

# Viz
import matplotlib.pyplot as plt

print("Libraries imported")

# Create output dir
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)  # For reproducibility
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):
    """Extract patches around locations"""
    print(f"Extracting patches (radius={radius}m, min_points={min_points}, target_size={TARGET_PATCH_SIZE})")
    
    tree = cKDTree(points[:, :2])
    patches = []
    valid_locs = []
    original_sizes = []
    
    for i, loc in enumerate(locations):
        indices = tree.query_ball_point(loc[:2], radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            original_sizes.append(len(patch_points))
            
            # Subsample to target size
            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
            
            # Center patch
            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])
            centered_patch = patch_points - center
            
            patches.append(centered_patch)
            valid_locs.append(loc)
    
    print(f"Extracted {len(patches)} valid patches")
    if original_sizes:
        print(f"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}")
        final_sizes = [len(p) for p in patches]
        print(f"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}")
    
    return patches, np.array(valid_locs)

def extract_features(patches):
    """Extract 22 features from patches"""
    features = []
    
    for patch in patches:
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        
        # Compute derived features
        radial_dist = np.sqrt(x**2 + y**2)
        height_above_min = z - np.min(z)
        
        # Statistical features (22 features total)
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch),  # num_points
            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio
            np.percentile(radial_dist, 90),  # 90th percentile radial distance
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height
        ]
        features.append(feature_vector)
    
    return np.array(features)

def load_and_reproject_kml(kml_path):
    """Load KML and reproject to UTM Zone 14N/15N"""
    gdf = gpd.read_file(kml_path)
    
    # Extract coordinates from polygon centroids
    pile_coords = []
    for geom in gdf.geometry:
        if geom.geom_type == 'Point':
            pile_coords.append([geom.x, geom.y])
        elif geom.geom_type == 'Polygon':
            centroid = geom.centroid
            pile_coords.append([centroid.x, centroid.y])
    
    pile_locations = np.array(pile_coords)
    
    # Create GeoDataFrame and reproject
    gdf_geo = gpd.GeoDataFrame(
        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),
        crs='EPSG:4326'  # WGS84 geographic
    )
    
    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)
    if 'nortan' in kml_path:
        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N
    else:
        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N
    
    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])
    
    return pile_locations_utm

print("Utility functions defined")

# STEP 1: TRAIN MODEL ON RES DATA
print("\n" + "=" * 60)
print("STEP 1: TRAINING MODEL ON RES DATA")
print("=" * 60)

# Load RES point cloud
print(f"\nLoading RES point cloud: {RES_POINT_CLOUD_PATH}")
res_las = laspy.read(RES_POINT_CLOUD_PATH)
res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T
print(f"Loaded {len(res_points):,} points")

# Load RES pile locations
print(f"\nLoading RES pile locations: {RES_BUFFER_KML_PATH}")
res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)
print(f"Loaded {len(res_pile_locations)} pile locations")

# Extract positive patches (pile locations)
print(f"\nExtracting positive patches from RES...")
res_pos_patches, _ = extract_patches(res_points, res_pile_locations)
print(f"Extracted {len(res_pos_patches)} positive patches")

# Create negative samples for RES
print(f"\nCreating negative samples for RES...")
np.random.seed(42)
x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()
y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()

n_negative = len(res_pos_patches)
random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)
random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)
random_locations = np.column_stack([random_x, random_y])

res_neg_patches, _ = extract_patches(res_points, random_locations)
print(f"Created {len(res_neg_patches)} negative patches")

# Extract features
print(f"\nExtracting features from RES patches...")
X_pos_res = extract_features(res_pos_patches)
X_neg_res = extract_features(res_neg_patches)

# Combine training data
X_train_res = np.vstack([X_pos_res, X_neg_res])
y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])

print(f"RES training dataset: {len(X_train_res)} samples ({np.sum(y_train_res)} positive, {len(y_train_res) - np.sum(y_train_res)} negative)")
print(f"Features per sample: {X_train_res.shape[1]}")

# Train model on RES data
print(f"\nTraining Gradient Boosting model on RES data...")
res_model = GradientBoostingClassifier(
    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42
)
res_model.fit(X_train_res, y_train_res)

# Test model on RES data (sanity check)
y_pred_res = res_model.predict(X_pos_res)
y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]
res_detection_rate = np.mean(y_pred_res)
res_avg_confidence = np.mean(y_prob_res)

print(f"\nRES model performance (sanity check):")
print(f"  Detection rate on RES piles: {res_detection_rate*100:.1f}%")
print(f"  Average confidence: {res_avg_confidence:.3f}")

# Save trained model
model_file = output_dir / f"res_trained_model_{timestamp}.pkl"
joblib.dump(res_model, model_file)
print(f"\nModel saved: {model_file}")
print(f"RES training complete!")

# STEP 2: TEST MODEL ON RCPS DATA (NO RETRAINING)
print("\n" + "=" * 60)
print("STEP 2: TESTING RES MODEL ON RCPS DATA")
print("=" * 60)
print("NO RETRAINING - PURE MODEL TRANSFER")

# Load RCPS point cloud
print(f"\nLoading RCPS point cloud: {RCPS_POINT_CLOUD_PATH}")
rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)
rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T
print(f"Loaded {len(rcps_points):,} points")

# Load RCPS pile locations
print(f"\nLoading RCPS pile locations: {RCPS_BUFFER_KML_PATH}")
rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)
print(f"Loaded {len(rcps_pile_locations)} pile locations")

# Extract patches from RCPS pile locations
print(f"\nExtracting patches from RCPS pile locations...")
rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)
print(f"Extracted {len(rcps_patches)} patches from RCPS")

# Extract features from RCPS patches
print(f"\nExtracting features from RCPS patches...")
X_rcps = extract_features(rcps_patches)
print(f"RCPS features: {X_rcps.shape}")

# Load trained RES model
print(f"\nLoading trained RES model...")
loaded_model = joblib.load(model_file)
print(f"Model loaded successfully")

# Apply RES model to RCPS data (NO RETRAINING!)
print(f"\nApplying RES-trained model to RCPS data...")
y_pred_rcps = loaded_model.predict(X_rcps)
y_prob_rcps = loaded_model.predict_proba(X_rcps)[:, 1]

rcps_detection_rate = np.mean(y_pred_rcps)
rcps_avg_confidence = np.mean(y_prob_rcps)

# Results
print(f"\nTRUE MODEL GENERALIZATION RESULTS:")
print(f"  Training site: {RES_SITE_NAME}")
print(f"  Testing site: {RCPS_SITE_NAME}")
print(f"  Known pile locations tested: {len(rcps_pile_locations)}")
print(f"  Valid patches extracted: {len(rcps_patches)}")
print(f"  Detected as piles: {np.sum(y_pred_rcps)} ({rcps_detection_rate*100:.1f}%)")
print(f"  Average confidence: {rcps_avg_confidence:.3f}")

# Compare with RES performance
performance_diff = rcps_detection_rate - res_detection_rate

print(f"\nGENERALIZATION ANALYSIS:")
print(f"  RES performance (training site): {res_detection_rate*100:.1f}%")
print(f"  RCPS performance (test site): {rcps_detection_rate*100:.1f}%")
print(f"  Performance difference: {performance_diff*100:+.1f} percentage points")

# Interpretation
if rcps_detection_rate >= 0.9:
    if abs(performance_diff) <= 0.1:
        status = "EXCELLENT GENERALIZATION"
        interpretation = "Model transfers perfectly across construction sites"
    else:
        status = "GOOD GENERALIZATION"
        interpretation = "Model works well but with some site-specific differences"
elif rcps_detection_rate >= 0.7:
    status = "MODERATE GENERALIZATION"
    interpretation = "Model partially generalizes - may need fine-tuning"
else:
    status = "POOR GENERALIZATION"
    interpretation = "Model fails to generalize - site-specific training needed"

print(f"\nCONCLUSION:")
print(f"  {status}")
print(f"  {interpretation}")

# Save results
results = {
    'experiment_info': {
        'type': 'true_model_generalization',
        'training_site': RES_SITE_NAME,
        'testing_site': RCPS_SITE_NAME,
        'timestamp': timestamp,
        'model_file': str(model_file)
    },
    'performance_metrics': {
        'res_detection_rate': float(res_detection_rate),
        'res_avg_confidence': float(res_avg_confidence),
        'rcps_detection_rate': float(rcps_detection_rate),
        'rcps_avg_confidence': float(rcps_avg_confidence),
        'performance_difference': float(performance_diff),
        'generalization_status': status
    },
    'data_info': {
        'res_pile_locations': len(res_pile_locations),
        'res_patches': len(res_pos_patches),
        'rcps_pile_locations': len(rcps_pile_locations),
        'rcps_patches': len(rcps_patches),
        'patch_radius': PATCH_RADIUS,
        'target_patch_size': TARGET_PATCH_SIZE
    }
}

results_file = output_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, 'w') as f:
    json.dump(results, f, indent=2)

print(f"\nResults saved: {results_file}")

# Export RCPS results for QGIS visualization
print(f"\nExporting RCPS generalization results for QGIS...")

# Create comprehensive results DataFrame for QGIS
rcps_results_df = pd.DataFrame({
    'pile_id': range(len(rcps_pile_locations)),
    'utm_x': rcps_pile_locations[:, 0],
    'utm_y': rcps_pile_locations[:, 1], 
    'predicted_pile': y_pred_rcps,
    'confidence': y_prob_rcps,
    'training_site': RES_SITE_NAME,
    'test_site': RCPS_SITE_NAME,
    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],
    'generalization_type': 'true_model_transfer'
})

# Convert UTM coordinates to geographic (WGS84) for QGIS
import geopandas as gpd
from shapely.geometry import Point

# Create GeoDataFrame with UTM coordinates (Zone 15N for RCPS)
geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]
gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')  # UTM Zone 15N

# Convert to WGS84 for QGIS
gdf_wgs84 = gdf.to_crs('EPSG:4326')

# Extract lat/lon from geometry
rcps_results_df['longitude'] = gdf_wgs84.geometry.x
rcps_results_df['latitude'] = gdf_wgs84.geometry.y

# Save CSV for QGIS
csv_file = output_dir / f"rcps_generalization_results_{timestamp}.csv"
rcps_results_df.to_csv(csv_file, index=False)

print(f"QGIS CSV exported: {csv_file}")
print(f"Columns: pile_id, utm_x, utm_y, longitude, latitude, predicted_pile, confidence, detection_status, training_site, test_site")
print(f"Coordinate System: WGS84 (EPSG:4326) + UTM Zone 15N")
print(f"Total RCPS points: {len(rcps_results_df)}")

# Generalization summary for QGIS
detected_count = np.sum(y_pred_rcps)
missed_count = len(y_pred_rcps) - detected_count

print(f"\nQGIS Generalization Visualization:")
print(f"  Training site: {RES_SITE_NAME} (368 piles)")
print(f"  Test site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)")
print(f"  Detected: {detected_count} piles")
print(f"  Missed: {missed_count} piles")
print(f"  Generalization rate: {rcps_detection_rate*100:.1f}%")
print(f"  Average confidence: {rcps_avg_confidence:.3f}")

print(f"\nTrue model generalization test complete!")
print(f"\nSUMMARY:")
print(f"  Trained once on {RES_SITE_NAME}")
print(f"  Tested on {RCPS_SITE_NAME} without retraining")
print(f"  Detection rate: {rcps_detection_rate*100:.1f}%")
print(f"  This is TRUE model generalization!")
print(f"  QGIS visualization ready: {csv_file}")

# FALSE POSITIVE TEST ON RCPS - Critical for generalization validation
if len(rcps_patches) > 0 and 'loaded_model' in locals():
    print("\n" + "=" * 60)
    print("FALSE POSITIVE RATE TEST ON RCPS")
    print("=" * 60)
    print("Testing RES-trained model on RCPS random non-pile locations")
    print("This tests cross-site discrimination ability")
    
    # Generate random test locations in RCPS (away from known piles)
    np.random.seed(456)  # Different seed for RCPS test locations
    n_test_negatives = min(300, len(rcps_patches))  # Test more for larger dataset
    
    # Sample random locations with buffer from known piles
    buffer_distance = 2.0  # 2m buffer from known piles (very reduced for dense RCPS)
    max_attempts = 3000  # Increased attempts for larger site
    test_negative_locations = []
    
    rcps_x_min, rcps_x_max = rcps_points[:, 0].min(), rcps_points[:, 0].max()
    rcps_y_min, rcps_y_max = rcps_points[:, 1].min(), rcps_points[:, 1].max()
    
    for attempt in range(max_attempts):
        if len(test_negative_locations) >= n_test_negatives:
            break
            
        # Random location within RCPS point cloud bounds (reduced margins)
        test_x = np.random.uniform(rcps_x_min + 50, rcps_x_max - 50)
        test_y = np.random.uniform(rcps_y_min + 50, rcps_y_max - 50)
        test_loc = np.array([test_x, test_y])
        
        # Check distance from all known RCPS piles
        distances = np.sqrt(np.sum((rcps_pile_locations[:, :2] - test_loc)**2, axis=1))
        min_distance = np.min(distances)
        
        # Only use if far enough from known piles
        if min_distance > buffer_distance:
            test_negative_locations.append(test_loc)
    
    test_negative_locations = np.array(test_negative_locations)
    print(f"Generated {len(test_negative_locations)} RCPS test locations (>{buffer_distance}m from known piles)")
    print(f"Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives")
    
    if len(test_negative_locations) > 0:
        # Extract patches from RCPS test negative locations (very relaxed parameters)
        test_neg_patches, _ = extract_patches(rcps_points, test_negative_locations, 1.5, 3)  # Very small radius, minimal points
        
        if len(test_neg_patches) > 0:
            # Subsample test negative patches
            test_neg_patches = [subsample_patch(patch, TARGET_PATCH_SIZE) for patch in test_neg_patches]
            
            # Extract features and predict using RES-trained model
            X_test_neg_rcps = extract_features(test_neg_patches)
            y_pred_test_neg = loaded_model.predict(X_test_neg_rcps)
            y_prob_test_neg = loaded_model.predict_proba(X_test_neg_rcps)[:, 1]
            
            false_positive_rate_rcps = np.mean(y_pred_test_neg)
            avg_confidence_fp_rcps = np.mean(y_prob_test_neg)
            
            print(f"\n📊 RCPS FALSE POSITIVE ANALYSIS:")
            print(f"  RCPS test negative locations: {len(test_negative_locations)}")
            print(f"  Valid negative patches: {len(test_neg_patches)}")
            print(f"  False positives: {np.sum(y_pred_test_neg)} / {len(y_pred_test_neg)}")
            print(f"  False positive rate: {false_positive_rate_rcps*100:.1f}%")
            print(f"  Avg confidence on negatives: {avg_confidence_fp_rcps:.3f}")
            
            # Interpretation
            if false_positive_rate_rcps <= 0.1:  # ≤10% false positives
                fp_status_rcps = "✅ EXCELLENT - Low false positives across sites"
            elif false_positive_rate_rcps <= 0.2:  # ≤20% false positives
                fp_status_rcps = "✅ GOOD - Acceptable false positives across sites"
            elif false_positive_rate_rcps <= 0.4:  # ≤40% false positives
                fp_status_rcps = "⚠️ MODERATE - Some false positives across sites"
            else:
                fp_status_rcps = "❌ POOR - Too many false positives across sites"
            
            print(f"  {fp_status_rcps}")
            
            print(f"\n🎯 COMPLETE GENERALIZATION VALIDATION:")
            print(f"  Training site: {RES_SITE_NAME}")
            print(f"  Test site: {RCPS_SITE_NAME}")
            print(f"  Pile detection rate: {rcps_detection_rate*100:.1f}%")
            print(f"  False positive rate: {false_positive_rate_rcps*100:.1f}%")
            print(f"  Cross-site performance: {fp_status_rcps}")
            
            # Overall assessment
            if rcps_detection_rate >= 0.9 and false_positive_rate_rcps <= 0.2:
                overall_status = "🎉 EXCELLENT GENERALIZATION - Ready for deployment"
            elif rcps_detection_rate >= 0.8 and false_positive_rate_rcps <= 0.3:
                overall_status = "✅ GOOD GENERALIZATION - Minor tuning may help"
            else:
                overall_status = "⚠️ GENERALIZATION ISSUES - Needs investigation"
            
            print(f"\n🏆 FINAL ASSESSMENT: {overall_status}")
            
            # Add to CSV for QGIS (negative test points)
            if len(test_negative_locations) > 0:
                # Create negative test results DataFrame
                # Fix array length mismatch: use only valid patches
                neg_test_df = pd.DataFrame({
                    'pile_id': [f'neg_test_{i}' for i in range(len(test_neg_patches))],
                    'utm_x': test_negative_locations[:len(test_neg_patches), 0],
                    'utm_y': test_negative_locations[:len(test_neg_patches), 1],
                    'predicted_pile': y_pred_test_neg,
                    'confidence': y_prob_test_neg,
                    'training_site': RES_SITE_NAME,
                    'test_site': RCPS_SITE_NAME,
                    'detection_status': ['False_Positive' if pred == 1 else 'True_Negative' for pred in y_pred_test_neg],
                    'generalization_type': 'negative_test'
                })
                
                # Convert to geographic coordinates (only valid patches)
                geometry_neg = [Point(xy) for xy in zip(test_negative_locations[:len(test_neg_patches), 0], test_negative_locations[:len(test_neg_patches), 1])]
                gdf_neg = gpd.GeoDataFrame(neg_test_df, geometry=geometry_neg, crs='EPSG:32615')  # UTM Zone 15N
                gdf_neg_wgs84 = gdf_neg.to_crs('EPSG:4326')
                
                neg_test_df['longitude'] = gdf_neg_wgs84.geometry.x
                neg_test_df['latitude'] = gdf_neg_wgs84.geometry.y
                
                # Save negative test results CSV
                neg_csv_file = output_dir / f"rcps_negative_test_results_{timestamp}.csv"
                neg_test_df.to_csv(neg_csv_file, index=False)
                
                print(f"\n📍 QGIS Negative Test CSV: {neg_csv_file}")
                print(f"   Use this to visualize false positive locations")
                print(f"   False positives: {np.sum(y_pred_test_neg)} red points")
                print(f"   True negatives: {len(y_pred_test_neg) - np.sum(y_pred_test_neg)} green points")
            
        else:
            print(f"⚠️ Could not extract valid patches from RCPS test negative locations")
    else:
        print(f"⚠️ Could not generate sufficient RCPS test negative locations")
        
else:
    print("No model available for RCPS false positive testing")

# %% [markdown]
# # True Model Generalization Test
# 
# Train model on RES data, test on RCPS data without retraining.
# 
# **Author**: Preetam Balijepalli  
# **Date**: August 2025

# %% [markdown]
# ## Configuration

# %%
# Parameters
RES_SITE_NAME = "nortan_res"
RCPS_SITE_NAME = "althea_rcps"

# Data paths
RES_POINT_CLOUD_PATH = "../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las"
RES_BUFFER_KML_PATH = "../../../../../data/raw/nortan_res/kml/Buffer_2m.kml"

RCPS_POINT_CLOUD_PATH = "../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las"
RCPS_BUFFER_KML_PATH = "../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml"

OUTPUT_DIR = "output_runs/true_generalization"

# Patch parameters
PATCH_RADIUS = 3.0
MIN_POINTS = 20
TARGET_PATCH_SIZE = 64

# %% [markdown]
# ## Imports

# %%
import numpy as np
import pandas as pd
import json
import joblib
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

import laspy
import geopandas as gpd
from scipy.spatial import cKDTree
from sklearn.ensemble import GradientBoostingClassifier
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
import matplotlib.pyplot as plt

# Create output directory
output_dir = Path(OUTPUT_DIR)
output_dir.mkdir(parents=True, exist_ok=True)
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

# %% [markdown]
# ## Utility Functions

# %%
def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):
    """Subsample patch to target size"""
    if len(patch_points) <= target_size:
        return patch_points
    
    np.random.seed(42)
    indices = np.random.choice(len(patch_points), target_size, replace=False)
    return patch_points[indices]

def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):
    """Extract patches around locations"""
    tree = cKDTree(points[:, :2])
    patches = []
    valid_locs = []
    
    for i, loc in enumerate(locations):
        indices = tree.query_ball_point(loc[:2], radius)
        
        if len(indices) >= min_points:
            patch_points = points[indices]
            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)
            
            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])
            centered_patch = patch_points - center
            
            patches.append(centered_patch)
            valid_locs.append(loc)
    
    return patches, np.array(valid_locs)

def extract_features(patches):
    """Extract 22 features from patches"""
    features = []
    
    for patch in patches:
        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]
        radial_dist = np.sqrt(x**2 + y**2)
        height_above_min = z - np.min(z)
        
        feature_vector = [
            # Basic spatial statistics (9 features)
            np.mean(x), np.std(x), np.max(x) - np.min(x),
            np.mean(y), np.std(y), np.max(y) - np.min(y),
            np.mean(z), np.std(z), np.max(z) - np.min(z),
            
            # Height-based features (4 features)
            np.mean(height_above_min), np.std(height_above_min),
            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),
            
            # Radial distance features (4 features)
            np.mean(radial_dist), np.std(radial_dist),
            np.min(radial_dist), np.max(radial_dist),
            
            # Shape and density features (5 features)
            len(patch),
            np.std(x) / (np.std(y) + 1e-6),
            np.std(z) / (np.std(x) + np.std(y) + 1e-6),
            np.percentile(radial_dist, 90),
            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),
        ]
        features.append(feature_vector)
    
    return np.array(features)

def load_and_reproject_kml(kml_path):
    """Load KML and reproject to UTM"""
    gdf = gpd.read_file(kml_path)
    
    pile_coords = []
    for geom in gdf.geometry:
        if geom.geom_type == 'Point':
            pile_coords.append([geom.x, geom.y])
        elif geom.geom_type == 'Polygon':
            centroid = geom.centroid
            pile_coords.append([centroid.x, centroid.y])
    
    pile_locations = np.array(pile_coords)
    
    gdf_geo = gpd.GeoDataFrame(
        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),
        crs='EPSG:4326'
    )
    
    # Reproject to appropriate UTM zone
    if 'nortan' in str(kml_path):
        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N
    else:
        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N
    
    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])
    return pile_locations_utm

# %% [markdown]
# ## Step 1: Train Model on RES Data

# %%
# Load RES point cloud
res_las = laspy.read(RES_POINT_CLOUD_PATH)
res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T
print(f"RES points: {len(res_points):,}")

# Load RES pile locations
res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)
print(f"RES pile locations: {len(res_pile_locations)}")

# Extract positive patches
res_pos_patches, _ = extract_patches(res_points, res_pile_locations)
print(f"RES positive patches: {len(res_pos_patches)}")

# %%
# Create negative samples using random sampling within point cloud bounds
print("Creating negative samples within point cloud coverage...")

np.random.seed(42)
x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()
y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()

n_negative = len(res_pos_patches)
random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)
random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)
random_locations = np.column_stack([random_x, random_y])

res_neg_patches, _ = extract_patches(res_points, random_locations)
print(f"RES negative patches: {len(res_neg_patches)}")
print("Note: Negatives may include pile-adjacent areas due to data filtering")

# %%
# Prepare training data
X_pos_res = extract_features(res_pos_patches)
X_neg_res = extract_features(res_neg_patches)

X_train_res = np.vstack([X_pos_res, X_neg_res])
y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])

print(f"Training data: {len(X_train_res)} samples")
print(f"Positive: {np.sum(y_train_res)}, Negative: {len(y_train_res) - np.sum(y_train_res)}")

# %%
# Train model
res_model = GradientBoostingClassifier(
    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42
)
res_model.fit(X_train_res, y_train_res)

# Sanity check on RES
y_pred_res = res_model.predict(X_pos_res)
y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]
res_detection_rate = np.mean(y_pred_res)

print(f"RES detection rate: {res_detection_rate*100:.1f}%")

# Save model
model_file = output_dir / f"res_trained_model_{timestamp}.pkl"
joblib.dump(res_model, model_file)
print(f"Model saved: {model_file}")

# %% [markdown]
# ## Step 2: Test on RCPS Data (No Retraining)

# %%
# Load RCPS data
rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)
rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T
print(f"RCPS points: {len(rcps_points):,}")

rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)
print(f"RCPS pile locations: {len(rcps_pile_locations)}")

# %%
# Extract RCPS patches
rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)
print(f"RCPS patches: {len(rcps_patches)}")

# Extract features
X_rcps = extract_features(rcps_patches)

# Apply trained model (no retraining)
y_pred_rcps = res_model.predict(X_rcps)
y_prob_rcps = res_model.predict_proba(X_rcps)[:, 1]

rcps_detection_rate = np.mean(y_pred_rcps)
performance_diff = rcps_detection_rate - res_detection_rate

print(f"RCPS detection rate: {rcps_detection_rate*100:.1f}%")
print(f"Performance difference: {performance_diff*100:+.1f} percentage points")



# %% [markdown]
# ## Results and Export

# %%
# Results summary
print("CROSS-SITE GENERALIZATION RESULTS:")
print(f"Training site: {RES_SITE_NAME} ({len(res_pile_locations)} piles)")
print(f"Testing site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)")
print(f"Cross-site detection rate: {rcps_detection_rate*100:.1f}%")
print(f"Performance difference: {performance_diff*100:+.1f} percentage points")

# Overall generalization assessment
if rcps_detection_rate >= 0.9:
    if abs(performance_diff) <= 0.1:
        status = "EXCELLENT CROSS-SITE GENERALIZATION"
    else:
        status = "GOOD CROSS-SITE GENERALIZATION"
elif rcps_detection_rate >= 0.7:
    status = "MODERATE CROSS-SITE GENERALIZATION"
else:
    status = "POOR CROSS-SITE GENERALIZATION"

print(f"Overall status: {status}")
print("Note: Point cloud data pre-filtered to pile buffer regions")

# %%
# Save results
results = {
    'experiment_info': {
        'type': 'true_model_generalization',
        'training_site': RES_SITE_NAME,
        'testing_site': RCPS_SITE_NAME,
        'timestamp': timestamp,
        'model_file': str(model_file)
    },
    'performance_metrics': {
        'res_detection_rate': float(res_detection_rate),
        'rcps_detection_rate': float(rcps_detection_rate),
        'performance_difference': float(performance_diff),
        'generalization_status': status
    },
    'data_info': {
        'res_pile_locations': len(res_pile_locations),
        'rcps_pile_locations': len(rcps_pile_locations),
        'patch_radius': PATCH_RADIUS,
        'target_patch_size': TARGET_PATCH_SIZE,
        'data_scope': 'pile_buffer_regions_only'
    }
}

results_file = output_dir / f"true_generalization_results_{timestamp}.json"
with open(results_file, 'w') as f:
    json.dump(results, f, indent=2)

print(f"Results saved: {results_file}")

# %%
# Export for QGIS visualization
rcps_results_df = pd.DataFrame({
    'pile_id': range(len(rcps_pile_locations)),
    'utm_x': rcps_pile_locations[:, 0],
    'utm_y': rcps_pile_locations[:, 1], 
    'predicted_pile': y_pred_rcps,
    'confidence': y_prob_rcps,
    'training_site': RES_SITE_NAME,
    'test_site': RCPS_SITE_NAME,
    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],
    'generalization_type': 'cross_site_pile_detection'
})

# Convert to geographic coordinates
from shapely.geometry import Point
geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]
gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')
gdf_wgs84 = gdf.to_crs('EPSG:4326')

rcps_results_df['longitude'] = gdf_wgs84.geometry.x
rcps_results_df['latitude'] = gdf_wgs84.geometry.y

csv_file = output_dir / f"rcps_generalization_results_{timestamp}.csv"
rcps_results_df.to_csv(csv_file, index=False)

print(f"QGIS CSV exported: {csv_file}")
print("Cross-site generalization test complete")