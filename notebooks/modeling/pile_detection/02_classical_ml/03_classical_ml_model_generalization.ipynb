{"cells": [{"cell_type": "markdown", "id": "header", "metadata": {}, "source": ["# True Model Generalization Test\n", "\n", "Test **true model generalization**: Train model on RES data, test on RCPS data without retraining.\n", "\n", "**Research Question**: Can a model trained on one construction site work on another site without retraining?\n", "\n", "**Approach**: \n", "1. Train model on RES data (with negative samples)\n", "2. Save trained model\n", "3. Load model and test on RCPS data (no retraining)\n", "4. Measure true generalization performance\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: August 2025"]}, {"cell_type": "code", "execution_count": 7, "id": "params", "metadata": {"tags": ["parameters"]}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TRUE MODEL GENERALIZATION TEST\n", "Training site: nortan_res\n", "Testing site: althea_rcps\n", "No retraining - pure model transfer\n"]}], "source": ["# Parameters\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# RES data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH = \"../../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "# RCPS data paths\n", "RCPS_POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH = \"../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# Patch parameters (consistent across both sites)\n", "PATCH_RADIUS = 3.0  # meters\n", "MIN_POINTS = 20\n", "TARGET_PATCH_SIZE = 64  # points per patch\n", "\n", "print(f\"TRUE MODEL GENERALIZATION TEST\")\n", "print(f\"Training site: {RES_SITE_NAME}\")\n", "print(f\"Testing site: {RCPS_SITE_NAME}\")\n", "print(f\"No retraining - pure model transfer\")"]}, {"cell_type": "code", "execution_count": 8, "id": "imports", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported\n"]}], "source": ["import numpy as np\n", "import pandas as pd\n", "import json\n", "import joblib\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Point cloud and spatial\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "\n", "# ML\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "\n", "# Viz\n", "import matplotlib.pyplot as plt\n", "\n", "print(\"Libraries imported\")\n", "\n", "# Create output dir\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")"]}, {"cell_type": "code", "execution_count": 9, "id": "utility_functions", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Utility functions defined\n"]}], "source": ["def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)  # For reproducibility\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):\n", "    \"\"\"Extract patches around locations\"\"\"\n", "    print(f\"Extracting patches (radius={radius}m, min_points={min_points}, target_size={TARGET_PATCH_SIZE})\")\n", "    \n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    original_sizes = []\n", "    \n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            original_sizes.append(len(patch_points))\n", "            \n", "            # Subsample to target size\n", "            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "            \n", "            # Center patch\n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "            \n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "    \n", "    print(f\"Extracted {len(patches)} valid patches\")\n", "    if original_sizes:\n", "        print(f\"Original sizes: min={min(original_sizes)}, max={max(original_sizes)}, mean={np.mean(original_sizes):.1f}\")\n", "        final_sizes = [len(p) for p in patches]\n", "        print(f\"Final sizes: min={min(final_sizes)}, max={max(final_sizes)}, mean={np.mean(final_sizes):.1f}\")\n", "    \n", "    return patches, np.array(valid_locs)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Extract 22 features from patches\"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        \n", "        # Compute derived features\n", "        radial_dist = np.sqrt(x**2 + y**2)\n", "        height_above_min = z - np.min(z)\n", "        \n", "        # Statistical features (22 features total)\n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch),  # num_points\n", "            np.std(x) / (np.std(y) + 1e-6),  # aspect_ratio\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),  # height_to_footprint_ratio\n", "            np.percentile(radial_dist, 90),  # 90th percentile radial distance\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),  # fraction above mean height\n", "        ]\n", "        features.append(feature_vector)\n", "    \n", "    return np.array(features)\n", "\n", "def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM Zone 14N/15N\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    # Extract coordinates from polygon centroids\n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    # Create GeoDataFrame and reproject\n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'  # WGS84 geographic\n", "    )\n", "    \n", "    # Reproject to UTM (Zone 14N for RES, Zone 15N for RCPS)\n", "    if 'nortan' in kml_path:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "    \n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    \n", "    return pile_locations_utm\n", "\n", "print(\"Utility functions defined\")"]}, {"cell_type": "code", "execution_count": 10, "id": "train_on_res", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "STEP 1: TRAINING MODEL ON RES DATA\n", "============================================================\n", "\n", "Loading RES point cloud: ../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\n", "Loaded 35,565,352 points\n", "\n", "Loading RES pile locations: ../../../../../data/raw/nortan_res/kml/Buffer_2m.kml\n", "Loaded 368 pile locations\n", "\n", "Extracting positive patches from RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 368 valid patches\n", "Original sizes: min=86195, max=152283, mean=96645.0\n", "Final sizes: min=64, max=64, mean=64.0\n", "Extracted 368 positive patches\n", "\n", "Creating negative samples for RES...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 357 valid patches\n", "Original sizes: min=65, max=105814, mean=43152.4\n", "Final sizes: min=64, max=64, mean=64.0\n", "Created 357 negative patches\n", "\n", "Extracting features from RES patches...\n", "RES training dataset: 725 samples (368.0 positive, 357.0 negative)\n", "Features per sample: 22\n", "\n", "Training Gradient Boosting model on RES data...\n", "\n", "RES model performance (sanity check):\n", "  Detection rate on RES piles: 100.0%\n", "  Average confidence: 1.000\n", "\n", "Model saved: output_runs/true_generalization/res_trained_model_20250807_214117.pkl\n", "RES training complete!\n"]}], "source": ["# STEP 1: TRAIN MODEL ON RES DATA\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"STEP 1: TRAINING MODEL ON RES DATA\")\n", "print(\"=\" * 60)\n", "\n", "# Load RES point cloud\n", "print(f\"\\nLoading RES point cloud: {RES_POINT_CLOUD_PATH}\")\n", "res_las = laspy.read(RES_POINT_CLOUD_PATH)\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"Loaded {len(res_points):,} points\")\n", "\n", "# Load RES pile locations\n", "print(f\"\\nLoading RES pile locations: {RES_BUFFER_KML_PATH}\")\n", "res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)\n", "print(f\"Loaded {len(res_pile_locations)} pile locations\")\n", "\n", "# Extract positive patches (pile locations)\n", "print(f\"\\nExtracting positive patches from RES...\")\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations)\n", "print(f\"Extracted {len(res_pos_patches)} positive patches\")\n", "\n", "# Create negative samples for RES\n", "print(f\"\\nCreating negative samples for RES...\")\n", "np.random.seed(42)\n", "x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()\n", "y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()\n", "\n", "n_negative = len(res_pos_patches)\n", "random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)\n", "random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)\n", "random_locations = np.column_stack([random_x, random_y])\n", "\n", "res_neg_patches, _ = extract_patches(res_points, random_locations)\n", "print(f\"Created {len(res_neg_patches)} negative patches\")\n", "\n", "# Extract features\n", "print(f\"\\nExtracting features from RES patches...\")\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = extract_features(res_neg_patches)\n", "\n", "# Combine training data\n", "X_train_res = np.vstack([X_pos_res, X_neg_res])\n", "y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])\n", "\n", "print(f\"RES training dataset: {len(X_train_res)} samples ({np.sum(y_train_res)} positive, {len(y_train_res) - np.sum(y_train_res)} negative)\")\n", "print(f\"Features per sample: {X_train_res.shape[1]}\")\n", "\n", "# Train model on RES data\n", "print(f\"\\nTraining Gradient Boosting model on RES data...\")\n", "res_model = GradientBoostingClassifier(\n", "    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42\n", ")\n", "res_model.fit(X_train_res, y_train_res)\n", "\n", "# Test model on RES data (sanity check)\n", "y_pred_res = res_model.predict(X_pos_res)\n", "y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]\n", "res_detection_rate = np.mean(y_pred_res)\n", "res_avg_confidence = np.mean(y_prob_res)\n", "\n", "print(f\"\\nRES model performance (sanity check):\")\n", "print(f\"  Detection rate on RES piles: {res_detection_rate*100:.1f}%\")\n", "print(f\"  Average confidence: {res_avg_confidence:.3f}\")\n", "\n", "# Save trained model\n", "model_file = output_dir / f\"res_trained_model_{timestamp}.pkl\"\n", "joblib.dump(res_model, model_file)\n", "print(f\"\\nModel saved: {model_file}\")\n", "print(f\"RES training complete!\")"]}, {"cell_type": "code", "execution_count": 11, "id": "test_on_rcps", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "STEP 2: TESTING RES MODEL ON RCPS DATA\n", "============================================================\n", "NO RETRAINING - <PERSON>URE MODEL TRANSFER\n", "\n", "Loading RCPS point cloud: ../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\n", "Loaded 52,862,386 points\n", "\n", "Loading RCPS pile locations: ../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\n", "Loaded 1359 pile locations\n", "\n", "Extracting patches from RCPS pile locations...\n", "Extracting patches (radius=3.0m, min_points=20, target_size=64)\n", "Extracted 1359 valid patches\n", "Original sizes: min=35562, max=67519, mean=38898.0\n", "Final sizes: min=64, max=64, mean=64.0\n", "Extracted 1359 patches from RCPS\n", "\n", "Extracting features from RCPS patches...\n", "RCPS features: (1359, 22)\n", "\n", "Loading trained RES model...\n", "Model loaded successfully\n", "\n", "Applying RES-trained model to RCPS data...\n", "\n", "TRUE MODEL GENERALIZATION RESULTS:\n", "  Training site: nortan_res\n", "  Testing site: althea_rcps\n", "  Known pile locations tested: 1359\n", "  Valid patches extracted: 1359\n", "  Detected as piles: 1359.0 (100.0%)\n", "  Average confidence: 1.000\n", "\n", "GENERALIZATION ANALYSIS:\n", "  RES performance (training site): 100.0%\n", "  RCPS performance (test site): 100.0%\n", "  Performance difference: +0.0 percentage points\n", "\n", "CONCLUSION:\n", "  EXCELLENT GENERALIZATION\n", "  Model transfers perfectly across construction sites\n", "\n", "Results saved: output_runs/true_generalization/true_generalization_results_20250807_214117.json\n", "\n", "Exporting RCPS generalization results for QGIS...\n", "QGIS CSV exported: output_runs/true_generalization/rcps_generalization_results_20250807_214117.csv\n", "Columns: pile_id, utm_x, utm_y, longitude, latitude, predicted_pile, confidence, detection_status, training_site, test_site\n", "Coordinate System: WGS84 (EPSG:4326) + UTM Zone 15N\n", "Total RCPS points: 1359\n", "\n", "QGIS Generalization Visualization:\n", "  Training site: nortan_res (368 piles)\n", "  Test site: althea_rcps (1359 piles)\n", "  Detected: 1359.0 piles\n", "  Missed: 0.0 piles\n", "  Generalization rate: 100.0%\n", "  Average confidence: 1.000\n", "\n", "True model generalization test complete!\n", "\n", "SUMMARY:\n", "  Trained once on nortan_res\n", "  Tested on althea_rcps without retraining\n", "  Detection rate: 100.0%\n", "  This is TRUE model generalization!\n", "  QGIS visualization ready: output_runs/true_generalization/rcps_generalization_results_20250807_214117.csv\n"]}], "source": ["# STEP 2: TEST MODEL ON RCPS DATA (NO RETRAINING)\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"STEP 2: TESTING RES MODEL ON RCPS DATA\")\n", "print(\"=\" * 60)\n", "print(\"NO RETRAINING - PURE MODEL TRANSFER\")\n", "\n", "# Load RCPS point cloud\n", "print(f\"\\nLoading RCPS point cloud: {RCPS_POINT_CLOUD_PATH}\")\n", "rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"Loaded {len(rcps_points):,} points\")\n", "\n", "# Load RCPS pile locations\n", "print(f\"\\nLoading RCPS pile locations: {RCPS_BUFFER_KML_PATH}\")\n", "rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)\n", "print(f\"Loaded {len(rcps_pile_locations)} pile locations\")\n", "\n", "# Extract patches from RCPS pile locations\n", "print(f\"\\nExtracting patches from RCPS pile locations...\")\n", "rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)\n", "print(f\"Extracted {len(rcps_patches)} patches from RCPS\")\n", "\n", "# Extract features from RCPS patches\n", "print(f\"\\nExtracting features from RCPS patches...\")\n", "X_rcps = extract_features(rcps_patches)\n", "print(f\"RCPS features: {X_rcps.shape}\")\n", "\n", "# Load trained RES model\n", "print(f\"\\nLoading trained RES model...\")\n", "loaded_model = joblib.load(model_file)\n", "print(f\"Model loaded successfully\")\n", "\n", "# Apply RES model to RCPS data (NO RETRAINING!)\n", "print(f\"\\nApplying RES-trained model to RCPS data...\")\n", "y_pred_rcps = loaded_model.predict(X_rcps)\n", "y_prob_rcps = loaded_model.predict_proba(X_rcps)[:, 1]\n", "\n", "rcps_detection_rate = np.mean(y_pred_rcps)\n", "rcps_avg_confidence = np.mean(y_prob_rcps)\n", "\n", "# Results\n", "print(f\"\\nTRUE MODEL GENERALIZATION RESULTS:\")\n", "print(f\"  Training site: {RES_SITE_NAME}\")\n", "print(f\"  Testing site: {RCPS_SITE_NAME}\")\n", "print(f\"  Known pile locations tested: {len(rcps_pile_locations)}\")\n", "print(f\"  Valid patches extracted: {len(rcps_patches)}\")\n", "print(f\"  Detected as piles: {np.sum(y_pred_rcps)} ({rcps_detection_rate*100:.1f}%)\")\n", "print(f\"  Average confidence: {rcps_avg_confidence:.3f}\")\n", "\n", "# Compare with RES performance\n", "performance_diff = rcps_detection_rate - res_detection_rate\n", "\n", "print(f\"\\nGENERALIZATION ANALYSIS:\")\n", "print(f\"  RES performance (training site): {res_detection_rate*100:.1f}%\")\n", "print(f\"  RCPS performance (test site): {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "# Interpretation\n", "if rcps_detection_rate >= 0.9:\n", "    if abs(performance_diff) <= 0.1:\n", "        status = \"EXCELLENT GENERALIZATION\"\n", "        interpretation = \"Model transfers perfectly across construction sites\"\n", "    else:\n", "        status = \"GOOD GENERALIZATION\"\n", "        interpretation = \"Model works well but with some site-specific differences\"\n", "elif rcps_detection_rate >= 0.7:\n", "    status = \"MODERATE GENERALIZATION\"\n", "    interpretation = \"Model partially generalizes - may need fine-tuning\"\n", "else:\n", "    status = \"POOR GENERALIZATION\"\n", "    interpretation = \"Model fails to generalize - site-specific training needed\"\n", "\n", "print(f\"\\nCONCLUSION:\")\n", "print(f\"  {status}\")\n", "print(f\"  {interpretation}\")\n", "\n", "# Save results\n", "results = {\n", "    'experiment_info': {\n", "        'type': 'true_model_generalization',\n", "        'training_site': RES_SITE_NAME,\n", "        'testing_site': RCPS_SITE_NAME,\n", "        'timestamp': timestamp,\n", "        'model_file': str(model_file)\n", "    },\n", "    'performance_metrics': {\n", "        'res_detection_rate': float(res_detection_rate),\n", "        'res_avg_confidence': float(res_avg_confidence),\n", "        'rcps_detection_rate': float(rcps_detection_rate),\n", "        'rcps_avg_confidence': float(rcps_avg_confidence),\n", "        'performance_difference': float(performance_diff),\n", "        'generalization_status': status\n", "    },\n", "    'data_info': {\n", "        'res_pile_locations': len(res_pile_locations),\n", "        'res_patches': len(res_pos_patches),\n", "        'rcps_pile_locations': len(rcps_pile_locations),\n", "        'rcps_patches': len(rcps_patches),\n", "        'patch_radius': PATCH_RADIUS,\n", "        'target_patch_size': TARGET_PATCH_SIZE\n", "    }\n", "}\n", "\n", "results_file = output_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"\\nResults saved: {results_file}\")\n", "\n", "# Export RCPS results for QGIS visualization\n", "print(f\"\\nExporting RCPS generalization results for QGIS...\")\n", "\n", "# Create comprehensive results DataFrame for QGIS\n", "rcps_results_df = pd.DataFrame({\n", "    'pile_id': range(len(rcps_pile_locations)),\n", "    'utm_x': rcps_pile_locations[:, 0],\n", "    'utm_y': rcps_pile_locations[:, 1], \n", "    'predicted_pile': y_pred_rcps,\n", "    'confidence': y_prob_rcps,\n", "    'training_site': RES_SITE_NAME,\n", "    'test_site': RCPS_SITE_NAME,\n", "    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],\n", "    'generalization_type': 'true_model_transfer'\n", "})\n", "\n", "# Convert UTM coordinates to geographic (WGS84) for QGIS\n", "import geopandas as gpd\n", "from shapely.geometry import Point\n", "\n", "# Create GeoDataFrame with UTM coordinates (Zone 15N for RCPS)\n", "geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]\n", "gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')  # UTM Zone 15N\n", "\n", "# Convert to WGS84 for QGIS\n", "gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "\n", "# Extract lat/lon from geometry\n", "rcps_results_df['longitude'] = gdf_wgs84.geometry.x\n", "rcps_results_df['latitude'] = gdf_wgs84.geometry.y\n", "\n", "# Save CSV for QGIS\n", "csv_file = output_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "rcps_results_df.to_csv(csv_file, index=False)\n", "\n", "print(f\"QGIS CSV exported: {csv_file}\")\n", "print(f\"Columns: pile_id, utm_x, utm_y, longitude, latitude, predicted_pile, confidence, detection_status, training_site, test_site\")\n", "print(f\"Coordinate System: WGS84 (EPSG:4326) + UTM Zone 15N\")\n", "print(f\"Total RCPS points: {len(rcps_results_df)}\")\n", "\n", "# Generalization summary for QGIS\n", "detected_count = np.sum(y_pred_rcps)\n", "missed_count = len(y_pred_rcps) - detected_count\n", "\n", "print(f\"\\nQGIS Generalization Visualization:\")\n", "print(f\"  Training site: {RES_SITE_NAME} (368 piles)\")\n", "print(f\"  Test site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)\")\n", "print(f\"  Detected: {detected_count} piles\")\n", "print(f\"  Missed: {missed_count} piles\")\n", "print(f\"  Generalization rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  Average confidence: {rcps_avg_confidence:.3f}\")\n", "\n", "print(f\"\\nTrue model generalization test complete!\")\n", "print(f\"\\nSUMMARY:\")\n", "print(f\"  Trained once on {RES_SITE_NAME}\")\n", "print(f\"  Tested on {RCPS_SITE_NAME} without retraining\")\n", "print(f\"  Detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"  This is TRUE model generalization!\")\n", "print(f\"  QGIS visualization ready: {csv_file}\")"]}, {"cell_type": "code", "execution_count": 12, "id": "false_positive_test", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "FALSE POSITIVE RATE TEST ON RCPS\n", "============================================================\n", "Testing RES-trained model on RCPS random non-pile locations\n", "This tests cross-site discrimination ability\n", "Generated 300 RCPS test locations (>2.0m from known piles)\n", "Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives\n", "Extracting patches (radius=1.5m, min_points=3, target_size=64)\n", "Extracted 175 valid patches\n", "Original sizes: min=3, max=15811, mean=3212.3\n", "Final sizes: min=3, max=64, mean=62.8\n", "\n", "📊 RCPS FALSE POSITIVE ANALYSIS:\n", "  RCPS test negative locations: 300\n", "  Valid negative patches: 175\n", "  False positives: 175.0 / 175\n", "  False positive rate: 100.0%\n", "  Avg confidence on negatives: 1.000\n", "  ❌ POOR - Too many false positives across sites\n", "\n", "🎯 COMPLETE GENERALIZATION VALIDATION:\n", "  Training site: nortan_res\n", "  Test site: althea_rcps\n", "  Pile detection rate: 100.0%\n", "  False positive rate: 100.0%\n", "  Cross-site performance: ❌ POOR - Too many false positives across sites\n", "\n", "🏆 FINAL ASSESSMENT: ⚠️ GENERALIZATION ISSUES - Needs investigation\n", "\n", "📍 QGIS Negative Test CSV: output_runs/true_generalization/rcps_negative_test_results_20250807_214117.csv\n", "   Use this to visualize false positive locations\n", "   False positives: 175.0 red points\n", "   True negatives: 0.0 green points\n"]}], "source": ["# FALSE POSITIVE TEST ON RCPS - Critical for generalization validation\n", "if len(rcps_patches) > 0 and 'loaded_model' in locals():\n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\"FALSE POSITIVE RATE TEST ON RCPS\")\n", "    print(\"=\" * 60)\n", "    print(\"Testing RES-trained model on RCPS random non-pile locations\")\n", "    print(\"This tests cross-site discrimination ability\")\n", "    \n", "    # Generate random test locations in RCPS (away from known piles)\n", "    np.random.seed(456)  # Different seed for RCPS test locations\n", "    n_test_negatives = min(300, len(rcps_patches))  # Test more for larger dataset\n", "    \n", "    # Sample random locations with buffer from known piles\n", "    buffer_distance = 2.0  # 2m buffer from known piles (very reduced for dense RCPS)\n", "    max_attempts = 3000  # Increased attempts for larger site\n", "    test_negative_locations = []\n", "    \n", "    rcps_x_min, rcps_x_max = rcps_points[:, 0].min(), rcps_points[:, 0].max()\n", "    rcps_y_min, rcps_y_max = rcps_points[:, 1].min(), rcps_points[:, 1].max()\n", "    \n", "    for attempt in range(max_attempts):\n", "        if len(test_negative_locations) >= n_test_negatives:\n", "            break\n", "            \n", "        # Random location within RCPS point cloud bounds (reduced margins)\n", "        test_x = np.random.uniform(rcps_x_min + 50, rcps_x_max - 50)\n", "        test_y = np.random.uniform(rcps_y_min + 50, rcps_y_max - 50)\n", "        test_loc = np.array([test_x, test_y])\n", "        \n", "        # Check distance from all known RCPS piles\n", "        distances = np.sqrt(np.sum((rcps_pile_locations[:, :2] - test_loc)**2, axis=1))\n", "        min_distance = np.min(distances)\n", "        \n", "        # Only use if far enough from known piles\n", "        if min_distance > buffer_distance:\n", "            test_negative_locations.append(test_loc)\n", "    \n", "    test_negative_locations = np.array(test_negative_locations)\n", "    print(f\"Generated {len(test_negative_locations)} RCPS test locations (>{buffer_distance}m from known piles)\")\n", "    print(f\"Extracting patches (radius=1.5m, min_points=3, target_size=64) - very relaxed for negatives\")\n", "    \n", "    if len(test_negative_locations) > 0:\n", "        # Extract patches from RCPS test negative locations (very relaxed parameters)\n", "        test_neg_patches, _ = extract_patches(rcps_points, test_negative_locations, 1.5, 3)  # Very small radius, minimal points\n", "        \n", "        if len(test_neg_patches) > 0:\n", "            # Subsample test negative patches\n", "            test_neg_patches = [subsample_patch(patch, TARGET_PATCH_SIZE) for patch in test_neg_patches]\n", "            \n", "            # Extract features and predict using RES-trained model\n", "            X_test_neg_rcps = extract_features(test_neg_patches)\n", "            y_pred_test_neg = loaded_model.predict(X_test_neg_rcps)\n", "            y_prob_test_neg = loaded_model.predict_proba(X_test_neg_rcps)[:, 1]\n", "            \n", "            false_positive_rate_rcps = np.mean(y_pred_test_neg)\n", "            avg_confidence_fp_rcps = np.mean(y_prob_test_neg)\n", "            \n", "            print(f\"\\n📊 RCPS FALSE POSITIVE ANALYSIS:\")\n", "            print(f\"  RCPS test negative locations: {len(test_negative_locations)}\")\n", "            print(f\"  Valid negative patches: {len(test_neg_patches)}\")\n", "            print(f\"  False positives: {np.sum(y_pred_test_neg)} / {len(y_pred_test_neg)}\")\n", "            print(f\"  False positive rate: {false_positive_rate_rcps*100:.1f}%\")\n", "            print(f\"  Avg confidence on negatives: {avg_confidence_fp_rcps:.3f}\")\n", "            \n", "            # Interpretation\n", "            if false_positive_rate_rcps <= 0.1:  # ≤10% false positives\n", "                fp_status_rcps = \"✅ EXCELLENT - Low false positives across sites\"\n", "            elif false_positive_rate_rcps <= 0.2:  # ≤20% false positives\n", "                fp_status_rcps = \"✅ GOOD - Acceptable false positives across sites\"\n", "            elif false_positive_rate_rcps <= 0.4:  # ≤40% false positives\n", "                fp_status_rcps = \"⚠️ MODERATE - Some false positives across sites\"\n", "            else:\n", "                fp_status_rcps = \"❌ POOR - Too many false positives across sites\"\n", "            \n", "            print(f\"  {fp_status_rcps}\")\n", "            \n", "            print(f\"\\n🎯 COMPLETE GENERALIZATION VALIDATION:\")\n", "            print(f\"  Training site: {RES_SITE_NAME}\")\n", "            print(f\"  Test site: {RCPS_SITE_NAME}\")\n", "            print(f\"  Pile detection rate: {rcps_detection_rate*100:.1f}%\")\n", "            print(f\"  False positive rate: {false_positive_rate_rcps*100:.1f}%\")\n", "            print(f\"  Cross-site performance: {fp_status_rcps}\")\n", "            \n", "            # Overall assessment\n", "            if rcps_detection_rate >= 0.9 and false_positive_rate_rcps <= 0.2:\n", "                overall_status = \"🎉 EXCELLENT GENERALIZATION - Ready for deployment\"\n", "            elif rcps_detection_rate >= 0.8 and false_positive_rate_rcps <= 0.3:\n", "                overall_status = \"✅ GOOD GENERALIZATION - Minor tuning may help\"\n", "            else:\n", "                overall_status = \"⚠️ GENERALIZATION ISSUES - Needs investigation\"\n", "            \n", "            print(f\"\\n🏆 FINAL ASSESSMENT: {overall_status}\")\n", "            \n", "            # Add to CSV for QGIS (negative test points)\n", "            if len(test_negative_locations) > 0:\n", "                # Create negative test results DataFrame\n", "                # Fix array length mismatch: use only valid patches\n", "                neg_test_df = pd.DataFrame({\n", "                    'pile_id': [f'neg_test_{i}' for i in range(len(test_neg_patches))],\n", "                    'utm_x': test_negative_locations[:len(test_neg_patches), 0],\n", "                    'utm_y': test_negative_locations[:len(test_neg_patches), 1],\n", "                    'predicted_pile': y_pred_test_neg,\n", "                    'confidence': y_prob_test_neg,\n", "                    'training_site': RES_SITE_NAME,\n", "                    'test_site': RCPS_SITE_NAME,\n", "                    'detection_status': ['False_Positive' if pred == 1 else 'True_Negative' for pred in y_pred_test_neg],\n", "                    'generalization_type': 'negative_test'\n", "                })\n", "                \n", "                # Convert to geographic coordinates (only valid patches)\n", "                geometry_neg = [Point(xy) for xy in zip(test_negative_locations[:len(test_neg_patches), 0], test_negative_locations[:len(test_neg_patches), 1])]\n", "                gdf_neg = gpd.GeoDataFrame(neg_test_df, geometry=geometry_neg, crs='EPSG:32615')  # UTM Zone 15N\n", "                gdf_neg_wgs84 = gdf_neg.to_crs('EPSG:4326')\n", "                \n", "                neg_test_df['longitude'] = gdf_neg_wgs84.geometry.x\n", "                neg_test_df['latitude'] = gdf_neg_wgs84.geometry.y\n", "                \n", "                # Save negative test results CSV\n", "                neg_csv_file = output_dir / f\"rcps_negative_test_results_{timestamp}.csv\"\n", "                neg_test_df.to_csv(neg_csv_file, index=False)\n", "                \n", "                print(f\"\\n📍 QGIS Negative Test CSV: {neg_csv_file}\")\n", "                print(f\"   Use this to visualize false positive locations\")\n", "                print(f\"   False positives: {np.sum(y_pred_test_neg)} red points\")\n", "                print(f\"   True negatives: {len(y_pred_test_neg) - np.sum(y_pred_test_neg)} green points\")\n", "            \n", "        else:\n", "            print(f\"⚠️ Could not extract valid patches from RCPS test negative locations\")\n", "    else:\n", "        print(f\"⚠️ Could not generate sufficient RCPS test negative locations\")\n", "        \n", "else:\n", "    print(\"No model available for RCPS false positive testing\")"]}, {"cell_type": "code", "execution_count": 16, "id": "f18deb8b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["RES points: 35,565,352\n", "RES pile locations: 368\n", "RES positive patches: 368\n", "Creating negative samples within point cloud coverage...\n", "RES negative patches: 357\n", "Note: Negatives may include pile-adjacent areas due to data filtering\n", "Training data: 725 samples\n", "Positive: 368.0, Negative: 357.0\n", "RES detection rate: 100.0%\n", "Model saved: output_runs/true_generalization/res_trained_model_20250807_221320.pkl\n", "RCPS points: 52,862,386\n", "RCPS pile locations: 1359\n", "RCPS patches: 1359\n", "RCPS detection rate: 100.0%\n", "Performance difference: +0.0 percentage points\n", "CROSS-SITE GENERALIZATION RESULTS:\n", "Training site: nortan_res (368 piles)\n", "Testing site: althea_rcps (1359 piles)\n", "Cross-site detection rate: 100.0%\n", "Performance difference: +0.0 percentage points\n", "Overall status: EXCELLENT CROSS-SITE GENERALIZATION\n", "Note: Point cloud data pre-filtered to pile buffer regions\n", "Results saved: output_runs/true_generalization/true_generalization_results_20250807_221320.json\n", "QGIS CSV exported: output_runs/true_generalization/rcps_generalization_results_20250807_221320.csv\n", "Cross-site generalization test complete\n"]}], "source": ["# %% [markdown]\n", "# # True Model Generalization Test\n", "# \n", "# Train model on RES data, test on RCPS data without retraining.\n", "# \n", "# **Author**: <PERSON><PERSON><PERSON>  \n", "# **Date**: August 2025\n", "\n", "# %% [markdown]\n", "# ## Configuration\n", "\n", "# %%\n", "# Parameters\n", "RES_SITE_NAME = \"nortan_res\"\n", "RCPS_SITE_NAME = \"althea_rcps\"\n", "\n", "# Data paths\n", "RES_POINT_CLOUD_PATH = \"../../../../../data/raw/nortan_res/pointcloud/Block_11_2m.las\"\n", "RES_BUFFER_KML_PATH = \"../../../../../data/raw/nortan_res/kml/Buffer_2m.kml\"\n", "\n", "RCPS_POINT_CLOUD_PATH = \"../../../../../data/raw/althea_rpcs/pointcloud/Point_Cloud.las\"\n", "RCPS_BUFFER_KML_PATH = \"../../../../../data/raw/althea_rpcs/kml/Buffer_2m.kml\"\n", "\n", "OUTPUT_DIR = \"output_runs/true_generalization\"\n", "\n", "# Patch parameters\n", "PATCH_RADIUS = 3.0\n", "MIN_POINTS = 20\n", "TARGET_PATCH_SIZE = 64\n", "\n", "# %% [markdown]\n", "# ## Imports\n", "\n", "# %%\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import joblib\n", "from pathlib import Path\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "import laspy\n", "import geopandas as gpd\n", "from scipy.spatial import cKDTree\n", "from sklearn.ensemble import GradientBoostingClassifier\n", "from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score\n", "import matplotlib.pyplot as plt\n", "\n", "# Create output directory\n", "output_dir = Path(OUTPUT_DIR)\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "\n", "# %% [markdown]\n", "# ## Utility Functions\n", "\n", "# %%\n", "def subsample_patch(patch_points, target_size=TARGET_PATCH_SIZE):\n", "    \"\"\"Subsample patch to target size\"\"\"\n", "    if len(patch_points) <= target_size:\n", "        return patch_points\n", "    \n", "    np.random.seed(42)\n", "    indices = np.random.choice(len(patch_points), target_size, replace=False)\n", "    return patch_points[indices]\n", "\n", "def extract_patches(points, locations, radius=PATCH_RADIUS, min_points=MIN_POINTS):\n", "    \"\"\"Extract patches around locations\"\"\"\n", "    tree = cKDTree(points[:, :2])\n", "    patches = []\n", "    valid_locs = []\n", "    \n", "    for i, loc in enumerate(locations):\n", "        indices = tree.query_ball_point(loc[:2], radius)\n", "        \n", "        if len(indices) >= min_points:\n", "            patch_points = points[indices]\n", "            patch_points = subsample_patch(patch_points, TARGET_PATCH_SIZE)\n", "            \n", "            center = np.array([loc[0], loc[1], np.mean(patch_points[:, 2])])\n", "            centered_patch = patch_points - center\n", "            \n", "            patches.append(centered_patch)\n", "            valid_locs.append(loc)\n", "    \n", "    return patches, np.array(valid_locs)\n", "\n", "def extract_features(patches):\n", "    \"\"\"Extract 22 features from patches\"\"\"\n", "    features = []\n", "    \n", "    for patch in patches:\n", "        x, y, z = patch[:, 0], patch[:, 1], patch[:, 2]\n", "        radial_dist = np.sqrt(x**2 + y**2)\n", "        height_above_min = z - np.min(z)\n", "        \n", "        feature_vector = [\n", "            # Basic spatial statistics (9 features)\n", "            np.mean(x), np.std(x), np.max(x) - np.min(x),\n", "            np.mean(y), np.std(y), np.max(y) - np.min(y),\n", "            np.mean(z), np.std(z), np.max(z) - np.min(z),\n", "            \n", "            # Height-based features (4 features)\n", "            np.mean(height_above_min), np.std(height_above_min),\n", "            np.percentile(height_above_min, 75), np.percentile(height_above_min, 25),\n", "            \n", "            # Radial distance features (4 features)\n", "            np.mean(radial_dist), np.std(radial_dist),\n", "            np.min(radial_dist), np.max(radial_dist),\n", "            \n", "            # Shape and density features (5 features)\n", "            len(patch),\n", "            np.std(x) / (np.std(y) + 1e-6),\n", "            np.std(z) / (np.std(x) + np.std(y) + 1e-6),\n", "            np.percentile(radial_dist, 90),\n", "            np.sum(height_above_min > np.mean(height_above_min)) / len(patch),\n", "        ]\n", "        features.append(feature_vector)\n", "    \n", "    return np.array(features)\n", "\n", "def load_and_reproject_kml(kml_path):\n", "    \"\"\"Load KML and reproject to UTM\"\"\"\n", "    gdf = gpd.read_file(kml_path)\n", "    \n", "    pile_coords = []\n", "    for geom in gdf.geometry:\n", "        if geom.geom_type == 'Point':\n", "            pile_coords.append([geom.x, geom.y])\n", "        elif geom.geom_type == 'Polygon':\n", "            centroid = geom.centroid\n", "            pile_coords.append([centroid.x, centroid.y])\n", "    \n", "    pile_locations = np.array(pile_coords)\n", "    \n", "    gdf_geo = gpd.GeoDataFrame(\n", "        geometry=gpd.points_from_xy(pile_locations[:, 0], pile_locations[:, 1]),\n", "        crs='EPSG:4326'\n", "    )\n", "    \n", "    # Reproject to appropriate UTM zone\n", "    if 'nortan' in str(kml_path):\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32614')  # UTM Zone 14N\n", "    else:\n", "        gdf_utm = gdf_geo.to_crs('EPSG:32615')  # UTM Zone 15N\n", "    \n", "    pile_locations_utm = np.array([[geom.x, geom.y] for geom in gdf_utm.geometry])\n", "    return pile_locations_utm\n", "\n", "# %% [markdown]\n", "# ## Step 1: Train Model on RES Data\n", "\n", "# %%\n", "# Load RES point cloud\n", "res_las = laspy.read(RES_POINT_CLOUD_PATH)\n", "res_points = np.vstack([res_las.x, res_las.y, res_las.z]).T\n", "print(f\"RES points: {len(res_points):,}\")\n", "\n", "# Load RES pile locations\n", "res_pile_locations = load_and_reproject_kml(RES_BUFFER_KML_PATH)\n", "print(f\"RES pile locations: {len(res_pile_locations)}\")\n", "\n", "# Extract positive patches\n", "res_pos_patches, _ = extract_patches(res_points, res_pile_locations)\n", "print(f\"RES positive patches: {len(res_pos_patches)}\")\n", "\n", "# %%\n", "# Create negative samples using random sampling within point cloud bounds\n", "print(\"Creating negative samples within point cloud coverage...\")\n", "\n", "np.random.seed(42)\n", "x_min, x_max = res_points[:, 0].min(), res_points[:, 0].max()\n", "y_min, y_max = res_points[:, 1].min(), res_points[:, 1].max()\n", "\n", "n_negative = len(res_pos_patches)\n", "random_x = np.random.uniform(x_min + 50, x_max - 50, n_negative)\n", "random_y = np.random.uniform(y_min + 50, y_max - 50, n_negative)\n", "random_locations = np.column_stack([random_x, random_y])\n", "\n", "res_neg_patches, _ = extract_patches(res_points, random_locations)\n", "print(f\"RES negative patches: {len(res_neg_patches)}\")\n", "print(\"Note: Negatives may include pile-adjacent areas due to data filtering\")\n", "\n", "# %%\n", "# Prepare training data\n", "X_pos_res = extract_features(res_pos_patches)\n", "X_neg_res = extract_features(res_neg_patches)\n", "\n", "X_train_res = np.vstack([X_pos_res, X_neg_res])\n", "y_train_res = np.hstack([np.ones(len(X_pos_res)), np.zeros(len(X_neg_res))])\n", "\n", "print(f\"Training data: {len(X_train_res)} samples\")\n", "print(f\"Positive: {np.sum(y_train_res)}, Negative: {len(y_train_res) - np.sum(y_train_res)}\")\n", "\n", "# %%\n", "# Train model\n", "res_model = GradientBoostingClassifier(\n", "    n_estimators=100, max_depth=6, learning_rate=0.1, random_state=42\n", ")\n", "res_model.fit(X_train_res, y_train_res)\n", "\n", "# Sanity check on RES\n", "y_pred_res = res_model.predict(X_pos_res)\n", "y_prob_res = res_model.predict_proba(X_pos_res)[:, 1]\n", "res_detection_rate = np.mean(y_pred_res)\n", "\n", "print(f\"RES detection rate: {res_detection_rate*100:.1f}%\")\n", "\n", "# Save model\n", "model_file = output_dir / f\"res_trained_model_{timestamp}.pkl\"\n", "joblib.dump(res_model, model_file)\n", "print(f\"Model saved: {model_file}\")\n", "\n", "# %% [markdown]\n", "# ## Step 2: Test on RCPS Data (No Retraining)\n", "\n", "# %%\n", "# Load RCPS data\n", "rcps_las = laspy.read(RCPS_POINT_CLOUD_PATH)\n", "rcps_points = np.vstack([rcps_las.x, rcps_las.y, rcps_las.z]).T\n", "print(f\"RCPS points: {len(rcps_points):,}\")\n", "\n", "rcps_pile_locations = load_and_reproject_kml(RCPS_BUFFER_KML_PATH)\n", "print(f\"RCPS pile locations: {len(rcps_pile_locations)}\")\n", "\n", "# %%\n", "# Extract RCPS patches\n", "rcps_patches, _ = extract_patches(rcps_points, rcps_pile_locations)\n", "print(f\"RCPS patches: {len(rcps_patches)}\")\n", "\n", "# Extract features\n", "X_rcps = extract_features(rcps_patches)\n", "\n", "# Apply trained model (no retraining)\n", "y_pred_rcps = res_model.predict(X_rcps)\n", "y_prob_rcps = res_model.predict_proba(X_rcps)[:, 1]\n", "\n", "rcps_detection_rate = np.mean(y_pred_rcps)\n", "performance_diff = rcps_detection_rate - res_detection_rate\n", "\n", "print(f\"RCPS detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "\n", "\n", "# %% [markdown]\n", "# ## Results and Export\n", "\n", "# %%\n", "# Results summary\n", "print(\"CROSS-SITE GENERALIZATION RESULTS:\")\n", "print(f\"Training site: {RES_SITE_NAME} ({len(res_pile_locations)} piles)\")\n", "print(f\"Testing site: {RCPS_SITE_NAME} ({len(rcps_pile_locations)} piles)\")\n", "print(f\"Cross-site detection rate: {rcps_detection_rate*100:.1f}%\")\n", "print(f\"Performance difference: {performance_diff*100:+.1f} percentage points\")\n", "\n", "# Overall generalization assessment\n", "if rcps_detection_rate >= 0.9:\n", "    if abs(performance_diff) <= 0.1:\n", "        status = \"EXCELLENT CROSS-SITE GENERALIZATION\"\n", "    else:\n", "        status = \"GOOD CROSS-SITE GENERALIZATION\"\n", "elif rcps_detection_rate >= 0.7:\n", "    status = \"MODERATE CROSS-SITE GENERALIZATION\"\n", "else:\n", "    status = \"POOR CROSS-SITE GENERALIZATION\"\n", "\n", "print(f\"Overall status: {status}\")\n", "print(\"Note: Point cloud data pre-filtered to pile buffer regions\")\n", "\n", "# %%\n", "# Save results\n", "results = {\n", "    'experiment_info': {\n", "        'type': 'true_model_generalization',\n", "        'training_site': RES_SITE_NAME,\n", "        'testing_site': RCPS_SITE_NAME,\n", "        'timestamp': timestamp,\n", "        'model_file': str(model_file)\n", "    },\n", "    'performance_metrics': {\n", "        'res_detection_rate': float(res_detection_rate),\n", "        'rcps_detection_rate': float(rcps_detection_rate),\n", "        'performance_difference': float(performance_diff),\n", "        'generalization_status': status\n", "    },\n", "    'data_info': {\n", "        'res_pile_locations': len(res_pile_locations),\n", "        'rcps_pile_locations': len(rcps_pile_locations),\n", "        'patch_radius': PATCH_RADIUS,\n", "        'target_patch_size': TARGET_PATCH_SIZE,\n", "        'data_scope': 'pile_buffer_regions_only'\n", "    }\n", "}\n", "\n", "results_file = output_dir / f\"true_generalization_results_{timestamp}.json\"\n", "with open(results_file, 'w') as f:\n", "    json.dump(results, f, indent=2)\n", "\n", "print(f\"Results saved: {results_file}\")\n", "\n", "# %%\n", "# Export for QGIS visualization\n", "rcps_results_df = pd.DataFrame({\n", "    'pile_id': range(len(rcps_pile_locations)),\n", "    'utm_x': rcps_pile_locations[:, 0],\n", "    'utm_y': rcps_pile_locations[:, 1], \n", "    'predicted_pile': y_pred_rcps,\n", "    'confidence': y_prob_rcps,\n", "    'training_site': RES_SITE_NAME,\n", "    'test_site': RCPS_SITE_NAME,\n", "    'detection_status': ['Detected' if pred == 1 else 'Missed' for pred in y_pred_rcps],\n", "    'generalization_type': 'cross_site_pile_detection'\n", "})\n", "\n", "# Convert to geographic coordinates\n", "from shapely.geometry import Point\n", "geometry = [Point(xy) for xy in zip(rcps_pile_locations[:, 0], rcps_pile_locations[:, 1])]\n", "gdf = gpd.GeoDataFrame(rcps_results_df, geometry=geometry, crs='EPSG:32615')\n", "gdf_wgs84 = gdf.to_crs('EPSG:4326')\n", "\n", "rcps_results_df['longitude'] = gdf_wgs84.geometry.x\n", "rcps_results_df['latitude'] = gdf_wgs84.geometry.y\n", "\n", "csv_file = output_dir / f\"rcps_generalization_results_{timestamp}.csv\"\n", "rcps_results_df.to_csv(csv_file, index=False)\n", "\n", "print(f\"QGIS CSV exported: {csv_file}\")\n", "print(\"Cross-site generalization test complete\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}